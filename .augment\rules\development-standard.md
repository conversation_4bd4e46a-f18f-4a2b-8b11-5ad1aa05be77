---
type: "agent_requested"
description: "Comprehensive Development Standards"
---

# ChatLo Development Standards - ACTIVE RULES
**Generated from**: Code Quality Review Meeting - 2025-07-20
**Status**: ✅ APPROVED AND ACTIVE
**Approved by**: Owner - 2025-07-23

## Owner's Core Principles (Priority Hierarchy)
1. **User Experience** - Intuitive, responsive, non-intrusive design
2. **Code Simplicity** - Clean, maintainable, error-free code
3. **Modular Design** - Reusable components and services
4. **Plugin Mindset** - Always think beyond one simple flow, but a plugin design as a whole.  Every features should always refer to our plugin structure
5. **Natural Language Explaining** - Communicate with owner with natural language and explain the tech, programming, flow, components, module and variables that involved in detail.

## RULE CATEGORY 1: USER VALUE FIRST DEVELOPMENT

### Rule 1.1: User Value Validation
**Requirement**: Every feature must pass user value assessment before development
**Enforcement**: 
- [ ] User story with clear benefit statement required
- [ ] Feature must solve identified user problem
- [ ] No features without explicit user signal or request
**Example**: Phase 1 pinning enhancement - user explicitly pins → system provides intelligent organization

### Rule 1.2: Intelligence as our core
**Requirement**: No background processing without explicit user signals
**Enforcement**:
- [ ] All intelligence features triggered by user actions unless a sensible design in mind
- [ ] Autonomous data collection or processing if needed
- [ ] User controls when and how data is processed
**Example**: Intelligence extraction only happens when user pins message, not continuously.

### Rule 1.3: Local-First Architecture
**Requirement**: Minimize external dependencies and API calls
**Enforcement**:
- [ ] All processing happens locally when possible
- [ ] External calls must be justified and documented
- [ ] Offline functionality must be preserved
- [ ] Bundle size impact assessment for new dependencies

### Rule 1.4: Unified Path Resolution Standard
**PRINCIPLE**: All path operations MUST use centralized PathResolver utilities. No duplicate path resolution logic.

**PROHIBITED**:
- [ ] Multiple path resolution systems (`isVirtualPath`, custom path parsing)
- [ ] Hardcoded user directories or drive letters
- [ ] OS-specific manual path splitting
- [ ] Duplicate `extractContextPath` logic in multiple files
- [ ] Direct string manipulation of file paths

**REQUIRED**:
- [ ] All path operations through `PathResolver` class
- [ ] Single source of truth for vault context resolution
- [ ] Consistent path normalization across all components
- [ ] Vault boundary validation for all file operations

**PATH TYPES SUPPORTED**:
```typescript
// Real file paths
PathResolver.resolve('C:\\Users\\<USER>\\file.pdf')
→ { type: 'file', vault: 'vault-path', context: 'context-id', path: 'normalized-path' }

// Annotation paths
PathResolver.resolve('chat-notes/conversation-123.json')
→ { type: 'annotation', vault: 'current-vault', context: 'current-context',
    path: '.intelligence/context-notes/conversation-123.json' }

// Vault-relative paths
PathResolver.resolve('documents/file.pdf', { vault: 'vault-path', context: 'context-id' })
→ { type: 'file', vault: 'vault-path', context: 'context-id',
    path: 'vault-path/context-id/documents/file.pdf' }
```

**IMPLEMENTATION REQUIREMENTS**:
- [ ] PathResolver handles ALL path types (files, annotations, vault-relative)
- [ ] VaultContextService maintains current context state
- [ ] All components use same path resolution mechanism
- [ ] Context flows consistently from UI → services → storage
- [ ] Remove all `isVirtualPath` legacy code

## RULE CATEGORY 2: CODE TIDINESS STANDARDS

### Rule 2.1: Variable Naming Convention
**Requirement**: Prevent duplicate variables through systematic naming
**Pattern**: `[context]_[purpose]_[type]` or `[component][Purpose][Type]`
**Examples**:
- ✅ `messagePin_state`, `vaultSuggestion_modal`, `intelligence_extraction_data`
- ❌ `state`, `modal`, `data` (too generic, causes duplicates)
**Enforcement**:
- [ ] ESLint rule for variable naming patterns
- [ ] Pre-commit hook validation
- [ ] Code review checklist item

### Rule 2.2: FontAwesome Icon Management
**Requirement**: Centralized icon management to use local downloaded  icons.  Use single service to prevent massive errors.
**Implementation**:
- [ ] Single icon registry file: `src/components/Icons/index.ts`
- [ ] All icons imported from registry only
- [ ] No direct FontAwesome imports in components, ensure no embedded/CDN from the internet
- [ ] Downloaded icons and must be offline accessible
- [ ] **Icon Storage Location**: All downloaded icons must be placed in `\src\components\Icons` folder
- [ ] **Offline Accessibility**: All icons must be offline accessible through the local folder `\src\components\Icons`
- [ ] **Development Icons Source**: Discover development icons from `\src\components\Icons\fontawesome-free-7.0.0-web` folder or `\src\components\Icons\fontawesome-free-7.0.0-desktop`
- [ ] **Only Icons in Use**: Only icons that are actively used in the application should be stored locally, through the local folder `\src\components\Icons`  I will check out when packaging.
**Enforcement**:
Use ESlint to block all connections to FontAwesome CDN.
Remove ESlint when deployment.


### Rule 2.3: Interface Consistency
**Requirement**: Prevent duplicate type definitions
**Implementation**:
- [ ] Single source of truth for all interfaces
- [ ] Global types in `src/types/index.ts`
- [ ] Preload types must match global types
- [ ] Regular interface synchronization checks
**Enforcement**:
- [ ] TypeScript strict mode enabled
- [ ] Automated type consistency validation
- [ ] Pre-commit TypeScript check

## RULE CATEGORY 3: ERROR PREVENTION & BUG LOGGING

### Rule 3.1: Mandatory Bug Logging
**Requirement**: All bugs must be logged with root cause analysis
**Template**:
```markdown
# Bug Report - [Date]
**Issue**: [Description]
**Root Cause**: [Why it happened]
**Prevention**: [How to avoid in future]
**Pattern**: [Similar issues to watch for]
```
**Enforcement**:
- [ ] Bug log entry required for all fixes
- [ ] Root cause analysis mandatory
- [ ] Pattern recognition documentation
- [ ] Monthly bug pattern review

### Rule 3.2: TypeScript Error Zero Tolerance
**Requirement**: No TypeScript errors allowed in codebase
**Implementation**:
- [ ] `npx tsc --noEmit` must return 0 errors
- [ ] Pre-commit hook blocks commits with TS errors
- [ ] End-of-session QA Engineer clearance required
- [ ] Automated error count tracking
**Enforcement**:
- [ ] CI/CD pipeline TypeScript check
- [ ] Development workflow integration
- [ ] Quality gate enforcement

### Rule 3.3: Mistake Pattern Recognition
**Requirement**: Learn from recurring issues to prevent repetition
**Common Patterns Identified**:
- Duplicate variable names
- Missing type definitions
- FontAwesome import inconsistencies
- External dependency bloat
- Repeat pattern of cache version not being detected, make regular purge of cache
- Everytime the code required electron rebuild, but no actions for me.
**Prevention**:
- [ ] Pattern-based linting rules
- [ ] Automated pattern detection
- [ ] Developer education on common mistakes

## RULE CATEGORY 4: DEVELOPMENT WORKFLOW

### Rule 4.1: Pre-Development Checklist
**Before starting any feature**:
- [ ] User value clearly defined
- [ ] UX design approved
- [ ] Technical approach simplified
- [ ] Component reusability considered
- [ ] External dependencies justified
- [ ] Performance impact assessed

### Rule 4.2: Code Review Standards
**Every code change must**:
- [ ] Pass TypeScript compilation
- [ ] Follow naming conventions
- [ ] Include proper error handling
- [ ] Maintain local-first principles
- [ ] Document any external calls
- [ ] Include user value justification

### Rule 4.3: Quality Gates
**Development cannot proceed without**:
- [ ] Zero TypeScript errors
- [ ] User value validation
- [ ] Performance budget compliance
- [ ] Icon import compliance
- [ ] Variable naming compliance
- [ ] Bug log updates (if fixing issues)
- [ ] Zero unused variables or imports
- [ ] Legacy code cleanup completed
- [ ] All sunset variables removed
- [ ] Data validation and default values implemented
- [ ] No undefined properties in critical data structures

### Rule 4.5: Step-by-Step Mode
**Development based on the scope, by proceed with step-by-step**:
- [ ] Not modify the pre-made UI
- [ ] If no service, variable, data or any elements - Create first.
- [ ] Always analyze the code, what's missed to proceed and complete the task, if not, ask me.
- [ ] Activate this mode by calling /step-by-step-dev

## RULE CATEGORY 5: LEGACY CODE CLEANUP & RESIDUE PREVENTION

### Rule 5.1: Zero Legacy Residue Policy
**Requirement**: No legacy variables, unused imports, or dead code allowed after feature changes
**PROHIBITED**:
- [ ] Commented-out code blocks
- [ ] Unused variable declarations
- [ ] Dead import statements
- [ ] Legacy function signatures
- [ ] Sunset variables from previous implementations
- [ ] Unused interface definitions
- [ ] Orphaned type declarations

**REQUIRED**:
- [ ] Immediate cleanup after any feature modification
- [ ] Remove all unused code before committing
- [ ] Delete legacy variables when switching features
- [ ] Clean up imports after dependency changes
- [ ] Remove unused interfaces and types
- [ ] Clear all TypeScript errors before proceeding

**CLEANUP CHECKLIST** (MANDATORY BEFORE COMMIT):
```typescript
// ❌ FORBIDDEN - Legacy residue
const oldFeature_state = useState(); // Unused after feature switch
import { LegacyComponent } from './old'; // Unused import
interface OldInterface {} // Unused type

// ✅ REQUIRED - Clean implementation
const currentFeature_state = useState(); // Only active variables
import { CurrentComponent } from './current'; // Only used imports
interface CurrentInterface {} // Only used types
```

### Rule 5.2: Feature Switch Cleanup Protocol
**Requirement**: Systematic cleanup when switching between features or approaches
**PROTOCOL**:
1. **Before Feature Switch**:
   - [ ] Document current state and variables
   - [ ] Identify all dependencies to be removed
   - [ ] Plan cleanup sequence

2. **During Feature Switch**:
   - [ ] Remove old variables immediately
   - [ ] Delete unused imports
   - [ ] Clean up interface definitions
   - [ ] Remove dead code paths

3. **After Feature Switch**:
   - [ ] Verify zero TypeScript errors
   - [ ] Confirm no unused variables
   - [ ] Validate all imports are used
   - [ ] Check for orphaned types

**ENFORCEMENT**:
- [ ] Pre-commit hook blocks commits with unused variables
- [ ] TypeScript compilation must pass with 0 errors
- [ ] Code review checklist includes residue check
- [ ] Automated unused code detection in CI/CD

### Rule 5.3: Sunset Code Management
**Requirement**: Proper handling of deprecated or replaced functionality
**APPROACHES**:
1. **Immediate Removal** (Preferred):
   - [ ] Delete deprecated code immediately
   - [ ] Update all references
   - [ ] Clean up related imports
   - [ ] Remove unused dependencies

2. **Gradual Migration** (If complex):
   - [ ] Mark deprecated code with `@deprecated`
   - [ ] Set removal deadline (max 1 sprint)
   - [ ] Create migration plan
   - [ ] Monitor usage patterns

**FORBIDDEN**:
- [ ] Keeping deprecated code "just in case"
- [ ] Commenting out large code blocks
- [ ] Leaving unused variables for future use
- [ ] Maintaining legacy interfaces

### Rule 5.4: Import Hygiene Standards
**Requirement**: Keep imports clean and minimal
**ENFORCEMENT**:
- [ ] Remove unused imports immediately
- [ ] No commented-out import statements
- [ ] Group imports logically (external, internal, relative)
- [ ] Use specific imports, avoid wildcards
- [ ] Regular import cleanup (weekly)

**IMPORT CLEANUP TOOLS**:
- [ ] ESLint `no-unused-vars` rule enabled
- [ ] TypeScript unused import detection
- [ ] Pre-commit import validation
- [ ] Automated import sorting

### Rule 5.5: Variable Lifecycle Management
**Requirement**: Track and manage variable usage throughout development
**LIFECYCLE TRACKING**:
1. **Creation**: Document purpose and scope
2. **Usage**: Monitor active usage patterns
3. **Modification**: Update documentation
4. **Removal**: Clean up when no longer needed

**PREVENTION MEASURES**:
- [ ] Variable purpose documentation
- [ ] Usage pattern monitoring
- [ ] Regular unused variable audits
- [ ] Automated cleanup suggestions

**CLEANUP TRIGGERS**:
- [ ] Feature completion
- [ ] Code refactoring
- [ ] Bug fixes
- [ ] Performance optimizations
- [ ] Dependency updates

### Rule 5.6: Data Validation & Default Values
**Requirement**: Prevent undefined/null properties from causing runtime errors
**PROHIBITED**:
- [ ] Passing undefined values through data pipelines
- [ ] Missing fallback values for required properties
- [ ] No validation of external data sources
- [ ] Silent failures when data is missing

**REQUIRED**:
- [ ] Default value assignment for all required properties
- [ ] Data validation at entry points (API, file loading, etc.)
- [ ] Fallback mechanisms for missing data
- [ ] Logging when defaults are applied
- [ ] Type safety enforcement for critical properties

**IMPLEMENTATION PATTERN**:
```typescript
// ❌ FORBIDDEN - No validation or defaults
const color = data.color // Could be undefined

// ✅ REQUIRED - Always provide fallbacks
const color = data.color || getDefaultColor(data.name)
const validatedData = {
  ...data,
  color: data.color || getDefaultColor(data.name),
  icon: data.icon || 'fa-folder'
}

// ✅ REQUIRED - Validate at entry points
if (registry.vaults && Array.isArray(registry.vaults)) {
  registry.vaults = registry.vaults.map(vault => ({
    ...vault,
    color: vault.color || this.getDefaultVaultColor(vault.name)
  }))
}
```

**COMMON PATTERNS TO PREVENT**:
- [ ] Undefined color properties in UI components
- [ ] Missing icon fallbacks in data structures
- [ ] No validation of JSON file contents
- [ ] Silent propagation of undefined values

## ENFORCEMENT MECHANISMS

### Automated Tools
- **ESLint**: Variable naming, import patterns
- **TypeScript**: Strict compilation checking
- **Bundle Analyzer**: Dependency and size monitoring
- **Pre-commit Hooks**: Automated quality checks
- **Import Linter**: FontAwesome centralization
- **Unused Code Detector**: Legacy residue identification
- **Import Cleanup Tools**: Automated import management
- **Dead Code Scanner**: Unused variable detection

### Manual Processes
- **Code Reviews**: Human validation of standards
- **QA Engineer Clearance**: End-of-session error checking
- **Bug Log Reviews**: Pattern recognition and prevention
- **User Value Assessment**: Feature justification
- **Legacy Code Audits**: Monthly unused code reviews
- **Import Cleanup Sessions**: Weekly import hygiene checks

### Quality Gates
**Development cannot proceed without**:
- [ ] Zero TypeScript errors
- [ ] User value validation
- [ ] Performance budget compliance
- [ ] Icon import compliance
- [ ] Variable naming compliance
- [ ] Bug log updates (if fixing issues)
- [ ] Zero unused variables or imports
- [ ] Legacy code cleanup completed
- [ ] All sunset variables removed
- [ ] Data validation and default values implemented
- [ ] No undefined properties in critical data structures

### Quality Metrics
- **TypeScript Error Count**: Must be 0
- **Bundle Size**: Monitor for bloat
- **External Dependencies**: Minimize and justify
- **Bug Pattern Frequency**: Track and reduce
- **User Value Score**: Measure feature utility
- **Legacy Code Count**: Track unused variables and imports
- **Cleanup Efficiency**: Measure time from feature switch to cleanup
- **Residue Prevention**: Count of new legacy code instances
- **Import Hygiene Score**: Percentage of clean imports maintained

## IMPLEMENTATION TIMELINE

### Phase 1: Immediate (Week 1)
- [ ] Implement TypeScript zero tolerance
- [ ] Create centralized icon registry
- [ ] Establish bug logging template
- [ ] Add pre-commit TypeScript check
- [ ] Implement legacy code cleanup rules
- [ ] Set up unused code detection

### Phase 2: Automation (Week 2)
- [ ] Configure ESLint rules
- [ ] Set up bundle analyzer
- [ ] Create import linting
- [ ] Implement quality gates
- [ ] Configure legacy code scanners
- [ ] Set up import cleanup automation

### Phase 3: Integration (Week 3)
- [ ] Full workflow integration
- [ ] Developer training
- [ ] Quality metric tracking
- [ ] Continuous improvement process
- [ ] Legacy code audit system
- [ ] Import hygiene monitoring

### Phase 4: Legacy Code Cleanup (Week 4)
- [ ] Initial codebase legacy audit
- [ ] Remove all existing unused variables
- [ ] Clean up orphaned imports
- [ ] Delete deprecated interfaces
- [ ] Establish cleanup baseline
- [ ] Monitor for new residue creation

## SUCCESS CRITERIA

### Code Quality
- Zero TypeScript errors maintained
- No duplicate variable issues
- Consistent FontAwesome usage
- Minimal external dependencies
- Zero legacy code residue
- Clean import statements
- No unused variables or interfaces

### User Value
- All features serve clear user needs
- Just-in-time intelligence pattern followed
- Local-first architecture maintained
- Non-intrusive user experience

### Development Efficiency
- Reduced debugging time
- Faster development cycles
- Fewer recurring issues
- Improved code maintainability
- Faster feature switching
- Reduced technical debt
- Cleaner codebase maintenance

---

**APPROVED AND ACTIVE**: These rules are now mandatory for all ChatLo development. They address all identified issues from Phase 1 and establish systematic prevention of recurring problems while maintaining ChatLo's core principles of user value, local-first architecture, and code quality.

**Implementation Status**: Phase 1 (Immediate) - IN PROGRESS
**New Rules Added**: Legacy Code Cleanup & Residue Prevention (Rule Category 5)
**Updated**: 2025-01-27 - Added comprehensive legacy code cleanup standards
