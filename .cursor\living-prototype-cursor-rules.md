# Living Prototype Cursor Rules - Agent-Made Backend Testing

## 🎯 **Quick Reference (Keep This Open During Testing)**

### **Core Commands**
- **`/init`** - Start new testing session
- **`/test-stage [1-5]`** - Execute specific test stage
- **`/test-item [item]`** - Test specific intelligence component

### **Current Focus: TIER 2 (Signal Enhancement)**
- **Goal**: Test noise filtering and signal synthesis
- **Challenge**: Signal-to-noise ratio management
- **Focus**: Multi-source aggregation + user feedback integration

### **📚 Document References for Deep Dive**
- **Architecture Details**: `plan/master-mode-plan1.md` (Lines 1-100)
- **System Design**: `docs/SYSTEM_DESIGN_ARCHITECTURE_V03.md`
- **API Reference**: `docs/API_REFERENCE.md`
- **Intelligence Schema**: `docs/intelligence-storage-schema.md`
- **Session Recording**: `plan/living-prototype.md` (Current session)
- **Core Services**: `plan/core_service_plan_001.md`

---

## 🧠 **Memory & Prompt Framework**
*📚 See `docs/intelligence-storage-schema.md` for detailed memory schemas*

### **Short-Term Memory (Session)**
```typescript
interface SessionMemory {
  current_context: { user_input: string; processing_stage: string; confidence_thresholds: number[] };
  recent_decisions: Array<{ decision: string; reasoning: string; confidence: number }>;
  user_feedback: Array<{ feedback_type: 'positive'|'negative'|'neutral'; content: string; impact_score: number }>;
}
```

### **Long-Term Memory (Vault)**
```typescript
interface VaultMemory {
  user_preferences: { confidence_thresholds: Record<string, number>; priority_weights: Record<string, number> };
  learned_patterns: Array<{ pattern_type: string; success_rate: number; confidence_correlation: number }>;
  quality_metrics: { average_confidence: number; user_satisfaction: number; noise_reduction_rate: number };
}
```

---

## 📋 **Testing Stages & Desired Results**
*📚 See `plan/file-intel-process-story.md` for detailed processing pipeline*

### **Stage 1: Data Input & Collection**
**Goal**: Validate input capture and processing
**Desired Results**:
- Generate specific prompts for different input types
- Define memory schemas (short-term + long-term)
- Extract 5+ metadata fields with confidence >0.8
- Classify user intent into 5 types with 90% accuracy
- Generate relevance scores (0-100) with clear reasoning

### **Stage 2: Intelligence Extraction**
**Goal**: Test core extraction pipeline
**Desired Results**:
- Create 3+ specialized entity extraction prompts
- Generate 5 topic classification prompts with confidence thresholds
- Define human connection detection algorithms
- Implement 0-100 relevance scoring system
- Create confidence scoring that correlates with actual accuracy

### **Stage 3: Noise Filtering & Signal Enhancement**
**Goal**: Validate signal-to-noise ratio management
**Desired Results**:
- Generate prompts for 5 priority levels with clear criteria
- Define confidence threshold filtering rules
- Create user feedback collection prompts
- Generate cross-validation prompts
- Define noise reduction algorithms with measurable metrics

### **Stage 4: Intelligence Synthesis**
**Goal**: Test multi-source combination and synthesis
**Desired Results**:
- Create aggregation prompts for multiple sources
- Define conflict resolution rules with confidence scoring
- Generate weighted synthesis prompts with clear weighting
- Create context integration prompts
- Define output formatting rules with validation criteria

### **Stage 5: Process Validation**
**Goal**: Validate complete end-to-end process
**Desired Results**:
- Generate pipeline execution prompts
- Define error handling logic with recovery prompts
- Create performance benchmarks with timing thresholds
- Generate user experience guidance prompts
- Define quality assessment rules with validation prompts

---

## 🚀 **Response Protocol**
*📚 See `plan/living-prototype.md` for detailed session templates*

### **When `/init` is triggered:**
1. Analyze context and vault state
2. Identify testing needs
3. Request specific information
4. Set expectations
5. Record setup in living-prototype.md

### **When `/test-stage` is triggered:**
1. Execute specified stage logic
2. Generate appropriate prompts and processing instructions
3. Provide clear dev app testing steps
4. Set validation criteria
5. Record results

### **When `/test-item` is triggered:**
1. Focus on specific item
2. Generate targeted logic
3. Provide validation criteria
4. Suggest improvements
5. Record item results

---

## 📊 **Quality Standards**
*📚 See `docs/feature-story/enhanced-file-page-overlay.md` for UI integration details*

### **Prompt Engineering Principles**
1. **Specificity**: Clear, measurable outcomes
2. **Memory Integration**: Reference and update memory structures
3. **Confidence Scoring**: Include confidence assessment
4. **User Feedback Loop**: Collect and integrate preferences
5. **Iterative Refinement**: Improve based on performance

### **Expected JSON Output Structure**
```json
{
  "file_metadata": { "path": "...", "type": "...", "hash": "..." },
  "intelligence": {
    "key_ideas": [{ "text": "...", "relevance_score": 85, "weight": 0.8 }],
    "weighted_entities": [{ "text": "...", "priority_level": "high", "weight": 1.0 }],
    "human_connections": [{ "name": "...", "email": "...", "company": "..." }]
  },
  "processing_metadata": { "confidence": 0.85, "model_used": "gemma3-32k" }
}
```

### **Priority Weight System**
```
Priority Level 1 (Weight: 1.0): User-confirmed labels + Human connections
Priority Level 2 (Weight: 0.8): Auto-selected high-relevance ideas
Priority Level 3 (Weight: 0.6): Technical concepts and knowledge
Priority Level 4 (Weight: 0.4): Supporting details and references
Priority Level 5 (Weight: 0.2): General metadata and structure
```

---

## 📝 **Documentation Requirements**

### **living-prototype.md Structure**
```markdown
## Session Overview
- **Vault**: [vault name]
- **Test Type**: [session type]
- **Goals**: [what we're testing]
- **Success Criteria**: [how we measure success]

## Test Execution
### Stage [X]: [Stage Name]
- **Input**: [what was provided]
- **Processing**: [what logic was applied]
- **Output**: [what was generated]
- **Dev App Results**: [JSON output analysis]
- **User Feedback**: [your commentary]
- **Refinements**: [what we learned/changed]
```

---

## 🎯 **Usage Guidelines**

1. **Always start with `/init`** for new testing sessions
2. **Use `/test-stage [1-5]`** to focus on specific aspects
3. **Use `/test-item [item]`** for granular validation
4. **Provide feedback** after each dev app test
5. **Document everything** in living-prototype.md
6. **Iterate and refine** based on actual results

---

## 📚 **Complete Document Reference Map**

### **🏗️ Architecture & Design**
- **Master Plan**: `plan/master-mode-plan1.md` - Complete architecture vision and roadmap
- **System Design**: `docs/SYSTEM_DESIGN_ARCHITECTURE_V03.md` - Technical architecture details
- **Core Services**: `plan/core_service_plan_001.md` - Service architecture and IPC design

### **🔧 Implementation & Services**
- **API Reference**: `docs/API_REFERENCE.md` - Complete API endpoint documentation
- **Intelligence Schema**: `docs/intelligence-storage-schema.md` - Data structures and storage
- **File Processing**: `plan/file-intel-process-story.md` - Processing pipeline details

### **🎨 UI & User Experience**
- **Enhanced Overlay**: `docs/feature-story/enhanced-file-page-overlay.md` - UI integration details
- **Page Reference**: `docs/PAGE_REFERENCE.md` - Component hierarchy and data flows

### **📝 Testing & Documentation**
- **Session Recording**: `plan/living-prototype.md` - Current testing session documentation
- **Quality Metrics**: `chat_intel_test_0.1/quality_metrics.md` - Testing quality standards

### **🚀 Quick Navigation Tips**
- **Need architecture details?** → `plan/master-mode-plan1.md`
- **Need API endpoints?** → `docs/API_REFERENCE.md`
- **Need data schemas?** → `docs/intelligence-storage-schema.md`
- **Need UI details?** → `docs/feature-story/enhanced-file-page-overlay.md`
- **Need processing logic?** → `plan/file-intel-process-story.md`

---

*This concise version focuses on what you need during testing sessions. Keep this open for quick reference, and use the document references above for deep dive explanations.*
