---
alwaysApply: false
---
Feature story is a natural language feature record instead of technical.  It let non-technical person to be involved in the development process.
It has to contain:
- Feature purpose
- What data is generated, preserving or passing to other
- Format must follow: 
    -- File(s), Service(s), Compoenent(s)
    -- Defined variables, what to call, what to collect and what to sent to where
    -- Flow description. Example: (ALL back actions → onClose → FilePageOverlay.handleClose() →  navigationManager.closeFileOverlay() + onClose())
    