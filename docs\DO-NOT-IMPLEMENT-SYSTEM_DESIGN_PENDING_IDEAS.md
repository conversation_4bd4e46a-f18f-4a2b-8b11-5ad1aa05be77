# ALL CONTENT IN THIS DOC IS NOT FOR CURRENT STAGE

Standalone on USB ChatLo


## Short answer

Yes. A USB‑portable ChatLo is feasible. We just need to ship a “Portable Mode” build per OS and route all data (DB + vault + .intelligence) to the USB path with safe connect/disconnect. The only hard constraint is local models: if you rely on Ollama/LM Studio on the host, offline “standalone AI” won’t be fully self‑contained unless we also bundle a model runtime and weights (large).

## What “standalone on USB” really means

There are three practical portability levels:

- Level 1: Data‑portable
  - App installed on host; vault and DB live on USB. Fully local/private; you can move the USB and reopen your work elsewhere.
- Level 2: App‑portable (per OS)
  - Electron app binaries live on the USB. You plug in and run the app directly from USB on that OS. All data (DB + vault) also on USB.
- Level 3: Cross‑OS on the same USB
  - USB contains separate builds for Windows/macOS/Linux in different folders. User launches the right one for the host OS. Data folder is shared (vault + DB).

All are realistic. Level 2/3 just require proper packaging and a few safeguards.

## Key constraints and caveats

- Local model runtime
  - Today we depend on Ollama or LM Studio on the host. For true offline “AI-in-a-stick,” we’d need to bundle a local runtime and model weights on the USB (hundreds of MB to many GB), plus per‑OS binaries. That’s doable but a different size/performance tradeoff.
- Platform packaging
  - Windows: portable Electron EXE is fine. Expect SmartScreen prompts; code signing helps.
  - macOS: Gatekeeper/quarantine. Notarized, signed app is recommended; users may need to move the app once or clear quarantine if running from removable media.
  - Linux: AppImage/AppDir works well from USB.
- Native modules
  - better‑sqlite3 is already bundled per platform in the packaged app. The app must be built for each target OS/arch. Don’t expect one binary to run everywhere.
- Performance and safe‑eject
  - USB can be slower; we must flush/close the DB before eject. Provide a “Prepare for Disconnect” action. No background scanning.

## What we need to implement

- Portable Mode settings (user‑triggered, no background processing)
  - Toggle “Portable Mode” and choose a portable root on the USB.
  - DB path configurable and stored under <portableRoot>/.chatlo-core/chatlo.db
  - Vault root stays on the USB; .intelligence already lives under the vault.
- Path bounds and safety
  - Bound PathResolver.inferVaultPath to known vault roots.
  - Validate db-path sits under the portable root when Portable Mode is on.
- Safe connect/disconnect
  - “Prepare for Disconnect” button: flush + close DB and show “Ready to eject.”
  - “Connect Portable DB” button after plugging in: re-open the DB at usb path or offer safe local fallback for this session.
- Packaging
  - Produce portable bundles per OS that don’t rely on app.getPath('userData') when Portable Mode is enabled.
  - Optional: include per‑OS builds together on the USB (Level 3).

## Recommended next step

- Approve me to deliver a minimal design+code change set to:
  - Add Portable Mode settings (no UI redesign; a simple settings toggle and path chooser).
  - Configure DatabaseManager to open from a user‑selected USB path.
  - Add “Prepare for Disconnect” and “Connect Portable DB” actions.
  - Harden path inference bounds.
  - Keep all intelligence operations JIT and user‑initiated.

If you approve, I’ll stage these changes carefully, run TS diagnostics, and provide a small smoke test plan.
