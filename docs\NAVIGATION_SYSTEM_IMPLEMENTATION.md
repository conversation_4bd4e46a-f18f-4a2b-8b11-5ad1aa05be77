# Navigation System Implementation Guide

## Overview

This document explains the complete implementation of the `chatlo://` navigation system that enables stateful file overlay navigation with context preservation.

## Problem Solved

**Before**: The `ChatLinkStyle` components had no way to actually open the `FilePageOverlay`, causing a disconnect between the UI and functionality.

**After**: A complete navigation service that bridges the gap between link clicks and component rendering, with full context preservation.

## Architecture Components

### 1. Navigation Service (`src/services/navigationService.ts`)

**Core Classes:**
- `ChatLoLinkParser`: Parses and builds `chatlo://` protocol links
- `NavigationManager`: Manages navigation state and file overlay lifecycle

**Key Features:**
- Universal link parsing for `chatlo://` protocol
- File overlay state management
- Context-aware navigation with origin tracking
- Event system for component communication

### 2. File Overlay Hook (`src/hooks/useFileOverlay.ts`)

**Purpose**: Provides a clean interface for components to interact with the navigation service.

**API:**
```typescript
const {
  isOpen,           // Whether file overlay is currently open
  filePath,         // Current file path
  fileName,         // Current file name
  origin,           // Origin context (where the overlay was opened from)
  openFileOverlay,  // Function to open file overlay
  closeFileOverlay, // Function to close file overlay
  navigateBack      // Function to navigate back to origin
} = useFileOverlay()
```

### 3. Updated Components

**ChatLinkStyle (`src/components/ChatLinkStyle.tsx`)**
- Now uses `navigationManager.openFileOverlay()` instead of just calling `onClick`
- Preserves chat context for return navigation

**FilePageOverlay (`src/components/FilePageOverlay.tsx`)**
- Integrated with navigation service
- Removed dependency on `fileViewerService`
- Uses navigation state for file information

**App.tsx**
- Added `FileOverlayManager` component
- Renders `FilePageOverlay` based on navigation state

## Data Flow Story

### 1. Link Click → Navigation Service → Component State → Overlay Render

```
User clicks ChatLinkStyle 
    ↓
ChatLinkStyle calls navigationManager.openFileOverlay(filePath, fileName, origin)
    ↓
NavigationManager updates state with fileOverlay: { isOpen: true, ... }
    ↓
App.tsx FileOverlayManager detects state change
    ↓
FilePageOverlay renders with proper context
```

### 2. Context Preservation Chain

```
Origin (Chat) 
    ↓
Link Click with context
    ↓
Navigation State stores origin
    ↓
Overlay opens with context
    ↓
Return navigation preserves context
```

### 3. State Management Flow

```
NavigationManager (Singleton)
    ↓
useFileOverlay Hook
    ↓
FileOverlayManager Component
    ↓
FilePageOverlay Component
```

## Implementation Details

### File Overlay State Structure

```typescript
interface NavigationState {
  // ... other navigation state
  fileOverlay?: {
    isOpen: boolean
    filePath: string
    fileName: string
    origin: {
      page: string           // 'chat', 'files', 'home', etc.
      context?: string       // vault name or context ID
      chatMessageId?: string // specific chat message ID
      conversationId?: string // conversation ID
    }
  }
}
```

### Navigation Events

```typescript
interface NavigationEvent {
  type: 'file-overlay-open' | 'file-overlay-close' | 'navigation-change'
  target: NavigationTarget
  data?: any
}
```

### Return Navigation Logic

The system automatically builds return links based on origin:
- **Chat**: Returns to specific conversation/message
- **Files**: Returns to file browser with context
- **Home**: Returns to home page
- **Custom**: Preserves any custom navigation context

## Testing the System

### 1. Development Test Component

A test component is available at `src/components/FileOverlayTest.tsx` and is automatically rendered on the HomePage in development mode.

**Features:**
- Simple file links with different contexts
- Chat context preservation testing
- Vault context testing

### 2. Manual Testing Steps

1. **Navigate to HomePage** (`/`)
2. **Find the "File Overlay Navigation Test" section** (development mode only)
3. **Click on any file link** (e.g., "example-document.md")
4. **Verify FilePageOverlay opens** with proper file information
5. **Check context preservation** in the overlay
6. **Test return navigation** using the close button

### 3. Expected Behavior

- ✅ File overlay opens immediately on link click
- ✅ File path and name are correctly displayed
- ✅ Origin context is preserved (chat, vault, etc.)
- ✅ Return navigation works correctly
- ✅ No infinite loops or state conflicts

## Integration Points

### 1. Existing Components

**FilesPage**: Already uses `fileViewerService.openFile()` - can be updated to use navigation service
**MessageBubble**: Has fallback to `fileViewerService` - can be updated to use navigation service
**ChatArea**: Can integrate with navigation service for file link handling

### 2. Future Enhancements

**Deep Linking**: Support for direct `chatlo://` protocol URLs
**Browser History**: Integration with React Router for proper back/forward navigation
**Keyboard Shortcuts**: Global shortcuts for file overlay management
**Context Menus**: Right-click context menus with navigation options

## Troubleshooting

### Common Issues

1. **File overlay doesn't open**
   - Check navigation service subscription in App.tsx
   - Verify FileOverlayManager is rendering
   - Check console for navigation service errors

2. **Context not preserved**
   - Verify origin object is passed correctly
   - Check navigation state updates
   - Ensure return navigation logic is working

3. **Infinite loops**
   - Check useEffect dependencies
   - Verify state update logic
   - Ensure proper cleanup in useEffect

### Debug Tools

- **Console Logs**: Navigation service provides detailed logging
- **React DevTools**: Check component state and props
- **Navigation State**: Monitor `navigationManager.getState()`

## Migration Guide

### From fileViewerService

**Before:**
```typescript
import { fileViewerService } from '../services/FileViewerService'

// Open file
fileViewerService.openFile(filePath, fileName)

// Subscribe to state
const unsubscribe = fileViewerService.subscribe(setState)
```

**After:**
```typescript
import { useFileOverlay } from '../hooks/useFileOverlay'

// Use hook
const { openFileOverlay, isOpen, filePath } = useFileOverlay()

// Open file with context
openFileOverlay(filePath, fileName, { page: 'current-page', context: 'vault-name' })
```

### Benefits of Migration

1. **Unified Navigation**: Single source of truth for all navigation
2. **Context Preservation**: Automatic origin tracking and return navigation
3. **Event System**: Component communication through navigation events
4. **State Management**: Centralized navigation state management
5. **Extensibility**: Easy to add new navigation features

## Conclusion

The navigation system provides a robust foundation for file overlay functionality with:

- ✅ **Complete Integration**: Links now actually open the overlay
- ✅ **Context Preservation**: Origin and return navigation work correctly
- ✅ **State Management**: Centralized navigation state
- ✅ **Event System**: Component communication
- ✅ **Extensibility**: Easy to add new features

This implementation resolves the foundation issue and provides a solid base for future navigation enhancements.

---

## chatlo:// In-App Linking (19/Aug/2025 update)

This section documents the current in-app deep linking and parameter passing between chat and the file overlay, including how parameters are emitted and how receivers consume them.

### Emission Points (Where links originate)

- `src/components/ChatLinkStyle.tsx`
  - Purpose: Render a clickable filename inside chat bubbles.
  - Behavior: Calls `navigationManager.openFileOverlay(filePath, fileName, origin)` with origin context.
  - Origin payload:
    - `page: 'chat'`
    - `context: vaultName` (the user-selected or inferred vault name/id)
    - When using `ChatLinkStyleWithContext` (preferred if message context is available):
      - `chatMessageId`
      - `conversationId`

```tsx
// ChatLinkStyleWithContext usage example
<ChatLinkStyleWithContext
  filename={fileName}
  filePath={filePath}
  vaultName={vaultName}
  chatMessageId={message.id}
  conversationId={message.conversation_id}
  className="..."
/>
```

- AI Actions (from `src/components/FilePageOverlay.tsx`)
  - When the user chooses Ask AI on a file, we navigate to chat:
    - `askAINavigationService.navigateToChat(...)`
    - Optional: include `action` query param (e.g. `summarize`, `extract`, `questions`).

### Transport Layer

- `src/services/navigationService.ts` (`NavigationManager`)
  - Central source of truth for `fileOverlay` state
  - Stores `origin` with:
    - `page`: `'chat' | 'files' | 'home' | ...`
    - `context`: vault name/id
    - `chatMessageId?`, `conversationId?`
  - Emits events on open/close to keep UI in sync

### Receiving Side (How parameters are consumed)

- `src/App.tsx`
  - Renders `FileOverlayManager` which shows `FilePageOverlay` when `fileOverlay.isOpen` is true.

- `src/components/FilePageOverlay.tsx`
  - Reads `navigationManager` state for `filePath`, `fileName`, and `origin`.
  - Implements smart back navigation: if `origin.page === 'chat'`, the left control returns to the specific chat (and message, when available). If `origin.page === 'files'`, return to Files with previous folder.

- `src/components/DocumentViewer.tsx`
  - Receives `chatContext` so the back icon and action behave contextually.
  - Adds `data-file-path` / `data-file-name` for global text-selection routing.

- `src/components/ChatArea.tsx` (Chat deep link receiver)
  - Reads URL params: `conversation`, `file`, `filename`, `vault`, `entities`, `selectedText`, `action`.
  - Creates an assistant intro message and attaches the file.
  - Important: Uses the URL `conversation` param directly (with a dedupe key `deeplink:<conversation>:<file>` in `sessionStorage`) to prevent duplicate inserts and cross-conversation mixing.

### URL/Param Reference

- `#/chat?conversation=<id>&file=<absolute-path>&filename=<name>&vault=<context-id|name>&action=<summarize|extract|chat|questions>&selectedText=<text>&entities=<jsonArray>`

Receiver responsibilities:
- Require `conversation` and `file` to proceed.
- If `action` present, augment assistant intro with action-specific guidance.
- Attach file (via unified files API) to the created message.

### ChatLinkStyle → FilePageOverlay: Required Steps

1) Emit
   - Use `ChatLinkStyleWithContext` when available to pass `conversationId` and `chatMessageId`.
   - Otherwise, `ChatLinkStyle` passes `page: 'chat'` and `context: vaultName`.

2) Store
   - `NavigationManager.openFileOverlay(filePath, fileName, origin)` writes `fileOverlay` state with `origin`.

3) Receive
   - `App.tsx / FileOverlayManager` renders `FilePageOverlay`.
   - `FilePageOverlay` reads `origin` and exposes `handleBackToOrigin()` to return precisely to chat bubble or files folder.

### Sessions Note

If your flows need logical sessions (e.g., deduping deep-links across reloads), use:
- `sessionStorage.setItem('deeplink:<conversationId>:<filePath>', '1')`
- Checked in `ChatArea.tsx` to ensure idempotent deep link intro creation.

This is orthogonal to Intelligence V02 sessions; no extra backend requirement.

