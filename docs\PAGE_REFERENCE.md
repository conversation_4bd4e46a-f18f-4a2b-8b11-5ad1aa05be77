# Page Architecture Reference

This document captures page-level architecture, component hierarchy, and user-story data flows so developers can quickly understand where to hook UI, which IPC endpoints to call, and how to persist intelligence data.

<PERSON><PERSON> emphasizes the File Viewer and Intelligence flow:

- Page: `src/components/FilePageOverlay.tsx`
- Components: `src/components/DocumentViewer.tsx`, `src/components/IntelligenceHub.tsx`
- Plugin Systems: Renderer File Type Plugins (`src/components/FileTypeRenderer/*`), Electron File Processor Plugins (`electron/fileProcessors/*`)

## Conventions

- Page > Components > Data Flow (by user story)
- IPC endpoint names match `docs/API_REFERENCE.md`
- Stored JSON follows the unified intelligence shape used by `useFileIntelligence`

---

## FilePageOverlay.tsx (Page)

### Responsibilities

- Orchestrates left (DocumentViewer) and right (IntelligenceHub) panels
- Owns page lifecycle and close action
- Consolidates extracted content and plugin metadata into the intelligence layer

### Component hierarchy

- FilePageOverlay
  - <PERSON>umentViewer (Left panel)
    - FileTypeRender<PERSON> (delegates to PDF/Markdown/Text/Image/Mermaid renderers)
  - IntelligenceHub (Right panel)
    - SmartLabelingInterface (AI-generated labels)
    - Smart Annotations (notes + optional AI responses)

### Key data model (unified)

```
interface FileIntelligenceData {
  file: { path: string; type: string; content: string };
  intelligence: {
    keyIdeas: KeyIdea[];
    summary?: string;
    fileIntelligence?: FileIntelligence; // full enriched object
  };
  ui: { selectedLabels: string[]; currentAnnotation: number };
  metadata?: { lastUpdated?: string; processingConfidence?: number; fileMetadata?: any };
}
```

### Primary flows (by user story)

1) Open file and render

- Trigger: user selects file in UI → `fileViewerService.openFile(path, name)`
- Page effect loads and mounts `DocumentViewer`
- File type detection centralized via `FileTypeRenderer/detectFileType`
- Display content loaded via `vault:readFile`

2) Extract text and metadata for intelligence

- If type requires/benefits from processing (pdf/image/markdown), `DocumentViewer` calls `files:processFile(path, typeHint)`
- Otherwise, text uses direct-read content
- `DocumentViewer` emits `onContentExtracted(text, metadata)`
- FilePageOverlay updates intelligence state:
  - `updateFileContent(text)`
  - `updateIntelligence({ fileMetadata: metadata, lastProcessed: new Date().toISOString() })`

3) AI-generated labels (SmartLabelingInterface)

- User clicks “process” or similar action inside `SmartLabelingInterface`
- Labels returned as `KeyIdea[]`
- FilePageOverlay consumes `onLabelsChanged(allIdeas)`:
  - Sets `intelligence.fileIntelligence.key_ideas = allIdeas`
  - Generates or clears summary
  - Persists unified state via `intelligence:write` (handled by hook/service)

4) Starred label handling

- Star state corresponds to the `user_confirmed` flag on `KeyIdea`
- Auto-selected suggestions use `auto_selected`
- UI derives “starred” from `user_confirmed === true`
- Persisted in `fileIntelligence.key_ideas` and mirrored into `intelligence.keyIdeas`

5) Annotations (save-first, local-first)

- Input saved immediately via `annotationStorageService.saveAnnotation(filePath, note)`
- If input is an AI prompt and local model is available, append AI response and update the same note via `annotationStorageService.updateAnnotation`
- View state paginates annotations; editing uses `updateAnnotation`

### IPC endpoints used

- Files
  - `vault:readFile` (display content)
  - `files:processFile` (plugin-based extraction; pdf/image/markdown)
- Intelligence
  - `intelligence:read` / `intelligence:write` / `intelligence:listSessions`
- Plugins (diagnostics)
  - `files:getFileProcessorPlugins`

### Storage layout and JSON example

Depending on configuration, intelligence is saved under vault/context intelligence storage (see API reference). Example minimal payload:

```json
{
  "file": {
    "path": "/abs/path/to/file.md",
    "type": "markdown",
    "content": "# Title..."
  },
  "intelligence": {
    "keyIdeas": [
      {
        "id": "idea_1",
        "text": "Core concept",
        "relevance_score": 0.92,
        "auto_selected": true,
        "user_confirmed": false
      }
    ],
    "summary": "Document Summary...",
    "fileIntelligence": {
      "file_path": "/abs/path/to/file.md",
      "key_ideas": [/* same as keyIdeas above */],
      "smart_annotations": [
        {
          "id": "note_1712345678",
          "type": "mixed",
          "content": "User note",
          "ai_response": "Model answer",
          "has_ai_response": true,
          "created_at": "2024-05-01T10:00:00.000Z",
          "updated_at": "2024-05-01T10:00:10.000Z"
        }
      ],
      "processing_confidence": 0.85,
      "analysis_metadata": {
        "model_used": "local-llm",
        "timestamp": "2024-05-01T10:00:00.000Z"
      },
      "created_at": "2024-05-01T10:00:00.000Z",
      "updated_at": "2024-05-01T10:00:10.000Z"
    }
  },
  "ui": { "selectedLabels": [], "currentAnnotation": 0 },
  "metadata": {
    "lastUpdated": "2024-05-01T10:00:10.000Z",
    "processingConfidence": 0.85,
    "fileMetadata": {
      "frontmatter": { "title": "Doc" },
      "lines": 200,
      "words": 1200
    }
  }
}
```

---

## DocumentViewer.tsx (Component)

### Responsibilities

- Detect file type and route rendering to the appropriate file type plugin
- Load display content (vault API) and extract text/metadata for intelligence
- Emit content lifecycle events back to page

### File type handling

- Uses `FileTypeRenderer/detectFileType(fileName)`
- Renders via `<FileTypeRenderer ... />` which resolves a plugin:
  - `pdf` → `PDFRenderer`
  - `markdown` → `MarkdownRenderer`
  - `mermaid` → `MermaidRenderer` (if markdown content matches Mermaid grammar)
  - `text`/`code` → `TextRenderer`
  - `image` → `ImageRenderer`
  - fallback → `UnsupportedRenderer`

### Extraction pipeline

- pdf/image/markdown → call `files:processFile` and emit `onContentExtracted(text, metadata)`
- text/code → direct content used as extracted text

### Events (to parent)

- `onContentLoad(content: string)`
- `onContentExtracted(text: string, metadata: any)`
- `onTextSelection(selected: string, position)`

---

## IntelligenceHub.tsx (Component)

### Responsibilities

- Display SmartLabelingInterface (AI-generated labels), manage label changes
- Manage Smart Annotations (save-first), optionally append AI responses from local models

### Labels

- `onLabelsChanged(KeyIdea[])` bubbles to page
- Starred labels: `user_confirmed === true`
- Selection state: `auto_selected` provided by model, user toggles `user_confirmed`

### Annotations (save-first)

- Create: `annotationStorageService.saveAnnotation`
- Update: `annotationStorageService.updateAnnotation`
- AI response: appended into the same note after local model call

### IPC endpoints used

- None directly; annotations use local storage service. Intelligence persistence is handled by the page/hook via `intelligence:*` APIs.

---

## Plugin Systems

### Renderer File Type Plugins

Located under `src/components/FileTypeRenderer/*`, registry in `index.tsx`.

```
interface FileTypeInfo { type: 'pdf'|'markdown'|'mermaid'|'text'|'image'|'code'|'unsupported'; ... }
interface FileTypePlugin {
  canHandle(fileTypeInfo: FileTypeInfo): boolean;
  render(props: FileRenderProps): React.ReactNode;
  extractText?(content: string): Promise<string>; // Optional, renderer-side only
  getMetadata?(content: string): Promise<any>;
}
```

Renderer plugins handle display. Text/metadata for intelligence should be sourced via Electron processors when available.

### Electron File Processor Plugins

Located under `electron/fileProcessors/*`.

```
interface FileProcessorPlugin {
  name: string; version: string;
  supportedTypes: string[]; supportedExtensions: string[];
  canProcess(filePath: string, fileType: string): boolean;
  process(filePath: string): Promise<{ text?: string; metadata?: any; error?: string }>;
}
```

Called via `files:processFile(filePath, fileTypeHint?)`.

---

## Typical End-to-End Trace

1) User opens `file.md` → `vault:readFile` → Markdown rendered
2) `files:processFile(file.md, 'md')` → MarkdownPlugin extracts frontmatter + metrics
3) `onContentExtracted(text, metadata)` → FilePageOverlay updates intelligence state
4) User stars labels in IntelligenceHub → `onLabelsChanged` → state updated and persisted
5) User adds a note → saved locally → optional AI response appended

---

## Notes and Guarantees

- Single-source type detection: prefer `FileTypeRenderer/detectFileType`
- Consolidation: All plugin-derived metadata must be funneled through FilePageOverlay to update intelligence
- Local-first: No external services required; AI responses use local model service


