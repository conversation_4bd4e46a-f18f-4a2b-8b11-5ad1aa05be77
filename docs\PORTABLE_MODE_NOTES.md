# Portable Mode (Kernel-Only) – V03 Notes

This document summarizes the kernel-side support for Portable Mode. No background processing; all operations are user-triggered from the renderer.

## Settings
- settings:setPortableMode(enabled: boolean)
- settings:setDBPath(dbPath: string)
  - Guard: dbPath must be within current vault root

## DB Lifecycle (user-triggered IPC)
- db:prepareForDisconnect – closes DB safely
- db:connectPortableDB – reads settings.db-path and opens DB there
- db:openAtPath(dbPath: string) – open with guard; integrity check executes via DatabaseManager.init()
- db:safeClose – internal close helper

## Migration (user-triggered)
- db:migrateToPortablePath
  - Creates target directory if needed
  - Copies default local DB file to target path
  - Validates by opening DB at target path

## Guard Rails
- dbPath must be under vault root (local-first design)
- No autonomous data collection or background tasks
- Errors returned in IPC payload; renderer decides UX

## Notes
- modelUpdateLogic vs modelUpdateService are separate pipelines (see preservation.md).
- Future: expose actual current dbPath from DatabaseManager if needed.

## Safe Exit (Close Intercept)
- Main intercepts BrowserWindow close and sends app:request-close to renderer
- <PERSON>derer shows ModalConfirmClose with blurred backdrop
  - Local copy: “Are you sure you want to close ChatLo? Any in-progress operations will stop.”
    - x-small: “Potential lost: generating responses, unsaved notes..etc.”
  - USB copy: “Close ChatLo and prepare for safe USB removal? We’ll flush and close your portable database so it’s safe to unplug.”
- On confirm: renderer sends app:confirm-close
  - Main checks portable-mode-enabled
  - If enabled: db:prepareForDisconnect
  - Then app.quit()
- No global kill of node/electron processes; only our PID tree exits

