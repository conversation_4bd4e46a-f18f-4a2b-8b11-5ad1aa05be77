## Search, Indexing, and Embedding Reference

### Overview
This document explains how ChatLo parses documents, builds search indexes, generates embeddings, and exposes these capabilities through a plugin-friendly architecture. The design is local-first, portable, and size-sensitive:
- Canonical intelligence lives inside each vault (portable).
- Indexes are rebuildable caches under the vault (disposable).
- Embedding/search engines are pluggable and can be switched per vault.

You can start small (keyword + minimal vectors) and opt-in to faster engines (ANN/HNSW) only when a vault really needs it.

### Stacks (Composable, swappable)
- Data preparation (shared)
  - Normalize files, chat content, artifacts, labels, and notes into clean, UTF‑8 text with metadata.
  - Chunk content with semantic boundaries (headings, paragraphs, code fences) and stable IDs.
- Keyword search (default)
  - SQLite FTS5 over chunk text for fast local keyword/phrase search.
- Embeddings (local-first)
  - Default: intfloat/multilingual-e5-small (384d) for multilingual (incl. Chinese).
  - Optional per-vault: e5-base, bge-small-zh, or other local models.
- Vector search engines (optional, per vault)
  - Brute-force cosine (smallest, zero-build).
  - SQLite-VSS/HNSW (fast queries, larger index, native extension).
  - Qdrant-lite (local HNSW service, richer filtering).

All ANN engines index the same neutral vectors, so you can switch engines without re-parsing documents.

### Roles of Each Module

- Data Preparation Layer
  - Reads from vault: documents, chat transcripts, artifacts, labels, user notes, and existing doc intelligence.
  - Outputs normalized “indexable units” (chunks) with stable IDs and metadata (file hash, section path, timestamps).

- Indexer (keyword)
  - Builds and maintains a per-vault FTS5 index of chunks.
  - Supports incremental updates on file add/change/remove.
  - Lives in `<vault>/.index/fts.db`.

- Embedder
  - Generates vectors for chunks and queries.
  - Supports multiple local models; tracks `model` and `dim`.
  - Stores vectors once in `<vault>/.index/vectors.db` (FLOAT32 or INT8).
  - Can be swapped per vault without touching indexes.

- ANN Search Engine (optional plugin)
  - Builds an acceleration structure (e.g., HNSW) from vectors.
  - Lives in engine-specific storage, e.g., `<vault>/.index/sqlite-vss.db` or `<vault>/.index/qdrant/`.
  - Fully disposable; rebuilds from `vectors.db`.

- Orchestrator (core)
  - Routes file events to indexers/embedders/ANN engines.
  - Executes hybrid search: shortlist via FTS → re-rank via vectors (brute-force or ANN).
  - Emits events for UI updates and progress.

### Data Source Preparation (What we index)

- Master (file system contents)
  - Source: documents in the selected vault.
  - Normalize text (UTF‑8), detect MIME, compute stable `file_hash`.

- Chat (messages and conversations)
  - Source: local DB messages and pinned notes.
  - Extract plain text per message with timestamps and conversation metadata.
  - Chunk long messages; preserve author/role in metadata.

- Artifacts (code, markdown, images, diagrams)
  - Source: artifacts sidebar storage.
  - For text/code: normalize and chunk with language hints.
  - For images: OCR text if available; store captions/alt text when present.

- Doc (original file text)
  - Source: parsed documents (PDF/Word/Markdown/etc.).
  - Chunk by headings/paragraphs; store `section_path` (e.g., “Executive Summary/Hypothesis”).

- Doc Intelligence (existing `.intelligence` JSON)
  - Source: `<vault>/.intelligence/files/[hash].json`.
  - Use summaries, key ideas, and annotations to enrich chunk metadata.
  - Never treat as index; this is canonical intelligence for rebuilds.

- Labels
  - Source: user-applied labels/tags.
  - Attach to chunks or files as metadata for filtering and boosting.

- User Notes
  - Source: freeform notes linked to files/conversations.
  - Index as separate chunks with strong relevance to the linked item.

All sources output standardized “chunks” with:
- `chunk_id` (stable content hash),
- `file_hash`, `relative_path`, `section_path`,
- `text` (normalized),
- `meta` (type: chat|doc|artifact|note, timestamps, labels, etc.).

### Size-Sensitive Strategy (Per-vault, on-demand)
- Default:
  - FTS5-only + lazy embeddings + brute-force re-rank (INT8 vectors).
  - Embedding only when a chunk is searched or recently used.
- Opt-in speed boost:
  - Per vault: enable ANN plugin (SQLite‑VSS or Qdrant‑lite).
  - Build ANN index in the background; continue serving queries via FTS + brute-force until ready.
- Switching engines:
  - Neutral vectors in `vectors.db` are reused.
  - Engine-specific indexes are disposable caches.

### Middleware ↔ Plugin Request Flow

- Core API registry (main process)
  - All endpoints are namespaced and pass through middleware (for validation, logging, authorization).
  - Plugin endpoints are registered under `plugin_<pluginId>:<endpoint>`.

- Typical call path
  1) Renderer calls Unified API client.
  2) Main process middleware validates and sanitizes.
  3) Request routed to core or plugin handler.
  4) Handler reads/writes vault-scoped files/DBs and emits events.

#### Core endpoints (stable)
- `intelligence:read` / `intelligence:write`
  - Reads/writes canonical JSON under `.intelligence`.
- `events:subscribe` / `events:unsubscribe`
  - Receives `file:*`, `intelligence:updated`, `task:progress`.

#### Index/Search/Embedding plugin endpoints (examples)

- Indexing
  - `plugin_<id>:index:status`
    - Get per-vault counts (`files`, `chunks`, `embeddings`) and timestamps.
  - `plugin_<id>:index:rebuild`
    - Force a rebuild from canonical sources; runs in background.
  - `plugin_<id>:index:onFileEvent`
    - Notify plugin of file add/change/remove for incremental updates.

- Embedding
  - `plugin_<id>:embed:modelInfo`
    - Report `model`, `dim`, supported languages.
  - `plugin_<id>:embed:enqueue`
    - Queue missing chunk embeddings for a vault (optional filters).
  - `plugin_<id>:embed:progress`
    - Track background queue progress.

- Search
  - `plugin_<id>:search:keyword`
    - Keyword/phrase search via FTS; returns ranked chunk/file hits.
  - `plugin_<id>:search:vector`
    - Vector-only search with a query embedding.
  - `plugin_<id>:search:hybrid`
    - FTS shortlist + vector re-rank; uses active embedder.

#### Request/response shapes (illustrative)

- Request (hybrid search)
```json
{
  "vaultId": "work-vault",
  "query": "project timeline risk",
  "limit": 20,
  "filters": { "pathPrefix": "client-project/documents" }
}
```

- Response (hybrid search)
```json
{
  "results": [
    {
      "file_hash": "ab12...",
      "relative_path": "project-requirements.md",
      "section_path": "Timeline/Risks",
      "snippet": "Key risks include vendor delay and scope creep...",
      "score": 0.86,
      "meta": { "type": "doc", "labels": ["risk","timeline"] }
    }
  ],
  "engine": { "keyword": "fts5", "vector": "brute_force", "model": "intfloat/multilingual-e5-small", "dim": 384 }
}
```

### Operational Notes

- Portability
  - Canonical intelligence: `.intelligence` (portable with vault).
  - Indexes: `.index` (rebuildable caches; safe to delete).
- Reliability
  - WAL mode for DBs; small batched writes; background jobs with progress events.
  - If any index is corrupted, delete and rebuild from `.intelligence`.
- Security & Privacy
  - Local-only by default; explicit opt-in for any remote calls.
  - Respect vault boundaries; never index outside selected vaults.
- Chinese and multilingual content
  - Default embedder: `intfloat/multilingual-e5-small` (supports zh).
  - Per-vault override: `e5-base` or `bge-small-zh` for zh-dominant vaults.
- Interface consistency
  - Shared request/response types live in `src/types/index.ts` as the single source of truth.

### Quick Start (recommended defaults)
- Enable FTS5 index per vault automatically.
- Turn on lazy embeddings (INT8) using multilingual‑e5‑small.
- Offer a per-vault “Speed Boost” toggle to build an ANN index (SQLite‑VSS first).
- Use hybrid search by default: FTS shortlist → vector re-rank.

## Data Flow Plan (end-to-end)

### Files pipeline (Explorer/Indexing)
- Detect file add/change/remove within the active vault (via `vault:*` + PathResolver; no raw FS in renderer).
- Parse and chunk
  - Normalize to UTF‑8; detect MIME; compute `file_hash`.
  - Chunk by semantic boundaries; assign stable `chunk_id`.
- Persist canonical intelligence
  - Write/update `<vault>/.intelligence/files/[file_hash].json` with labels, key ideas, and metadata (source of truth).
  - Emit `intelligence:updated`.
- Update indexes (rebuildable caches under `<vault>/.index/`)
  - Keyword: upsert chunk text into `fts.db` (FTS5).
  - Vectors: if enabled, enqueue new/changed chunks for embedding into `vectors.db` (FLOAT32 or INT8).
  - ANN (optional): if the vault has a speed-boost engine enabled, ingest vectors to its own cache (e.g., `sqlite-vss.db`).
- Events and progress
  - Emit `task:progress` during parsing/embedding/ANN ingest.
  - File removals delete entries from FTS/vectors and schedule compaction when idle.

### Chat pipeline (Messages & Attachments)
- Messages
  - On send/receive, store message locally and normalize to text.
  - For search/context: index long messages as chunks in `fts.db` with type `chat`, optionally embed lazily when first used by search/RAG.
- Attachments (size-sensitive, user-controlled)
  - Offer “Add to Vault for indexing” (recommended) or “Use once (ephemeral)”.
  - If added to vault: copy into active vault (e.g., `conversations/<id>/attachments/`), run the Files pipeline; becomes searchable and reusable.
- If ephemeral: parse/chunk in-memory or under a session sandbox (e.g., `.index/session/<conversationId>/`), embed only for the current chat; do not write `.intelligence`; exclude from global search.
- Reuse vectors first
  - On attach or reference, check `vectors.db` for existing embeddings by `chunk_id` and `model`. If present, reuse; if missing, embed just-in-time and store according to the chosen mode (vault or session).
- Language-aware model routing
  - Default to `intfloat/multilingual-e5-small` (zh-friendly). If content is predominantly CJK, vaults may opt into a zh-specialized embedder (e.g., `bge-small-zh`).

### FilePageOverlay readiness
- On open
  - Load `<vault>/.intelligence/files/[file_hash].json` first for immediate display (labels, ideas, annotations).
  - Subscribe to `intelligence:updated` and `task:progress` to live-refresh labels/sections and indexing state.
- Search-in-overlay
  - Use hybrid search scoped to the file path: shortlist via FTS, re-rank via vectors if available; if vectors are missing, enqueue embeddings in background without blocking the UI.
- Edits and labels
  - User edits/labels write back to `.intelligence` and emit `intelligence:updated`, which flows to index updates asynchronously.

### Do chat file attachments use the same embedding data?
- Yes. The embedding store is unified per vault.
  - Persistent path: `<vault>/.index/vectors.db` holds embeddings for all chunked content (files, chat, artifacts, notes) with `meta.type` describing the source.
  - Reuse precedence: existing vectors (same `chunk_id` + `model`) are reused; missing ones are embedded on-demand and saved.
- Ephemeral option (no vault write)
  - For one-off attachments, vectors may be kept in a session-scoped cache or memory. They power the current chat’s RAG but are not added to global search and are discarded when the session ends.
- Result: attachments added to a vault become part of the vault’s unified search and RAG; ephemeral attachments remain private to the session.

### API touchpoints per flow (high level)
- Files
  - `files:*` (add/change/remove) → parse/chunk → `intelligence:write` → index updates (FTS, vectors, optional ANN) → events.
- Chat
  - `chat:*` (store message) → optional chunk/index for search → lazy embed on first use.
  - `attachments:*` → user chooses vault or ephemeral → run respective pipeline.
- Search
  - `plugin_<id>:search:hybrid` (default) or `:keyword`/`:vector` → returns ranked results; emits minimal telemetry locally for progress only (no network).

### Privacy and controls (user-rules aligned)
- Intelligence and indexing happen only on user actions (attach, search, open overlay, enable speed-boost).
- Vault boundaries are strictly respected. No indexing outside selected vaults. Ephemeral mode avoids persistence by design.
- External engines/models are opt-in; defaults are fully offline.