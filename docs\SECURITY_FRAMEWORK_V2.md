# 🛡️ ChatLo Security Framework V2.0

## 🎯 **Security Philosophy: Focused & Practical**

### **What We DON'T Handle (Leave to OS/Antivirus)**
- ❌ **Network Attacks**: OS firewall and network security
- ❌ **Code Injection**: OS-level process isolation and antivirus
- ❌ **File Trojans**: No executable files allowed (.exe, .bat, .cmd, .ps1, etc.)
- ❌ **System-level Security**: Windows Defender, antivirus software

### **What We DO Handle (App-level Privacy)**
- ✅ **Private Mode Enforcement**: Local vs External model access control
- ✅ **Data Sharing Awareness**: User notification when sharing sensitive content
- ✅ **Logging & Transparency**: Clear audit trail of external communications
- ✅ **Gateway Rules**: Smart content filtering before external LLM calls

---

## 🔐 **Core Security Model: Private Mode**

### **Private Mode ON** 🔒
- **Behavior**: Only local models (Ollama, LM Studio) available
- **External Models**: Completely disabled and hidden from UI
- **File Sharing**: All files stay local, no external transmission
- **Logging**: Local operations only, no external communication logs

### **Private Mode OFF** 🌐
- **Behavior**: Both local and external models available
- **External Models**: OpenRouter, Claude, GPT, etc. enabled
- **File Sharing**: Subject to gateway rules and user consent
- **Logging**: All external communications logged with content awareness

---

## 🚦 **Gateway Rules: Data Sharing Control**

### **Security Levels for External LLM Communication**

#### **🔴 STRICT Mode**
**Behavior**: Block transmission of detected sensitive patterns
**Warning Message**:
```
🚫 Sensitive Content Blocked

Your message contains potentially sensitive information that cannot be shared with external AI services:
• Personal Identifiable Information (PII) detected
• [Specific patterns found: email, phone, etc.]

This content is blocked in Strict mode to protect your privacy.

[Edit Message] [Change Security Level in Settings]
```

#### **🟡 BALANCED Mode** (Default)
**Behavior**: Ask for permission before sharing detected patterns
**Warning Message**:
```
⚠️ Sensitive Content Detected

Your message may contain sensitive information:
• [Specific patterns: email address, phone number, etc.]

This will be sent to [External AI Service]. Do you want to proceed?

💡 You can change your security level in Settings for future messages.

[Cancel] [Send Anyway]
```

#### **🟢 DISABLED Mode**
**Behavior**: Reminder only, no blocking or permission requests
**Warning Message**:
```
ℹ️ Content Sharing Reminder

You're sharing content with [External AI Service].
Security filtering is currently disabled.

The following types of information are being shared:
• [Content summary without revealing actual data]

[OK] [Change Settings]
```

---

## 📊 **Logging & Transparency System**

### **External Communication Log**
```typescript
interface ExternalCommLog {
  timestamp: string
  model: string           // "gpt-4", "claude-3", etc.
  mode: 'strict' | 'normal' | 'disable'
  contentType: 'text' | 'file' | 'image'
  filesShared?: string[]  // File names only, not content
  patternsDetected?: string[] // "email", "phone", etc.
  userConsent: boolean
  blocked: boolean
}
```

### **Privacy Dashboard**
- **Location**: Settings → Privacy & Security
- **Features**:
  - View all external communications
  - Export communication logs
  - Change security level (Strict/Normal/Disable)
  - Clear communication history
  - Private mode toggle with clear explanation

---

## 🔧 **Implementation Architecture**

### **1. Private Mode Controller**
```typescript
class PrivateModeController {
  private isPrivateMode: boolean = false

  // Core private mode logic
  setPrivateMode(enabled: boolean): void
  isExternalModelAllowed(): boolean
  getAvailableModels(): Model[]

  // Integration with model selection
  filterModelsByPrivacy(models: Model[]): Model[]
}
```

### **2. Gateway Security Service**
```typescript
class GatewaySecurityService {
  private securityLevel: 'strict' | 'normal' | 'disable' = 'normal'

  // Content analysis before external transmission
  async analyzeContent(content: string, files?: File[]): Promise<SecurityAnalysis>
  async checkFileSharing(files: File[]): Promise<FileSecurityCheck>

  // Pattern detection
  detectSensitivePatterns(content: string): string[]

  // User consent management
  async requestUserConsent(analysis: SecurityAnalysis): Promise<boolean>
}
```

### **3. Communication Logger**
```typescript
class CommunicationLogger {
  // Log external LLM communications
  async logExternalComm(log: ExternalCommLog): Promise<void>

  // Retrieve logs for privacy dashboard
  async getCommLogs(dateRange?: DateRange): Promise<ExternalCommLog[]>

  // Export logs
  async exportLogs(format: 'json' | 'csv'): Promise<string>
}
```

---

## 🎨 **User Experience Design**

### **Private Mode Toggle**
- **Location**: Top-right corner of main interface
- **Visual**: Clear lock icon (🔒 Private / 🌐 Connected)
- **Tooltip**: "Private: Local models only" / "Connected: Local + External models"

### **Security Level Settings**
- **Location**: Settings → Security
- **Options**:
  - 🔴 **Strict**: "Maximum protection - Block sensitive content transmission"
  - 🟡 **Balanced**: "Balanced protection - Ask permission before sharing sensitive content"
  - 🟢 **Disabled**: "No restrictions - Allow all content sharing with reminders"

### **Custom Vault Patterns**
- **Location**: Settings → Security → Custom Vault Patterns
- **Purpose**: Define patterns that are ALLOWED to bypass security filtering
- **Description**: "Define custom patterns for your vault names. These will be allowed regardless of security level."
- **Examples**:
  - `patent.*analysis` - Allow patent-related analysis discussions
  - `proprietary.*research` - Allow proprietary research sharing
  - `confidential.*strategy` - Allow confidential strategy discussions

### **Detailed Warning Examples**

#### **Strict Mode - Content Blocked**
```
🚫 Sensitive Content Blocked

Your message contains potentially sensitive information:
• Email address detected: j***@company.com
• Patent reference detected: Patent #1,234,567
• Proprietary information detected: "confidential algorithm"

This content cannot be shared with Claude-3 (Anthropic) in Strict mode.

Options:
• Edit your message to remove sensitive information
• Add exception patterns in Settings → Security → Custom Vault Patterns
• Change to Balanced mode in Settings for permission-based sharing

[Edit Message] [Open Security Settings]
```

#### **Balanced Mode - Permission Request**
```
⚠️ Sensitive Content Detected

Your message contains potentially sensitive information:
• Email address: j***@company.com
• Patent reference: Patent #1,234,567

This will be sent to Claude-3 (Anthropic). Do you want to proceed?

💡 Tip: You can change your security level in Settings → Security

[Cancel] [Send Anyway] [Don't Ask Again for This Pattern]
```

#### **Disabled Mode - Information Reminder**
```
ℹ️ Content Sharing Active

You're sharing content with Claude-3 (Anthropic):
• Text content with potential PII patterns
• Security filtering is currently disabled

All content will be transmitted without restrictions.

[OK] [Change Security Settings]
```

---

## 📋 **Security Configuration**

### **Default Settings**
```typescript
const DEFAULT_SECURITY_CONFIG = {
  privateMode: false,           // Allow external models by default
  securityLevel: 'normal',      // Balanced approach
  logRetentionDays: 30,         // Keep logs for 30 days
  patternDetection: true,       // Enable sensitive pattern detection
  fileWarnings: true,           // Warn about file sharing
  exportLogsEnabled: true       // Allow users to export their data
}
```

### **Sensitive Pattern Detection**

#### **Smart Sensitive Patterns (Context-Aware Detection)**
```typescript
const SENSITIVE_PATTERNS = {
  // Personal Identifiable Information (PII)
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
  ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
  creditCard: /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,

  // Financial Information
  bankAccount: /\b\d{8,17}\b/g,
  routingNumber: /\b\d{9}\b/g,

  // Legal & Proprietary (Smart Detection)
  patent: /\b(?:patent|Patent)\s*(?:no\.?|number|#)\s*[\d,\-\/]+/gi,

  // Contract & Legal Headers (NEW - Sensitive)
  contractTitle: /\b(?:CONFIDENTIAL|PROPRIETARY|NON-DISCLOSURE|AGREEMENT|CONTRACT)\s+(?:AGREEMENT|CONTRACT|DOCUMENT)/gi,
  legalHeader: /\b(?:ATTORNEY-CLIENT|PRIVILEGED|WORK PRODUCT|CONFIDENTIAL COMMUNICATION)/gi,

  // Business Sensitive (Context-aware)
  proprietary: /\b(?:proprietary|confidential|internal|restricted)\b(?!\s*(?:footer|disclaimer|notice))/gi,
  nda: /\b(?:NDA|non-disclosure|confidentiality agreement)\b/gi,

  // Personal Information
  address: /\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)\b/gi,
  dob: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g
}

// Patterns to EXCLUDE from detection (common footers/disclaimers)
const EXCLUDED_PATTERNS = {
  commonFooter: /\b(?:this\s+(?:email|message|communication)\s+is\s+confidential|confidential\s+and\s+proprietary|for\s+internal\s+use\s+only)/gi,
  legalDisclaimer: /\b(?:disclaimer|notice|warning):\s*(?:confidential|proprietary)/gi,
  copyrightFooter: /\b(?:copyright|©)\s*\d{4}.*(?:all\s+rights\s+reserved|company\s+name)/gi,
  emailSignature: /\b(?:confidential|proprietary).*(?:signature|footer|disclaimer)/gi
}
```

#### **Custom Vault Patterns (User-Defined Exceptions)**
**Purpose**: Allow specific patterns to bypass security filtering when user explicitly permits them.

**Use Cases**:
- Sharing patented graphs for AI analysis advice
- Discussing proprietary algorithms for optimization suggestions
- Sending copyrighted content for fair use analysis
- Sharing business-sensitive data for strategic planning

**How It Works**:
- User defines patterns they want to ALLOW through the gateway
- These patterns override the default sensitive pattern blocking
- Example: `patent.*graph|algorithm.*optimization` would allow patent-related graphs and algorithm discussions

**Configuration**:
```typescript
// User can add patterns like:
"patent.*(?:graph|diagram|chart)"     // Allow patent-related visuals
"proprietary.*(?:algorithm|method)"   // Allow proprietary technical discussions
"confidential.*(?:strategy|plan)"     // Allow confidential business planning
```

---

## � **Plugin Override System**

### **Simple JSON Override Mechanism**
**Purpose**: Allow external plugins to override security patterns without over-engineering

**Location**: `plugins/security-overrides.json`
```json
{
  "version": "1.0.0",
  "pluginName": "CustomSecurityRules",
  "overrides": {
    "sensitivePatterns": {
      "email": "/\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b/g",
      "customPattern": "/\\b(?:secret|classified)\\s+(?:project|document)/gi"
    },
    "excludedPatterns": {
      "companyFooter": "/\\b(?:Acme Corp|confidential footer)/gi"
    },
    "customVaultPatterns": [
      "research.*(?:data|analysis)",
      "internal.*(?:memo|report)"
    ]
  },
  "enabled": true
}
```

### **Plugin Loading Logic**
```typescript
class SecurityPatternLoader {
  private defaultPatterns: SecurityPatterns
  private pluginOverrides: SecurityPatterns | null = null

  async loadPatterns(): Promise<SecurityPatterns> {
    // Load default patterns
    const patterns = { ...this.defaultPatterns }

    // Check for plugin override
    const overrideFile = path.join(process.cwd(), 'plugins/security-overrides.json')

    if (fs.existsSync(overrideFile)) {
      try {
        const override = JSON.parse(fs.readFileSync(overrideFile, 'utf8'))

        if (override.enabled) {
          // Merge plugin patterns with defaults
          patterns.sensitivePatterns = {
            ...patterns.sensitivePatterns,
            ...override.overrides.sensitivePatterns
          }

          patterns.excludedPatterns = {
            ...patterns.excludedPatterns,
            ...override.overrides.excludedPatterns
          }

          console.log(`[Security] Loaded plugin overrides: ${override.pluginName}`)
        }
      } catch (error) {
        console.warn('[Security] Failed to load plugin overrides:', error.message)
      }
    }

    return patterns
  }
}
```

### **Plugin Override Examples**

#### **Enterprise Plugin Override**
```json
{
  "version": "1.0.0",
  "pluginName": "EnterpriseSecurityRules",
  "overrides": {
    "sensitivePatterns": {
      "employeeId": "/\\b(?:EMP|ID)[-\\s]?\\d{6,8}\\b/gi",
      "projectCode": "/\\b(?:PROJ|PROJECT)[-\\s]?[A-Z]{2,4}[-\\s]?\\d{3,6}\\b/gi",
      "companyConfidential": "/\\b(?:ACME\\s+CONFIDENTIAL|INTERNAL\\s+ACME)/gi"
    },
    "excludedPatterns": {
      "acmeFooter": "/\\bThis\\s+message\\s+contains\\s+Acme\\s+Corp\\s+confidential/gi"
    }
  },
  "enabled": true
}
```

#### **Legal Firm Plugin Override**
```json
{
  "version": "1.0.0",
  "pluginName": "LegalFirmSecurity",
  "overrides": {
    "sensitivePatterns": {
      "caseNumber": "/\\b(?:Case|Matter)\\s+No\\.?\\s*\\d{4}-\\d{6}/gi",
      "clientPrivilege": "/\\b(?:ATTORNEY-CLIENT|PRIVILEGED|WORK\\s+PRODUCT)/gi",
      "billableHours": "/\\b\\d+\\.\\d+\\s+hours?\\s+@\\s+\\$\\d+/gi"
    },
    "excludedPatterns": {
      "firmDisclaimer": "/\\bThis\\s+communication\\s+may\\s+be\\s+privileged/gi"
    }
  },
  "enabled": true
}
```

---

## �🚀 **Implementation Plan**

### **Phase 1: Core Private Mode (Week 1)**
1. **PrivateModeController** implementation
2. **Model filtering** based on private mode state
3. **UI toggle** for private mode
4. **Settings integration** for private mode persistence

### **Phase 2: Gateway Security (Week 2)**
1. **GatewaySecurityService** implementation
2. **Pattern detection** for sensitive content
3. **File sharing analysis** and warnings
4. **User consent dialogs** for strict mode

### **Phase 3: Logging & Transparency (Week 3)**
1. **CommunicationLogger** implementation
2. **Privacy dashboard** in settings
3. **Log export** functionality
4. **Log retention** management

### **Phase 4: Integration & Testing (Week 4)**
1. **Integration** with existing model selection
2. **End-to-end testing** of all security levels
3. **User experience** refinement
4. **Documentation** and user guides

---

## 🔄 **Migration from Current Security**

### **What to Remove**
- ❌ Complex path validation systems
- ❌ File system monitoring scripts
- ❌ Quarantine mechanisms
- ❌ Emergency cleanup scripts
- ❌ Overly complex security patterns

### **What to Keep**
- ✅ Basic file type restrictions (no executables)
- ✅ Input validation for user data
- ✅ Error handling and logging infrastructure
- ✅ Settings persistence mechanisms

### **Migration Steps**
1. **Backup** current security configurations
2. **Implement** new security framework alongside old
3. **Test** new framework thoroughly
4. **Switch** to new framework
5. **Remove** old security code
6. **Update** documentation

---

## 📊 **Success Metrics**

### **User Experience Metrics**
- **Private Mode Usage**: % of users who enable private mode
- **Security Level Distribution**: Strict/Normal/Disable usage
- **Warning Interaction**: How users respond to security warnings
- **Log Export Usage**: How often users export their data

### **Security Effectiveness**
- **Blocked Transmissions**: Files/content blocked in strict mode
- **Pattern Detection Accuracy**: False positive/negative rates
- **User Consent Rates**: How often users consent to sharing
- **Privacy Awareness**: User understanding of data sharing

---

## 🎯 **Key Benefits of New Approach**

### **Simplicity**
- **Focused scope**: Only handle what matters for privacy
- **Clear boundaries**: OS handles system security, we handle data privacy
- **User-friendly**: Simple on/off switches, clear warnings

### **Transparency**
- **Visible logging**: Users can see all external communications
- **Export capability**: Users own their data and logs
- **Clear consent**: No hidden data sharing

### **Flexibility**
- **Three security levels**: Users choose their comfort level
- **Granular control**: Per-conversation privacy settings possible
- **Easy configuration**: Simple settings, no complex rules

### **Maintainability**
- **Smaller codebase**: Remove complex, unused security features
- **Clear architecture**: Well-defined components and responsibilities
- **Easier testing**: Focused functionality is easier to test

---

## 📝 **Documentation Updates Needed**

1. **User Guide**: How to use private mode and security settings
2. **Developer Guide**: How to integrate with new security framework
3. **API Documentation**: New security-related APIs and interfaces
4. **Migration Guide**: How to transition from old security system

---

**🎉 This new security framework focuses on what matters: user privacy and data sharing transparency, while leaving system-level security to the OS and antivirus software where it belongs.**
