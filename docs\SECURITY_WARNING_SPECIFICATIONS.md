# 🚨 Security Warning System Specifications

## 📋 **Overview**

This document specifies the exact warning messages, UI components, and behavior for ChatLo's security gateway system with smart pattern detection and plugin override capabilities.

---

## 🧠 **Smart Pattern Detection**

### **Context-Aware Sensitive Patterns**
```typescript
const SENSITIVE_PATTERNS = {
  // Personal Identifiable Information (PII)
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
  ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
  creditCard: /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,

  // Legal & Contract Headers (NEW - Highly Sensitive)
  contractTitle: /\b(?:CONFIDENTIAL|PROPRIETARY|NON-DISCLOSURE|AGREEMENT|CONTRACT)\s+(?:AGREEMENT|CONTRACT|DOCUMENT)/gi,
  legalHeader: /\b(?:ATTORNEY-CLIENT|PRIVILEGED|WORK PRODUCT|CONFIDENTIAL COMMUNICATION)/gi,

  // Business Sensitive (Smart - excludes footers)
  proprietary: /\b(?:proprietary|confidential|internal|restricted)\b(?!\s*(?:footer|disclaimer|notice))/gi,
  patent: /\b(?:patent|Patent)\s*(?:no\.?|number|#)\s*[\d,\-\/]+/gi,
  nda: /\b(?:NDA|non-disclosure|confidentiality agreement)\b/gi,

  // Financial Information
  bankAccount: /\b\d{8,17}\b/g,
  routingNumber: /\b\d{9}\b/g,

  // Personal Information
  address: /\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)\b/gi,
  dob: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g
}

// Smart Exclusions - Don't flag common footers/disclaimers
const EXCLUDED_PATTERNS = {
  commonFooter: /\b(?:this\s+(?:email|message|communication)\s+is\s+confidential|confidential\s+and\s+proprietary|for\s+internal\s+use\s+only)/gi,
  legalDisclaimer: /\b(?:disclaimer|notice|warning):\s*(?:confidential|proprietary)/gi,
  copyrightFooter: /\b(?:copyright|©)\s*\d{4}.*(?:all\s+rights\s+reserved|company\s+name)/gi,
  emailSignature: /\b(?:confidential|proprietary).*(?:signature|footer|disclaimer)/gi
}
```

### **Smart Detection Logic**
```typescript
class SmartPatternDetector {
  detectSensitivePatterns(content: string): DetectedPattern[] {
    const patterns: DetectedPattern[] = []

    // First, check for excluded patterns (footers/disclaimers)
    const excludedMatches = this.findExcludedPatterns(content)

    // Then detect sensitive patterns, excluding footer areas
    for (const [type, regex] of Object.entries(SENSITIVE_PATTERNS)) {
      const matches = content.match(regex)
      if (matches) {
        // Filter out matches that are in excluded areas
        const validMatches = matches.filter(match =>
          !this.isInExcludedArea(match, content, excludedMatches)
        )

        if (validMatches.length > 0) {
          patterns.push({
            type,
            matches: validMatches.map(m => this.maskSensitiveValue(m, type)),
            severity: this.getPatternSeverity(type)
          })
        }
      }
    }

    return patterns
  }

  private isInExcludedArea(match: string, content: string, excludedAreas: ExcludedArea[]): boolean {
    const matchIndex = content.indexOf(match)

    return excludedAreas.some(area =>
      matchIndex >= area.start && matchIndex <= area.end
    )
  }
}
```

---

## 🔌 **Plugin Override Integration**

### **Plugin Pattern Loading**
```typescript
class SecurityPatternManager {
  private patterns: SecurityPatterns

  async initialize(): Promise<void> {
    // Load default patterns
    this.patterns = this.getDefaultPatterns()

    // Check for plugin overrides
    await this.loadPluginOverrides()
  }

  private async loadPluginOverrides(): Promise<void> {
    const overrideFile = path.join(process.cwd(), 'plugins/security-overrides.json')

    if (fs.existsSync(overrideFile)) {
      try {
        const override = JSON.parse(fs.readFileSync(overrideFile, 'utf8'))

        if (override.enabled) {
          // Merge plugin patterns
          this.patterns.sensitivePatterns = {
            ...this.patterns.sensitivePatterns,
            ...this.convertStringPatternsToRegex(override.overrides.sensitivePatterns)
          }

          this.patterns.excludedPatterns = {
            ...this.patterns.excludedPatterns,
            ...this.convertStringPatternsToRegex(override.overrides.excludedPatterns)
          }

          console.log(`[Security] Plugin override loaded: ${override.pluginName}`)
        }
      } catch (error) {
        console.warn('[Security] Plugin override failed:', error.message)
      }
    }
  }

  private convertStringPatternsToRegex(patterns: Record<string, string>): Record<string, RegExp> {
    const regexPatterns: Record<string, RegExp> = {}

    for (const [key, pattern] of Object.entries(patterns)) {
      try {
        // Extract regex pattern and flags from string like "/pattern/gi"
        const match = pattern.match(/^\/(.+)\/([gimuy]*)$/)
        if (match) {
          regexPatterns[key] = new RegExp(match[1], match[2])
        } else {
          regexPatterns[key] = new RegExp(pattern, 'gi')
        }
      } catch (error) {
        console.warn(`[Security] Invalid pattern ${key}: ${pattern}`)
      }
    }

    return regexPatterns
  }
}
