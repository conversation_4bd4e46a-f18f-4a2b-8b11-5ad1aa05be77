<!-- Appendix added: clarify model update services and path bounds -->

# ChatLo Unified IPC System - System Design & Architecture (V03)

## What’s New in V03
- Confirms a pragmatic, local-first roadmap focused on fast delivery of end-user value before advanced indexing/embeddings.
- Prioritizes complete file-type extraction in `FilePageOverlay` and keeps current chat text-parsing flow intact.
- Defers unified data I/O spec and `master.md` presentation until all file+chat+artifacts paths are stable.
- Introduces staged E5 embeddings and optional ANN (SQLite‑VSS/Qdrant-lite) only after presentation phase.
- Defines “Think Phase” MVP where `master.md` consolidates all sources for local reasoning.

References: `docs/SYSTEM_DESIGN_ARCHITECTURE.md` (base), `docs/SYSTEM_DESIGN_ARCHITECTURE_V02.md` (unified IPC, intelligence storage, events), `docs/SEARCH_INDEX_EMBEDDING_REFEREENCE.md` (indexing/embedding plan).

## Summary of Current Maturity (carried from V02, updated)
- IPC, DB schema, and chat streaming are stable. File processing uses kernel-side plugin processors.
- Gaps intentionally accepted in V03 scope: semantic retrieval, unified intel I/O, vector indexes (to be addressed post-presentation phase).

## Chosen Roadmap (user-preferred)
1) Complete all file-type capability in `FilePageOverlay` to extract data.
2) Enable “send file to chat” data flow, continuing the current text-parsing inclusion approach.
3) After file types + chat + artifacts are complete, unify data collection I/O spec and compose a basic `master.md` (Presentation Phase).
4) Integrate E5 embeddings to enable semantic and full-text reasoning; ANN remains optional.
5) MVP: Completed `master.md` becomes the place to consolidate all data sources to think (Think Phase).

## Design Changes from V02 → V03
- Keep `.intelligence` as canonical intelligence store; postpone introducing vector stores until Step 4.
- Maintain prompt assembly using parsed text snippets (no vectors sent externally); improve extraction coverage first.
- Introduce `master.md` as the presentation-layer consolidation target after I/O unification.
- Position embeddings as an add-on to existing flows rather than a replacement of them.

## Data Flow Stories (updated)

### Files Page + FilePageOverlay (extract-first)
- Detect file open → `files:processFile(path, typeHint)` via IPC.
- Kernel file-processor plugin extracts normalized text/metadata → renderer updates overlay.
- Overlay reads/writes per-file intelligence via `intelligence:*` (JSON stored under `.intelligence`).
- Events: `intelligence:updated`, `task:progress` refresh overlay without reloads.

### Chat with Files (current text-parsing path retained)
- User sends message with attachments/@refs.
- Store builds file context by fetching `extracted_content` (process on-demand if missing).
- Compose role-based messages; send to local model or OpenRouter (non-private mode).
- Stream response → persist assistant message to DB.
- No vector usage required in this phase; snippets only.

### Artifacts
- Code/markdown/web-link artifacts already detected during chat streaming and file viewing.
- Artifacts are indexed for display and can be included in prompts as lightweight snippets.

### Presentation Phase: `master.md` (after I/O unification)
- Consolidate per-source summaries and labels into `master.md`:
  - Sections: Files, Chats, Artifacts, Labels/Notes, Key Ideas, Open Questions.
- Each section draws from canonical `.intelligence` and structured DB content.
- `master.md` is regenerated or incrementally updated on demand; lives inside the vault. Master.md is not actually a single markdown, but a json composition into a tabbed-page.  Tab design would be determined later.  Key purpose would be a homepage of context.  First tab is for recent activity summary, then it's a blank canvas for displaying prompting result from sidecar. Another tab would be linear timeline to seeing documents, note and chat sequences with simple filter by type.

### Think Phase (post E5 integration)
- Retrieval uses hybrid search (FTS shortlist → E5 vectors for re-rank; ANN optional) to assemble focused context for reasoning.
- Generated insights appended back to `.intelligence` and reflected in `master.md`.

## Module Roles (no breaking changes now)
- File Processing (kernel plugin): Extracts text/metadata for supported types (PDF/Word/Excel/Images/Markdown/Text/Code).
- IntelligenceCoreService: Persists/reads canonical JSON under `.intelligence`; emits update events.
- EventBus: `file:*`, `intelligence:updated`, `task:progress` power live UI.
- UnifiedAPIClient: Renderer’s typed IPC gate; renderer holds no FS logic.

## Unified Data I/O Spec (planned consolidation)
- Single schema for chunked text, labels, annotations, and links across sources (files/chats/artifacts/notes).
- Backed by `.intelligence` JSON per file and normalized DB records for chats/artifacts.
- `master.md` is a read model built from the unified I/O spec.

## Search/Index Roadmap (size-sensitive)
- Phase A (now): FTS-only search over chunk text; no embeddings required.
- Phase B (opt-in): E5 multilingual embeddings (INT8 by default) in `<vault>/.index/vectors.db`; hybrid retrieval; ANN optional in `<vault>/.index/sqlite-vss.db`.
- Phase C: CLI/IDE assistant can reuse the same indexes for repo semantics.

## Privacy & Data Sharing
- Private by default; only curated text leaves device in non-private chat. Vectors, `.intelligence`, paths, and IDs never leave.
- Ephemeral attachments stay session-scoped unless user adds to vault.

## Acceptance Criteria (per step)
1) FilePageOverlay extraction: All target file types produce normalized text + metadata; overlay updates live; `.intelligence` writes verified.
2) Chat-flow with files: On-demand extraction; prompt assembly stable; streaming/persistence reliable.
3) Unified I/O + `master.md`: A basic, regenerable `master.md` renders consolidated sections without errors; reflects latest labels/notes.
4) E5 integration: Embedding queue works; hybrid retrieval improves hit quality; no regressions in latency for small vaults.
5) Think Phase MVP: `master.md` supports reasoning tasks; added insights persist back into `.intelligence`.

## Risks & Mitigations
- Extraction variance across file types → Standardize plugin outputs; add smoke tests per type.
- Latency during large extractions → Debounce/coalesce; background tasks with progress events.
- Index bloat when adding vectors → INT8 by default; per-vault opt-in; disposable ANN caches.
- Schema drift → Single source of truth in `src/types/index.ts`; strict TypeScript checks.

## Day-to-Day Plan to Aug-20 (target launch)

Assumes start date: Aug-14.

- Aug-14 (Thu)
  - Audit file-type coverage in `FilePageOverlay` and kernel processors.
  - Close gaps for Markdown, PDF (including OCR toggle), common Office docs, and images.
  - Add event-driven refresh and minimal telemetry for extraction progress.

- Aug-15 (Fri)
  - Finish extraction edge cases (encodings, large files, timeouts).
  - Validate `.intelligence` writes via `IntelligenceCoreService`; add corruption checks.
  - Smoke tests: open → extract → update overlay across all types.

- Aug-16 (Sat)
  - Wire “send file to chat” flows using current text-parsing; ensure on-demand extraction and prompt assembly.
  - Stream responses; persist; handle errors. Add user notifications for extraction readiness.

- Aug-17 (Sun)
  - Unify I/O spec draft and implement adapters for files/chats/artifacts/notes.
  - Generate first `master.md` (Presentation Phase) with sections and anchors.
  - Validate regeneration without duplication; add basic navigation from overlay.

- Aug-18 (Mon)

  - E5 integration (local-only): background embedding queue; INT8 storage; hybrid retrieval (FTS shortlist + cosine).
  - Feature flag per-vault; no ANN by default. Measure latency/quality on sample vaults.

- Aug-19 (Tue)
  - “Think Phase” MVP: reading from `master.md` + retrieval to drive reasoning prompts.
  - Feedback loop: new insights saved to `.intelligence`; reflected back to `master.md`.
  - Polish UX: statuses, errors, retry, and progress events.

- Aug-20 (Wed)
  - Final stabilization: docs, acceptance tests, packaging toggles (private/non-private indicators).
  - Ship V03.

## Deliverables
- Completed extraction across file types in `FilePageOverlay` with live updates.
- Stable chat file flow using current text parsing.
- Unified I/O spec and initial `master.md` generator.
- E5-based hybrid retrieval behind a feature flag.
- Think Phase MVP where `master.md` becomes the consolidation hub for reasoning.

## Appendix 1: Key Files
- `electron/core/IntelligenceCoreService.ts` — `.intelligence` read/write.
- `electron/fileProcessors/*` — file extraction plugins.
- `src/components/FilePageOverlay.tsx` — overlay and intel interactions.
- `src/store/index.ts` — chat send/stream flows and prompt assembly.
- `docs/SEARCH_INDEX_EMBEDDING_REFEREENCE.md` — detailed indexing/embedding reference.

---
## Appendix 2: Portable Mode strategy
- Portable Mode strategy
  - App supports “data on USB” today for vault/.intelligence by pointing vault-root-path to USB.
  - To make chat history portable too, add configurable db-path under USB, gated by a new portable-mode-enabled setting.
  - Enforce path bounds so db-path and intelligence writes stay within chosen vault/portable roots.

- Kernel DB path configurability (design)
  - DatabaseManager to accept a user-selected db-path when portable mode is enabled.
  - On startup: if portable mode, open DB at that path; else use userData.
  - Validate db-path is under allowed portable root; fail safely with user prompt if not mounted.

- Safe connect/disconnect flow (design)
  - “Prepare for Disconnect” action to flush and close DB, then show “Ready to eject.”
  - “Connect Portable DB” action to re-open at usb db-path, or offer “Use local fallback this session.”
  - No background detection; only user-triggered actions.

- Renderer wiring and minimal UI hooks (proposal, no code)
  - Settings additions (exact UI pending your approval):
    - Toggle: Portable Mode
    - Selectors: Vault Root (already exists), Portable DB Path (defaults to <vaultRoot>/.chatlo-core/chatlo.db)
    - Buttons: Prepare for Disconnect, Connect Portable DB
  - Non-intrusive notifications using existing toast/banners (no redesign):
    - “Portable DB not available; click Connect after mounting”
    - “Safe to eject”

## Short Answer

- Yes, we should plan explicitly for USB/portable usage. It is central to “local-first, privacy-first.”
- Today the SQLite DB lives in app data by default; vault files and .intelligence are already user‑selectable and can be pointed to a USB folder. We need to make the DB path configurable as well if you want truly portable chat history and model metadata on the USB.
- We will design this with user-triggered flows (no background scanning), safe eject preparation, and OS‑safe path bounds.

Below is a careful, step-by-step plan. I won’t implement UI or code changes until you approve.

## Current State (DB and Storage)

- Database location
  - Dev: ../chatlo-dev.db
  - Prod: app.getPath('userData')/chatlo.db
  - It is not currently configurable by the user. Settings table exists; vault root path is configurable and used by file system flows.

- Vault root path
  - Renderer can set vault-root-path via files:setVaultRootPath. This can be a USB directory, so the vault content and .intelligence JSON can already live on USB.

- Intelligence store
  - Kernel writes canonical JSON per file to <vault>/.intelligence/files/<hash>.json
  - This is already compatible with portable usage when the vault root is on USB.

- Risk
  - Chat DB is separate from vault. If vault is on USB but DB is local, you won’t have a fully portable experience. We need an opt-in “Portable Mode” where DB file moves to USB as well.

## Proposed Strategy: Portable Mode (USB)

Design goals:
- User-controlled, no background polling: USB handling occurs only on explicit user actions.
- Cross-OS compatibility: use kernel path APIs and settings, not hardcoded paths.
- Safe eject: provide a “Prepare for Disconnect” action that flushes/close DB connections and persists state.
- Boundaries: only allow DB/vault paths within user-selected portable root; prevent path traversal.

### 1) Settings and Modes

- New explicit toggle: “Portable Mode”
  - When ON: require selecting a portable root (e.g., the same vault root on a USB drive).
  - DB path: <portableRoot>/.chatlo-core/chatlo.db (configurable beneath that root)
  - Intelligence remains under the selected vault root as today.

- Settings keys:
  - vault-root-path (already exists)
  - portable-mode-enabled: boolean
  - db-path: string (only used when portable mode is on; must be inside the selected portable root)
  - last-known-portable-root: string

- Guard rails:
  - When enabling Portable Mode, validate db-path is in the same device subtree as vault-root-path (or allow setting a dedicated DB location under the same removable drive).
  - Reject paths not on the selected drive.

### 2) Kernel DB Path Configurability

- Add support in DatabaseManager to:
  - Initialize DB at a user-provided path (from settings).
  - Close and re-open DB safely when the path changes (manual user trigger).
  - Ensure all migrations and integrity checks work for the portable DB path.

- Settings→DB wiring:
  - On app start: read portable-mode-enabled. If true, read db-path; if missing, derive default at <vault-root>/.chatlo-core/chatlo.db; ensure directories exist.
  - If portable mode is off: fallback to app userData path.

- Bounds and safety:
  - Before opening DB: verify db-path is under allowed root when portable mode is on.
  - If the path is unavailable (USB not mounted), do not block UI:
    - Show a non-blocking banner “Portable DB not available; running in read-only or local fallback mode” with a “Reconnect” button.
    - Do NOT silently create a new empty DB at userData unless the user selects “Use local fallback for this session.”

### 3) Safe Connect/Disconnect UX (User-triggered, no background polling)

- Provide explicit actions:
  - “Prepare for Disconnect” (in Settings or Vault/Portable section)
    - Flush DB, close connection, sync cache to disk, then show “Ready to eject.”
  - “Connect Portable DB” (after user plugs in device and clicks)
    - Attempt to open DB at db-path; if succeeds, resume normal operations.
    - If fails, display the exact error and options: “Retry” or “Use Local Fallback This Session.”

- Important: No auto-detection. We won’t watch drives or mount points in the background to comply with Just-in-Time Intelligence (user signals only).

### 4) Data Layout and Portability

- Vault/Intelligence
  - Already portable when vault-root-path is on USB. All .intelligence files live there.

- Chat DB
  - With portable mode, chat history, pinned intelligence pointers, and settings live on USB in the configured db-path.

- Path enforcement
  - Harden PathResolver.inferVaultPath to bound search within known vault roots (optional but recommended for privacy-first).
  - Kernel ensures any intelligence writes/reads are within the configured vault root.

### 5) Migration Flow

- Guided migration:
  - Wizard in Settings:
    - Step 1: Choose vault root (USB).
    - Step 2: Enable Portable Mode.
    - Step 3: Choose DB location (default suggested under portable root).
    - Step 4: “Migrate Now” (copies existing local DB → portable DB, verifies integrity).
    - Step 5: “Switch” (closes local DB, opens portable DB).
  - Migration uses a one-time copy; verify schema; keep a backup of original DB.

- Reversible:
  - Provide “Disable Portable Mode” that safely closes portable DB and reopens local DB in userData.

### 6) Cross-OS Considerations

- Windows/macOS/Linux:
  - Always use PathResolver and IPC path:join in kernel and renderer.
  - Avoid raw string concatenation in renderer for any storage paths.

- Removable drive letters and mounts:
  - Do not rely on drive letters. Store absolute path picked by user.
  - We do not scan for the device; we only attempt to open the path on user request.

### 7) Testing and Validation

- Unit/integration tests:
  - DB open at userData, then at custom db-path; simulate close/open cycle.
  - Migration path: create small local DB, migrate to portable path, then verify tables and counts.
  - Intelligence write/read remains unaffected by DB path change.

- Manual smoke:
  - Select a USB folder as vault root and enable Portable Mode with DB under the same root. Create a conversation, add messages/files, close app, unplug device (after “Prepare for Disconnect”), replug, “Connect Portable DB,” and verify continuity.

### 8) No Background Processing

- All intelligence and DB operations triggered by explicit user actions:
  - Opening files, sending messages, enabling Portable Mode, connecting/disconnecting.
  - No auto-indexing or scanning when devices change state.

## Requested Approvals

I will not change UI or code yet. If you approve:

- Phase 1 (Design-only changes + minimal kernel safeguards)
  - Add path bounds in PathResolver.inferVaultPath to the selected vault root(s).
  - Draft DB path configurability in DatabaseManager (design PR doc, no code changes).
  - Document the Portable Mode keys and flows.

- Phase 2 (Implementation with tests; UI hooks pending your review)
  - Implement DatabaseManager with configurable path and explicit open/close methods.
  - Implement settings endpoints to set/get db-path and portable-mode-enabled.
  - Add a non-intrusive UI hook proposal in Settings (exact style pending your approval).

---
