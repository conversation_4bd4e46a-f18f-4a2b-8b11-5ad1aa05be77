# V03 Appendix

- Path bounds: All path operations must use vaultPath utilities; db-path must be under vault root. Kernel now guards db-path.
- Model updates: modelUpdateLogic.ts (store loader) vs modelUpdateService.ts (cron-like JSON service) are separate pipelines as per preservation.md.
- Event-driven refresh: renderer listens to 'intelligence:updated' and triggers a lightweight refresh (no UI redesign).

