# FilePageOverlay Refactoring Plan

## Overview

The FilePageOverlay component has been analyzed and annotated with clear architectural comments. This document outlines the step-by-step refactoring plan to extract components incrementally without breaking functionality.

**Updated**: Based on comprehensive parallel analysis of plugin architecture, renderer registry, and data flow patterns.

## Current State Analysis

- **Total Lines**: 1,505+ lines (monolithic component)
- **Responsibilities**: File viewing, intelligence processing, annotation management, multiple file type rendering
- **Issues**: Complex state management, IDE navigation difficulties, tight coupling, no extensibility

## 🚨 **Critical Architectural Gaps Identified**

### **1. Duplicated File Type Detection (High Risk)**
- **Three competing implementations** with inconsistent type unions
- `DocumentViewer` imports `detectFileType` but shadows it with local implementation
- Missing 'mermaid' type in some detectors, causing rendering failures
- **Impact**: Type detection drift, plugin matching failures, maintenance overhead

### **2. Split Extraction Flows (Data Integrity Risk)**
- Renderer plugins only handle display; extraction is fragmented
- Markdown uses "direct-read" bypassing Electron `MarkdownPlugin` metadata
- PDF/images use `processFile` but markdown frontmatter never reaches intelligence
- **Impact**: Incomplete intelligence data, inconsistent plugin behavior

### **3. Intelligence Ingestion Not Wired**
- `FilePageOverlay` logs `onContentLoad`/`onContentExtracted` but doesn't call `updateFileContent`
- Plugin metadata never propagates to `useFileIntelligence`
- **Impact**: Intelligence layer receives no plugin-extracted data

### **4. Dual Annotation Persistence (Race Condition)**
- `IntelligenceHub` uses `annotationStorageService`
- `FilePageOverlay.handleLabelsChanged` uses `intelligence:listSessions`
- **Impact**: Data races, annotation loss, divergent state

### **5. Markdown Security & Duplication**
- Two separate regex renderers using `dangerouslySetInnerHTML` without sanitization
- Styling/transform rules diverge across implementations
- **Impact**: Local XSS risk, maintenance burden

### **6. Inconsistent File Type Models**
- `FileViewerService`: `'unknown'`, `DocumentViewer`: `'unsupported'`, `FilePageOverlay`: includes `'mermaid'`
- **Impact**: Plugin matching failures, type system violations

## Refactoring Strategy

### **Phase 0: Foundation Fixes (CRITICAL - Must Complete First)**

**Target**: Resolve architectural gaps before component extraction

#### **0.1 Unify File Type Detection**
```typescript
// Create src/types/fileTypes.ts
export type FileType = 'pdf' | 'image' | 'markdown' | 'text' | 'code' | 'mermaid' | 'excel' | 'unknown'

export interface FileTypeInfo {
  type: FileType;
  extension: string;
  canExtract: boolean;
  canAnnotate: boolean;
  requiresProcessing: boolean;
}

// Create src/services/fileTypeRegistry.ts
export const detectFileType = (fileName: string, content?: string): FileTypeInfo
export const isMermaidContent = (content: string): boolean
```

**Actions**:
1. Create single source of truth for file type detection
2. Remove duplicate implementations in `FileViewerService`, `DocumentViewer`, `FilePageOverlay`
3. Fix import shadowing in `DocumentViewer.tsx`
4. Include 'mermaid' type in all detectors

#### **0.2 Consolidate Plugin Extraction Flow**
```typescript
// Extend renderer plugin registry
interface FileTypePlugin {
  canHandle(fileName: string): boolean;
  loadContent(filePath: string): Promise<string>;
  renderContent(content: string): React.ReactNode;
  extract?(filePath: string, content: string): Promise<{
    text: string;
    metadata: any;
  }>;
}
```

**Actions**:
1. Route ALL extraction through plugin pathway
2. For Electron processors (pdf, image, markdown): call `electronAPI.files.processFile`
3. For text/code: normalize metadata structure
4. Ensure frontmatter and stats reach intelligence layer

#### **0.3 Wire Intelligence Ingestion**
```typescript
// In FilePageOverlay
const handleContentLoad = (content: string) => {
  updateFileContent(content);
};

const handleContentExtracted = (text: string, metadata: any) => {
  updateFileContent(text);
  updateIntelligence({ fileMetadata: metadata });
};
```

**Actions**:
1. Connect `onContentLoad` → `updateFileContent`
2. Connect `onContentExtracted` → `updateFileContent` + `updateIntelligence`
3. Ensure plugin metadata flows to intelligence layer

#### **0.4 Unify Annotation Persistence**
**Actions**:
1. Use `annotationStorageService` everywhere
2. Remove `intelligence:listSessions` merge in `FilePageOverlay.handleLabelsChanged`
3. Eliminate race conditions between storage paths

#### **0.5 Secure Markdown Rendering**
**Actions**:
1. Keep single `MarkdownRenderer` under `FileTypeRenderer`
2. Have `MarkdownArtifactViewer` import and reuse it
3. Add basic HTML escaping before regex transforms

### Phase 1: Extract DocumentViewer Component (LEFT PANEL)

**Target**: Lines 1413-1510 (Document viewer section)

**API Design** (Updated):
```typescript
interface DocumentViewerProps {
  filePath: string;
  content: string;
  fileType: FileTypeInfo; // From unified type system
  isLoading: boolean;
  onContentLoad: (content: string) => void; // NEW: Wire to intelligence
  onContentExtracted: (text: string, metadata: any) => void; // NEW: Wire to intelligence
  onClose: () => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ ... }) => {
  // File header, content rendering, zoom controls
  // Uses unified file type detection
  // Routes extraction through plugin system
};
```

**Extraction Steps** (Updated):
1. Create `src/components/DocumentViewer.tsx`
2. Use unified file type detection from registry
3. Move content loading functions (lines 556-751) with plugin integration
4. Move file rendering functions (lines 805-1066)
5. Wire extraction hooks to parent component
6. Update FilePageOverlay to use DocumentViewer with proper event handlers

### Phase 2: Extract IntelligenceHub Component (RIGHT PANEL)

**Target**: Lines 1511+ (Intelligence panel section)

**API Design** (Updated):
```typescript
interface IntelligenceHubProps {
  intelligenceData: FileIntelligenceData;
  onDataChange: (data: FileIntelligenceData) => void;
  onClose: () => void; // NEW: Consistent close behavior
}

const IntelligenceHub: React.FC<IntelligenceHubProps> = ({ ... }) => {
  // Smart labeling interface, annotation display, summary
  // Uses unified annotationStorageService
  // Consistent close behavior via onClose prop
};
```

**Extraction Steps** (Updated):
1. Create `src/components/IntelligenceHub.tsx`
2. Move SmartLabelingInterface integration
3. Move annotation management (lines 1067-1215) using unified storage
4. Move intelligence utilities (lines 220-255)
5. Replace `window.history.back()` with `onClose()` prop
6. Update FilePageOverlay to use IntelligenceHub with proper handlers

### Phase 3: Create FileIntelligenceService (DATA LAYER)

**Target**: Centralize data management with plugin metadata integration

**API Design** (Updated):
```typescript
interface FileIntelligenceData {
  file: {
    path: string;
    type: FileType; // From unified type system
    content: string;
  };
  intelligence: {
    keyIdeas: KeyIdea[];
    annotations: SmartAnnotationNote[];
    summary?: string;
    fileIntelligence?: FileIntelligence; // From plugin processing
  };
  ui: {
    selectedLabels: string[];
    currentAnnotation: number;
  };
  metadata?: {
    lastUpdated?: string;
    processingConfidence?: number;
    fileMetadata?: any; // Plugin-extracted metadata (frontmatter, stats, etc.)
  };
}

const useFileIntelligence = (filePath: string) => {
  // Unified data management hook
  // Integrates plugin metadata
  // Single source of truth for intelligence data
  return {
    data: FileIntelligenceData;
    updateFileContent: (content: string) => void;
    updateIntelligence: (intelligence: Partial<FileIntelligenceData['intelligence']>) => void;
    updateMetadata: (metadata: any) => void;
    // ... other methods
  };
};
```

### Phase 4: Enhance FileTypeRenderer Plugin System

**Target**: Extend existing plugin system with extraction capabilities

**API Design** (Updated):
```typescript
interface FileTypePlugin {
  canHandle(fileName: string): boolean;
  loadContent(filePath: string): Promise<string>;
  renderContent(content: string): React.ReactNode;
  extract?(filePath: string, content: string): Promise<{
    text: string;
    metadata: any;
  }>; // NEW: Bridge to Electron processors
}

const FileTypeRenderer: React.FC<{
  fileType: FileType; // From unified type system
  content: string;
  options?: any;
}> = ({ ... }) => {
  // Enhanced plugin-based rendering system
  // Bridges to Electron file processors for extraction
};

// NEW: Registry query API
interface FileTypeRegistry {
  listPlugins(): FileTypePlugin[];
  getPlugin(fileType: FileType): FileTypePlugin | null;
  registerPlugin(fileType: FileType, plugin: FileTypePlugin): void;
}
```

## Implementation Approach

### **CRITICAL: Phase 0 Must Complete First**

**Why Phase 0 is Essential**:
- Architectural gaps create data integrity risks
- Duplicate implementations cause maintenance overhead
- Plugin metadata never reaches intelligence layer
- Race conditions in annotation storage

**Phase 0 Success Criteria**:
- [ ] Single file type detection source (no duplicates)
- [ ] All extraction routes through plugin system
- [ ] Plugin metadata flows to intelligence layer
- [ ] Single annotation storage path
- [ ] Secure markdown rendering

### Step-by-Step Process (Updated)

1. **Phase 0: Foundation Fixes** (CRITICAL)
   - Fix architectural gaps before component extraction
   - Ensure data integrity and plugin consistency
   - Verify intelligence ingestion works end-to-end

2. **Extract One Component**: Create new component file
3. **Test in Isolation**: Ensure component works independently
4. **Replace in FilePageOverlay**: Update main component to use extracted component
5. **Verify Functionality**: Test that all features still work
6. **Repeat**: Move to next component

### Benefits of This Approach

- **No Breaking Changes**: Functionality remains intact throughout
- **Incremental Progress**: Each step is a complete, testable unit
- **Clear Boundaries**: Components have well-defined responsibilities
- **IDE Friendly**: Smaller files are easier to navigate and edit
- **Data Integrity**: Plugin metadata flows correctly to intelligence
- **Security**: Unified markdown rendering with sanitization

## API Endpoints to Build

### **0. Foundation APIs (Phase 0)**
```typescript
// Unified file type detection
const detectFileType = (fileName: string, content?: string): FileTypeInfo;
const isMermaidContent = (content: string): boolean;

// Plugin extraction bridge
interface PluginExtractionResult {
  text: string;
  metadata: {
    frontmatter?: any;
    stats?: any;
    processingConfidence?: number;
    [key: string]: any;
  };
}

const extractViaPlugin = (filePath: string, fileType: FileType): Promise<PluginExtractionResult>;
```

### 1. DocumentViewer APIs (Updated)
```typescript
// File content loading with plugin integration
const useFileContent = (filePath: string, fileType: FileType) => {
  // Returns: { content, isLoading, error, metadata }
};

// File type detection (unified)
const useFileTypeDetection = (fileName: string, content?: string) => {
  // Returns: { fileType, canExtract, canAnnotate, requiresProcessing }
};

// Content extraction for intelligence
const useContentExtraction = (filePath: string, fileType: FileType) => {
  // Returns: { extractedText, metadata, isExtracting, error }
};
```

### 2. IntelligenceHub APIs (Updated)
```typescript
// Intelligence data management with plugin metadata
const useFileIntelligence = (filePath: string) => {
  // Returns: { intelligence, updateIntelligence, updateMetadata, isLoading }
};

// Unified annotation management
const useAnnotations = (filePath: string) => {
  // Returns: { annotations, addAnnotation, editAnnotation }
  // Uses annotationStorageService exclusively
};
```

### 3. FileTypeRenderer APIs (Enhanced)
```typescript
// Plugin registration with extraction
const registerFileTypePlugin = (type: FileType, plugin: FileTypePlugin) => {
  // Register new file type handler with extraction capability
};

// Renderer factory with extraction bridge
const createFileTypeRenderer = (fileType: FileType) => {
  // Returns appropriate renderer component with extraction support
};

// Registry query API
const listFileTypePlugins = (): FileTypePlugin[];
const getFileTypePlugin = (fileType: FileType): FileTypePlugin | null;
```

## Next Steps (Updated Priority)

### **IMMEDIATE: Phase 0 Foundation Fixes**
1. **Create unified file type system** (`src/types/fileTypes.ts`, `src/services/fileTypeRegistry.ts`)
2. **Fix import shadowing** in `DocumentViewer.tsx` (remove local `detectFileType`)
3. **Wire intelligence ingestion** in `FilePageOverlay` (`onContentLoad` → `updateFileContent`)
4. **Unify annotation storage** (remove `intelligence:listSessions` path)
5. **Secure markdown rendering** (single renderer with HTML escaping)

### **Phase 0 Success Verification**
- [ ] Console shows plugin metadata reaching intelligence layer
- [ ] No duplicate file type detection functions
- [ ] Markdown frontmatter appears in intelligence data
- [ ] Annotations persist through single storage path
- [ ] No XSS warnings in markdown rendering

### **THEN: Component Extraction**
1. **Extract DocumentViewer**: With unified types and proper event wiring
2. **Extract IntelligenceHub**: With unified annotation storage
3. **Create Component Boundaries**: Use clear props interfaces for communication
4. **Maintain State Flow**: Ensure data flows correctly between components
5. **Test Incrementally**: Verify each extraction works before proceeding

## 🎯 **Success Metrics**

### **Phase 0 Completion**
- Zero duplicate file type detection implementations
- 100% plugin metadata reaching intelligence layer
- Single annotation persistence path
- Secure markdown rendering without XSS risk

### **Full Refactor Completion**
- FilePageOverlay < 500 lines (orchestration only)
- DocumentViewer < 400 lines (display + extraction hooks)
- IntelligenceHub < 300 lines (intelligence UI)
- All file types route through unified plugin system
- External plugins can be added without core changes

This approach will transform the monolithic FilePageOverlay into a maintainable, extensible system while **first ensuring data integrity and plugin consistency**.
