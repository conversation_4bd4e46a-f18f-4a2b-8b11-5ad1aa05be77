echo to @pre-draft-plugin-design.md

Okay, I'll trace through these three intelligence services to understand their data flows, what they're doing, and the potential impact if they break after fixing the compliance issues. Let me examine each service systematically.


[1 tool called]

Now let me examine the unifiedIntelligenceService:


[1 tool called]

Now let me examine the documentIntelligenceService:


[1 tool called]

Now let me check what data these services are actually saving by looking at the storage patterns:


[1 tool called]

Now I have a comprehensive understanding of these three intelligence services. Let me provide you with the complete data flow analysis and story.

## 🧠 **Intelligence Services Data Flow Analysis: The Complete Story**

### 📊 **Executive Summary**
These three services form the **core intelligence processing pipeline** of ChatLo, handling everything from message analysis to document intelligence to unified data retrieval. They're the **brain** of the system, but they have critical compliance gaps that could break functionality after fixing.

---

## 🎯 **Service 1: IntelligenceService - The Message Intelligence Extractor**

### **What It Does:**
- **Primary Role**: Extracts intelligence from chat messages and user content
- **Trigger**: User actions (message pinning, text selection, manual requests)
- **Processing**: Hybrid approach (keyword + optional LLM enhancement)

### **Data Flow Story:**
```
User Pins Message → IntelligenceService.extractIntelligence() → 
Keyword Extraction → Confidence Assessment → Optional LLM Enhancement → 
Structured Data → Storage → Vault Master File Update
```

### **What It Saves:**
```typescript
// Core Intelligence Data
{
  entities: [
    { text: "React", type: "technology", confidence: 0.8 },
    { text: "API", type: "concept", confidence: 0.7 }
  ],
  topics: [
    { name: "Frontend Development", relevance: 0.9, keywords: ["react", "api"] }
  ],
  artifacts: [
    { type: "code", title: "Code snippet 1 (JavaScript)" },
    { type: "link", title: "Link 1", description: "https://..." }
  ],
  summary: "Message about Frontend Development involving React, API (150 characters)"
}

// Processing Metadata
{
  extraction_time_ms: 45,
  model_used: "hybrid_llm_enhanced",
  processing_version: "1.1"
}
```

### **Storage Location:**
- **Primary**: Database via `window.electronAPI.db.updateMessageIntelligence()`
- **Secondary**: Vault master.md files via `updateVaultMasterFile()`
- **Format**: Structured JSON with confidence scores and metadata

---

## 🎯 **Service 2: UnifiedIntelligenceService - The File Intelligence Orchestrator**

### **What It Does:**
- **Primary Role**: Orchestrates file intelligence processing with caching and queue management
- **Trigger**: File operations (upload, analysis requests, chat attachments)
- **Processing**: Intelligence-first approach (check existing → process if needed)

### **Data Flow Story:**
```
File Operation → Check Cache → Check Queue → Check Existing Intelligence → 
Process File (if needed) → Generate Basic Intelligence → Store → Return Unified Data
```

### **What It Saves:**
```typescript
// File Intelligence Data
{
  file_path: "/path/to/document.md",
  key_ideas: [
    { id: "idea_123", text: "Important concept here", relevance_score: 0.8, category: "extracted_content" }
  ],
  weighted_entities: [
    { text: "Technology", type: "UNKNOWN", confidence: 0.6, frequency: 3 }
  ],
  human_connections: [],
  processing_confidence: 0.7,
  analysis_metadata: {
    processing_time_ms: 100,
    model_used: "basic-extraction",
    timestamp: "2025-01-27T10:30:00Z",
    content_length: 2500,
    word_count: 450,
    sentence_count: 25,
    extraction_method: "keyword-based"
  }
}

// Processing Queue Management
{
  cacheKey: "filePath:vaultPath",
  processingQueue: Map<string, Promise<ProcessedIntelligence>>,
  cacheTTL: 30 minutes
}
```

### **Storage Location:**
- **Primary**: `<vault>/.intelligence/documents/<hash>/artifacts/artifacts.json`
- **Format**: Canonical JSON with processing metadata
- **Cache**: In-memory with 30-minute TTL

---

## 🎯 **Service 3: DocumentIntelligenceService - The Smart Annotation Processor**

### **What It Does:**
- **Primary Role**: Handles document analysis sessions and smart annotations
- **Trigger**: Document analysis requests, annotation creation
- **Processing**: AI-powered entity extraction and insight generation

### **Data Flow Story:**
```
Document Analysis Request → AI Model Integration → Entity Extraction → 
Insight Generation → Session Creation → Vault Storage → Entity Index Update
```

### **What It Saves:**
```typescript
// Document Intelligence Session
{
  session_id: "session_1706365800000_abc123def",
  timestamp: "2025-01-27T10:30:00Z",
  document: {
    hash: "sha256_hash_here",
    path: "/path/to/document.md",
    vault: "/vault/path"
  },
  intelligence_session: {
    session_type: "smart_annotation",
    ai_model: "gemma3-32k",
    processing_time_ms: 1500,
    confidence_score: 0.85,
    extracted_entities: [
      { entity: "API Design", type: "technical_concept", confidence: 0.9, user_selected: false }
    ],
    key_insights: [
      { insight: "Document covers multiple domains and concepts", importance: "high", confidence: 0.85 }
    ],
    content_summary: "Document contains 1200 words with key focus on: API Design, Security, Performance"
  },
  user_interactions: [],
  context_signals: {
    user_intent: "document_analysis",
    document_importance: "medium",
    workflow_stage: "analysis"
  }
}
```

### **Storage Location:**
- **Primary**: `<vault>/.intelligence/documents/<hash>/sessions/<session_id>.json`
- **Format**: Session-based JSON with full analysis context
- **Index**: Entity index for cross-document relationships

---

## 🚨 **Critical Compliance Gaps & Potential Breakage**

### **Gap 1: Missing User Action Correlation IDs**
**Current State**: None of these services track user action context
**Required by Design**: "Every entrypoint requires a `userAction` context (id, timestamp, type)"

**What Will Break:**
- **JIT Intelligence Enforcement**: System can't verify if intelligence processing was user-triggered
- **Audit Trail**: No way to track who/what triggered intelligence operations
- **Rate Limiting**: Can't implement per-user action limits

**Impact After Fixing:**
```typescript
// CURRENT (will break):
await intelligenceService.extractIntelligence(content)

// REQUIRED (after fixing):
await intelligenceService.extractIntelligence(content, {
  userAction: {
    id: "action_123",
    timestamp: "2025-01-27T10:30:00Z", 
    type: "message_pin"
  }
})
```

### **Gap 2: Inconsistent APIResponse Handling**
**Current State**: Mixed return types across services
**Required by Design**: "All Electron API endpoints should return `APIResponse<T>`"

**What Will Break:**
- **Frontend Components**: Expecting raw values but receiving envelopes
- **Error Handling**: Inconsistent error response formats
- **Type Safety**: TypeScript errors from mismatched return types

**Impact After Fixing:**
```typescript
// CURRENT (will break):
const result = await intelligenceService.extractIntelligence(content)
const entities = result.data.entities // ❌ result.data is undefined

// REQUIRED (after fixing):
const result = await intelligenceService.extractIntelligence(content)
const entities = result.data.entities // ✅ result is APIResponse<IntelligenceData>
```

### **Gap 3: Missing Vault Inference Guarantees**
**Current State**: Some services don't guarantee vault path inference
**Required by Design**: "Both `indexFile` and `processFile` must receive `inferredVault`"

**What Will Break:**
- **File Storage**: Intelligence data could be saved to wrong locations
- **Cross-Vault Contamination**: Data from one vault appearing in another
- **Path Resolution**: Broken deep links and file references

---

## �� **Potential System Breakage Scenarios**

### **Scenario 1: User Action Tracking Breaks Intelligence Processing**
```typescript
// If we add user action requirements without updating all callers:
// ❌ BREAKS: MessageBubble.tsx
await intelligenceService.extractIntelligence(content) // Missing userAction

// ❌ BREAKS: SmartAnnotationPanel.tsx  
await documentIntelligenceService.analyzeDocument(filePath, content) // Missing userAction

// ❌ BREAKS: InputArea.tsx
await unifiedIntelligenceService.getIntelligence(fileInfo) // Missing userAction
```

**Impact**: **100% of intelligence features stop working**

### **Scenario 2: APIResponse Envelope Breaks Frontend Components**
```typescript
// If we standardize return types without updating frontend:
// ❌ BREAKS: IntelligenceHub.tsx
const intelligence = await intelligenceClient.read(filePath, vaultPath)
const keyIdeas = intelligence.key_ideas // ❌ intelligence is now APIResponse

// ❌ BREAKS: FilePageOverlay.tsx
const result = await fileAnalysisService.analyzeAndStoreDocument(content, filePath, vaultPath)
const ideas = result.key_ideas // ❌ result is now APIResponse
```

**Impact**: **UI components crash, intelligence display broken**

### **Scenario 3: Vault Inference Breaks File Storage**
```typescript
// If we enforce vault inference without updating services:
// ❌ BREAKS: UnifiedIntelligenceService
await intelligenceClient.write(filePath, vaultPath, { json: intelligence })
// ❌ Error: "vaultPath must be inferred, not provided"

// ❌ BREAKS: DocumentIntelligenceService  
await this.saveSession(session) // ❌ Error: "Cannot determine vault path"
```

**Impact**: **No intelligence data can be saved, complete data loss**

---

## 🛠️ **Required Fixes to Prevent Breakage**

### **Fix 1: Add User Action Context to All Services**
```typescript
// Update all service interfaces
interface IntelligenceRequest {
  content: string
  userAction: {
    id: string
    timestamp: string
    type: UserActionType
  }
  options?: ProcessingOptions
}

// Update all service methods
async extractIntelligence(request: IntelligenceRequest): Promise<APIResponse<IntelligenceData>>
```

### **Fix 2: Standardize APIResponse Returns**
```typescript
// Update all service methods to return consistent envelopes
async extractIntelligence(request: IntelligenceRequest): Promise<APIResponse<IntelligenceData>> {
  try {
    const result = await this.processIntelligence(request)
    return {
      success: true,
      data: result,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      code: 'EXTRACTION_FAILED',
      timestamp: Date.now()
    }
  }
}
```

### **Fix 3: Enforce Vault Inference**
```typescript
// Update all storage calls to use inferred vault paths
async saveIntelligence(filePath: string, data: any): Promise<APIResponse<boolean>> {
  const inferredVault = PathResolver.inferVaultPath(filePath, { allowedVaultRoots })
  if (!inferredVault) {
    return {
      success: false,
      error: "Cannot infer vault path",
      code: "VAULT_INFERENCE_FAILED"
    }
  }
  
  return await intelligenceClient.write(filePath, inferredVault, { json: data })
}
```

---

## 📊 **Breakage Risk Assessment**

| Service | User Action Risk | APIResponse Risk | Vault Inference Risk | Overall Risk |
|---------|------------------|------------------|---------------------|--------------|
| **IntelligenceService** | �� HIGH | 🟡 MEDIUM | 🟢 LOW | �� HIGH |
| **UnifiedIntelligenceService** | �� HIGH | �� MEDIUM | 🟡 MEDIUM | 🔴 HIGH |
| **DocumentIntelligenceService** | �� HIGH | �� MEDIUM | 🟡 MEDIUM | 🔴 HIGH |

**Overall Risk: 🔴 CRITICAL** - All three services will break if compliance fixes are implemented without proper migration.

---

## 🎯 **Recommended Migration Strategy**

### **Phase 1: Add User Action Context (Low Risk)**
- Add optional `userAction` parameter to all services
- Maintain backward compatibility
- Log warnings for missing user actions

### **Phase 2: Standardize APIResponse (Medium Risk)**
- Introduce new methods with `APIResponse` returns
- Deprecate old methods
- Provide migration helpers

### **Phase 3: Enforce Vault Inference (High Risk)**
- Update all storage calls
- Add comprehensive testing
- Implement rollback mechanisms

**Bottom Line**: These services are the **core intelligence backbone** of ChatLo. Fixing compliance issues without proper migration will cause **complete system failure**. The fixes must be implemented incrementally with comprehensive testing at each phase.