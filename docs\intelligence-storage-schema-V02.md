# Intelligence Storage Schema V02 - ACTUAL IMPLEMENTATION

## Overview
This document captures the **ACTUAL** intelligence collection storage schema currently implemented and in use in the ChatLo codebase. This is a snapshot of the existing schema for record and planning purposes, based on the real implementation rather than theoretical design.

## Current Implementation Status

### ✅ **IMPLEMENTED AND WORKING**
- File intelligence storage via `intelligence:write/read` API
- Smart annotation system with save-first architecture
- Context annotation service for chat-based insights
- Unified API client for intelligence operations
- File analysis service with fallback mechanisms

### ⚠️ **PARTIALLY IMPLEMENTED**
- Vault intelligence aggregation (temporarily disabled during migration)
- Session-based storage structure (defined but not fully utilized)
- Entity index and patterns (types defined, implementation pending)

### ❌ **NOT IMPLEMENTED**
- Master.md intelligence integration
- Learning data and user preferences storage
- Export functionality
- Cross-vault intelligence patterns

## ACTUAL STORAGE ARCHITECTURE

### Directory Structure (Current Implementation)
```
<vault_root>/
├── <context_name>/
│   ├── master.md                    # Basic vault metadata (no intelligence integration)
│   ├── documents/                   # User documents
│   ├── .vault/                     # Existing vault metadata
│   └── .intelligence/              # Intelligence data storage (ACTUALLY IMPLEMENTED)
│       ├── files/                  # Per-file intelligence (ACTUALLY IMPLEMENTED)
│       │   └── <file_hash>.json   # Single JSON per file (ACTUALLY IMPLEMENTED)
│       └── context-notes/          # Context annotations (ACTUALLY IMPLEMENTED)
│           ├── <conversation_id>.json
│           ├── file-<hash>.json    # File-specific context notes
│           └── global_context.json # Cross-conversation insights
```

## ACTUAL JSON SCHEMAS IN USE

### 1. File Intelligence JSON (`<vault>/.intelligence/files/<hash>.json`)

**ACTUAL IMPLEMENTATION** - This is what gets generated by `fileAnalysisService.ts`:

```json
{
  "file_path": "/path/to/document.pdf",
  "key_ideas": [
    {
      "id": "idea_uuid_123",
      "text": "React component library",
      "relevance_score": 95,
      "intent_types": ["topic", "knowledge"],
      "weight": 1.0,
      "auto_selected": true,
      "user_confirmed": false,
      "context": "Document contains extensive UI design specifications",
      "extracted_from": "Overview section"
    }
  ],
  "weighted_entities": [
    {
      "text": "UI Design",
      "type": "technical_concept",
      "confidence": 0.95,
      "weight": 1.0,
      "intent_types": ["topic", "knowledge"],
      "context": "Document contains extensive UI design specifications",
      "priority_level": "high"
    }
  ],
  "human_connections": [
    {
      "name": "John Doe",
      "email": "<EMAIL>",
      "company": "Design Corp",
      "title": "Senior Designer",
      "role": "Lead Designer",
      "connection_strength": 0.9,
      "collaboration_context": "Primary designer for this project",
      "document_mentions": 3,
      "priority_weight": 1.0
    }
  ],
  "processing_confidence": 0.87,
  "analysis_metadata": {
    "processing_time_ms": 1250,
    "model_used": "ollama:gemma3:latest",
    "timestamp": "01-15-2025, 14:30:25",
    "plugin_metadata": {
      "extraction_method": "pdf_parser",
      "stats": {
        "pages": 15,
        "words": 2500
      }
    }
  },
  "created_at": "01-15-2025, 14:30:25",
  "updated_at": "01-15-2025, 14:30:25",
  "smart_annotations": [
    {
      "id": "annotation_uuid_456",
      "type": "user",
      "title": "Design System Notes",
      "content": "This document outlines a comprehensive design system",
      "created_at": "2025-01-15T14:30:25.000Z",
      "updated_at": "2025-01-15T14:30:25.000Z",
      "labels_snapshot": [
        {
          "id": "idea_uuid_123",
          "text": "React component library",
          "relevance_score": 95,
          "intent_types": ["topic", "knowledge"],
          "weight": 1.0,
          "auto_selected": true,
          "user_confirmed": false
        }
      ],
      "ai_response": "This document provides a comprehensive overview of the React component library design system...",
      "has_ai_response": true,
      "note_number": 1
    }
  ]
}
```

### 2. Context Notes JSON (`<vault>/.intelligence/context-notes/<conversation_id>.json`)

**ACTUAL IMPLEMENTATION** - Generated by `contextAnnotationService.ts`:

```json
{
  "version": "1.0",
  "conversation_id": "conv-uuid-123",
  "vault_context": "design-system-vault",
  "created": "2025-01-15T14:30:25.000Z",
  "last_updated": "2025-01-15T14:30:25.000Z",
  "context_notes": [
    {
      "id": "context_note_uuid_789",
      "type": "general_context",
      "title": "Design System Principles",
      "content": "**Selected Context:**\nThe design system should prioritize consistency over novelty\n\n**User Notes:**\nThis principle applies across all UI components\n\n---\n*Source: General context annotation*",
      "selected_text": "The design system should prioritize consistency over novelty",
      "source_context": {
        "chat_message_id": "msg-uuid-456",
        "conversation_id": "conv-uuid-123",
        "timestamp": "2025-01-15T14:30:25.000Z",
        "user_input": "This principle applies across all UI components",
        "vault_context": "design-system-vault"
      },
      "metadata": {
        "tags": ["design-system", "principles", "consistency"],
        "importance": "high",
        "category": "design_insight",
        "related_concepts": ["UI consistency", "design tokens", "component library"],
        "confidence_score": 0.8
      },
      "created_at": "2025-01-15T14:30:25.000Z",
      "updated_at": "2025-01-15T14:30:25.000Z",
      "note_number": 1
    }
  ],
  "statistics": {
    "total_notes": 1,
    "last_note_created": "2025-01-15T14:30:25.000Z",
    "most_common_tags": ["design-system", "principles"]
  }
}
```

### 3. File-Specific Context Notes (`<vault>/.intelligence/context-notes/file-<hash>.json`)

**ACTUAL IMPLEMENTATION** - For file-specific annotations:

```json
{
  "version": "1.0",
  "conversation_id": "file-specific",
  "vault_context": "design-system-vault",
  "created": "2025-01-15T14:30:25.000Z",
  "last_updated": "2025-01-15T14:30:25.000Z",
  "context_notes": [
    {
      "id": "file_note_uuid_101",
      "type": "general_context",
      "title": "Component Library Architecture",
      "content": "**Selected Context:**\nThe component library should be modular and extensible\n\n**User Notes:**\nConsider plugin architecture for future extensions\n\n---\n*Source: File annotation from /design-system/components.md*",
      "selected_text": "The component library should be modular and extensible",
      "source_context": {
        "timestamp": "2025-01-15T14:30:25.000Z",
        "user_input": "Consider plugin architecture for future extensions",
        "vault_context": "design-system-vault",
        "file_path": "/design-system/components.md"
      },
      "metadata": {
        "tags": ["component-library", "architecture", "modularity"],
        "importance": "medium",
        "category": "technical_insight",
        "related_concepts": ["plugin-architecture", "extensibility", "modular-design"],
        "confidence_score": 0.7
      },
      "created_at": "2025-01-15T14:30:25.000Z",
      "updated_at": "2025-01-15T14:30:25.000Z",
      "note_number": 1
    }
  ],
  "statistics": {
    "total_notes": 1,
    "last_note_created": "2025-01-15T14:30:25.000Z",
    "most_common_tags": ["component-library", "architecture"]
  }
}
```

## HOW TO GENERATE

### 1. File Intelligence Generation

**Service**: `src/services/fileAnalysisService.ts`
**Method**: `analyzeAndStoreDocument()`

**Process**:
1. **Content Analysis**: Uses local models (preferred: `ollama:gemma3:latest`)
2. **Fallback**: Keyword-based extraction if AI analysis fails
3. **Storage**: Calls `intelligenceClient.write()` with JSON payload
4. **Location**: `<vault>/.intelligence/files/<file_hash>.json`

**Code Path**:
```typescript
// In fileAnalysisService.ts line 1190
await intelligenceClient.write(filePath, vaultPath, {
  json: {
    file_path: fileIntelligence.file_path,
    key_ideas: fileIntelligence.key_ideas,
    weighted_entities: fileIntelligence.weighted_entities,
    human_connections: fileIntelligence.human_connections,
    processing_confidence: fileIntelligence.processing_confidence,
    analysis_metadata: fileIntelligence.analysis_metadata,
    created_at: fileIntelligence.created_at,
    updated_at: fileIntelligence.updated_at
  },
  rawMarkdown: this._lastRawMarkdown || undefined
})
```

### 2. Context Notes Generation

**Service**: `src/services/contextAnnotationService.ts`
**Method**: `addContextAnnotation()`

**Process**:
1. **User Selection**: Text selection from chat or file
2. **Note Creation**: Builds `ContextNote` object
3. **Storage**: Uses `intelligenceClient.write()` with context-notes path
4. **Location**: `<vault>/.intelligence/context-notes/<filename>.json`

**Code Path**:
```typescript
// In contextAnnotationService.ts line 258
const saveResult = await intelligenceClient.write(targetPath, vaultPath, {
  json: contextNote  // Save the individual note directly
})
```

### 3. Smart Annotations Generation

**Service**: `src/services/annotationStorageService.ts`
**Method**: `saveAnnotation()`

**Process**:
1. **User Input**: User creates annotation note
2. **AI Response**: Optional AI-generated response
3. **Storage**: Updates existing file intelligence JSON
4. **Location**: Same as file intelligence (`<vault>/.intelligence/files/<hash>.json`)

## PURPOSE AND DEPENDENCIES

### Primary Purpose

1. **Document Intelligence**: Extract and store key ideas, entities, and insights from files
2. **User Annotations**: Capture user notes and AI responses about specific content
3. **Context Awareness**: Store general insights from chat conversations and cross-file patterns
4. **Local-First**: All data stored locally within vault structure, no external dependencies

### Dependencies

#### Core Services
- **`IntelligenceCoreService`** (electron/core/): Handles file I/O and path resolution
- **`PathResolver`** (electron/core/): Manages storage paths and file hashing
- **`VaultCoreService`** (electron/core/): File system operations

#### Frontend Services
- **`annotationStorageService`**: Manages smart annotations
- **`contextAnnotationService`**: Handles context notes
- **`fileAnalysisService`**: Processes documents and extracts intelligence

#### API Layer
- **`UnifiedAPIClient.intelligence`**: Frontend-to-backend communication
- **IPC Channels**: `intelligence:write`, `intelligence:read`

#### Storage Dependencies
- **File System**: Local storage only, no database required
- **Path Resolution**: Vault-aware path handling
- **File Hashing**: Stable hash generation for file identification

## EXISTING SCHEMA COMPARISON

### What's Actually Implemented vs. Original Design

| Feature | Original Design | Actual Implementation | Status |
|---------|----------------|----------------------|---------|
| **File Intelligence** | ✅ Full schema | ✅ Full schema | **MATCHES** |
| **Smart Annotations** | ✅ Multi-note system | ✅ Multi-note system | **MATCHES** |
| **Context Notes** | ✅ Chat-based insights | ✅ Chat-based insights | **MATCHES** |
| **Entity Index** | ✅ Global registry | ❌ Types only | **MISSING** |
| **Learning Data** | ✅ User preferences | ❌ Not implemented | **MISSING** |
| **Master.md Integration** | ✅ Intelligence summary | ❌ Not implemented | **MISSING** |
| **Session Storage** | ✅ Timestamped sessions | ❌ Single file only | **MISSING** |
| **Export Functionality** | ✅ Data export | ❌ Not implemented | **MISSING** |

### Key Differences

1. **Storage Strategy**: Original planned session-based storage, actual uses single JSON per file
2. **Entity Index**: Original planned global entity registry, actual stores entities per file
3. **Learning System**: Original planned user preference learning, actual no learning implementation
4. **Integration**: Original planned master.md enhancement, actual no integration

## CURRENT LIMITATIONS

### 1. **No Global Entity Index**
- Entities are stored per-file only
- No cross-document entity relationships
- No entity frequency tracking across vault

### 2. **No Learning System**
- User preferences not stored
- No behavior pattern analysis
- No adaptive intelligence

### 3. **No Master.md Integration**
- Intelligence data not reflected in vault overview
- No automated intelligence summaries
- No workflow connection visualization

### 4. **Limited Cross-Vault Intelligence**
- Context notes are vault-specific
- No cross-vault pattern detection
- No global intelligence aggregation

## MIGRATION PATH TO FULL SCHEMA

### Phase 1: Entity Index Implementation
- Implement global entity registry
- Aggregate entities across files
- Build entity relationship mapping

### Phase 2: Learning System
- Store user preferences
- Track interaction patterns
- Implement adaptive intelligence

### Phase 3: Master.md Integration
- Enhance master.md with intelligence data
- Add intelligence summaries
- Show workflow connections

### Phase 4: Cross-Vault Intelligence
- Implement vault intelligence aggregation
- Detect cross-vault patterns
- Global intelligence export

## TECHNICAL IMPLEMENTATION DETAILS

### File Path Resolution
```typescript
// In PathResolver.ts line 79
const filesDir = this.joinSafe(this.getIntelligenceBaseDir(normalizedVault), 'files')
const jsonPath = this.joinSafe(filesDir, `${fileHash}.json`)
```

### File Hashing Algorithm
```typescript
// In PathResolver.ts line 18
static computeStableHash(input: string): string {
  const hash = crypto.createHash('sha256').update(input).digest('hex')
  const fileName = path.basename(input, path.ext(input))
  const hashSuffix = hash.substring(0, 8)
  return `${fileName}_${hashSuffix}`
}
```

### Storage Validation
```typescript
// In IntelligenceCoreService.ts line 45
if (data.file_path && data.file_path !== PathResolver.normalizePath(filePath)) {
  console.log('[CORE] 🚨 CORRUPTION DETECTED: file_path mismatch')
  return { success: true, data: null }
}
```

## CONCLUSION

The current intelligence storage schema is **functionally complete** for basic document intelligence and user annotations, but **architecturally incomplete** compared to the original design. The core data structures are implemented and working, but the higher-level intelligence aggregation and learning systems are missing.

**Recommendation**: The current schema provides a solid foundation. Focus on implementing the missing entity index and learning systems rather than redesigning the existing working components.
