client:733 [vite] connecting...
client:826 [vite] connected.
react-dom_client.js?v=52d093b9:17987 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
ServiceLogger.ts:140 ℹ️ 10:37:56 [OpenRouterService] doInitialize: OpenRouter service initialized {baseUrl: undefined, hasApiKey: false}
ServiceLogger.ts:140 ℹ️ 10:37:56 [LocalModelService] doInitialize: Local model service initialized {ollamaUrl: undefined, lmStudioUrl: undefined}
intelligenceCacheManager.ts:53 [INTELLIGENCE-CACHE] 🚀 Cache manager initialized with config: {maxSize: 52428800, maxEntries: 1000, defaultTTL: 1800000, cleanupInterval: 300000}
performanceMonitor.ts:385 🖥️ [SYSTEM] Using default hardware profile: 16GB RAM, mid-range tier
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
cacheManager.ts:182 🔥 [CACHE] Loaded cache from storage: 2 warm, 2 cold
ServiceLogger.ts:140 ℹ️ 10:37:57 [CacheManager] doInitialize: Cache manager initialized {config: {…}, hotCacheEntries: 0}
vaultUIManager.ts:93 🔍 [VAULT-SETTING] === GET VAULT REGISTRY START ===
vaultUIManager.ts:94 🔍 [VAULT-SETTING] Checking cache first...
ServiceLogger.ts:140 ℹ️ 10:37:57 [IntelligenceService] doInitialize: Intelligence service initialized {processingVersion: undefined}
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultFileHandler] VaultFileHandler initialized with streaming support 
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileAnalysisService] doInitialize: File Analysis Service initialized 
ServiceLogger.ts:140 ℹ️ 10:37:57 [StreamingFileProcessor] doInitialize: Streaming file processor initialized {config: undefined}
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileProcessingQueue] doInitialize: File processing queue initialized 
ServiceLogger.ts:140 ℹ️ 10:37:57 [BatchFileProcessingService] doInitialize: Batch File Processing Service initialized 
ServiceLogger.ts:140 ℹ️ 10:37:57 [SmartInstructionService] doInitialize: Smart Instruction service initialized with LLM integration {processingVersion: undefined}
index.tsx:57 [FileTypeRenderer] Registered plugin for type: pdf
index.tsx:57 [FileTypeRenderer] Registered plugin for type: markdown
index.tsx:57 [FileTypeRenderer] Registered plugin for type: mermaid
index.tsx:57 [FileTypeRenderer] Registered plugin for type: text
index.tsx:57 [FileTypeRenderer] Registered plugin for type: image
index.tsx:57 [FileTypeRenderer] Registered plugin for type: word
index.tsx:57 [FileTypeRenderer] Registered plugin for type: excel
index.tsx:57 [FileTypeRenderer] Registered plugin for type: powerpoint
index.tsx:57 [FileTypeRenderer] Registered plugin for type: unsupported
ServiceLogger.ts:140 ℹ️ 10:37:57 [AskAINavigationService] Ask AI Navigation Service initialized 
main.tsx:9 Window electronAPI available: true
main.tsx:11 ElectronAPI methods: (12) ['invoke', 'db', 'settings', 'files', 'processing', 'shell', 'vault', 'path', 'updater', 'plugins', 'events', 'windowControls']
ServiceLogger.ts:140 ℹ️ 10:37:57 [OpenRouterService] initialization: Operation completed in 29ms: initialization {duration: 29}
ServiceLogger.ts:140 ℹ️ 10:37:57 [OpenRouterService] initialize: Operation completed successfully: initialize {initializationTime: 29}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] initialization: Operation completed in 29ms: initialization {duration: 29}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] initialize: Operation completed successfully: initialize {initializationTime: 29}
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
ServiceLogger.ts:140 ℹ️ 10:37:57 [PerformanceMonitor] doInitialize: Performance monitor initialized with adaptive thresholds {systemProfile: null, jsHeapThreshold: '512MB', systemMemoryThreshold: '80.0%', cpuThreshold: 50, processingBudgets: {…}, …}
ServiceLogger.ts:140 ℹ️ 10:37:57 [CacheManager] initialization: Operation completed in 15ms: initialization {duration: 15}
ServiceLogger.ts:140 ℹ️ 10:37:57 [CacheManager] initialize: Operation completed successfully: initialize {initializationTime: 15}
cacheManager.ts:231 🌡️ [CACHE] Warm cache hit: vault_registry
ServiceLogger.ts:140 ℹ️ 10:37:57 [IntelligenceService] initialization: Operation completed in 12ms: initialization {duration: 12}
ServiceLogger.ts:140 ℹ️ 10:37:57 [IntelligenceService] initialize: Operation completed successfully: initialize {initializationTime: 12}
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultFileHandler] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultFileHandler] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileAnalysisService] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileAnalysisService] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [StreamingFileProcessor] initialization: Operation completed in 10ms: initialization {duration: 10}
ServiceLogger.ts:140 ℹ️ 10:37:57 [StreamingFileProcessor] initialize: Operation completed successfully: initialize {initializationTime: 10}
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileProcessingQueue] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [FileProcessingQueue] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [BatchFileProcessingService] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [BatchFileProcessingService] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SmartInstructionService] initialization: Operation completed in 10ms: initialization {duration: 10}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SmartInstructionService] initialize: Operation completed successfully: initialize {initializationTime: 10}
ServiceLogger.ts:140 ℹ️ 10:37:57 [AskAINavigationService] initialization: Operation completed in 7ms: initialization {duration: 7}
ServiceLogger.ts:140 ℹ️ 10:37:57 [AskAINavigationService] initialize: Operation completed successfully: initialize {initializationTime: 7}
ServiceLogger.ts:140 ℹ️ 10:37:57 [PerformanceMonitor] initialization: Operation completed in 18ms: initialization {duration: 18}
ServiceLogger.ts:140 ℹ️ 10:37:57 [PerformanceMonitor] initialize: Operation completed successfully: initialize {initializationTime: 18}
vaultUIManager.ts:101 🔍 [VAULT-SETTING] ✅ Cache hit - returning cached registry
vaultUIManager.ts:102 🔍 [VAULT-SETTING] Cached registry structure: {vaultRoot: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3', vaultsCount: 2, contextsCount: 2}
vaultUIManager.ts:109 🔍 [VAULT-SETTING] Validating cached registry paths...
vaultUIManager.ts:200 🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY START ===
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultContextService] setCurrentContext: Context changed {contextId: 'wew7hjs5k', vaultPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault'}
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultContextService] initialization: Operation completed in 36ms: initialization {duration: 36}
ServiceLogger.ts:140 ℹ️ 10:37:57 [VaultContextService] initialize: Operation completed successfully: initialize {initializationTime: 36}
react-dom_client.js?v=52d093b9:16 [Violation] 'message' handler took 234ms
vaultUIManager.ts:214 🔍 [VAULT-SETTING] ✅ Cached vault root exists: C:\Users\<USER>\Documents\Post-Kernel-Test3
index.ts:1495 Auto-loading models with saved API key...
useKeyboardShortcuts.ts:35 Navigation history updated: {currentIndex: 0, historyLength: 1, currentPath: '/chat'}
ChatArea.tsx:67 [DEBUG] 🎯 CRITICAL: ChatArea URL params check: {contextFromUrl: null, currentSelectedContextId: null, allParams: {…}, searchParams: '', location: 'http://localhost:5173/?conversation=2cec9edb-c33c-4785-aa76-ad403a98aa47#/chat'}
ChatArea.tsx:78 [DEBUG] 🎯 CRITICAL: No context in URL, keeping current: null
ChatArea.tsx:177 [CHAT-AREA] 🔍 Raw conversation parameter analysis: {conversationParam: undefined, conversationParamType: 'undefined', currentConversationId: null, currentConversationIdType: 'object', convIdRaw: null, …}
ChatArea.tsx:205 [CHAT-AREA] 🔧 Deep link conversation analysis: {conversationParam: undefined, currentConversationId: null, convIdRaw: null, convId: '', convIdType: 'string', …}
ChatArea.tsx:253 [CHAT-AREA] ❌ Missing convId or filePath: {convId: '', filePath: null}
handleDeepLink @ ChatArea.tsx:253
(anonymous) @ ChatArea.tsx:510
react-stack-bottom-frame @ react-dom_client.js?v=52d093b9:17478
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
commitHookEffectListMount @ react-dom_client.js?v=52d093b9:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=52d093b9:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9899
flushPassiveEffects @ react-dom_client.js?v=52d093b9:11302
(anonymous) @ react-dom_client.js?v=52d093b9:11060
performWorkUntilDeadline @ react-dom_client.js?v=52d093b9:36
<ChatArea>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=52d093b9:250
App @ App.tsx:147
react-stack-bottom-frame @ react-dom_client.js?v=52d093b9:17424
renderWithHooks @ react-dom_client.js?v=52d093b9:4206
updateFunctionComponent @ react-dom_client.js?v=52d093b9:6619
beginWork @ react-dom_client.js?v=52d093b9:7654
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
performUnitOfWork @ react-dom_client.js?v=52d093b9:10868
workLoopSync @ react-dom_client.js?v=52d093b9:10728
renderRootSync @ react-dom_client.js?v=52d093b9:10711
performWorkOnRoot @ react-dom_client.js?v=52d093b9:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=52d093b9:11623
performWorkUntilDeadline @ react-dom_client.js?v=52d093b9:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=52d093b9:250
(anonymous) @ main.tsx:23
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'events:subscribe', category: 'events', endpoint: 'subscribe', argsCount: 2, args: Array(2), …}
ServiceLogger.ts:140 ℹ️ 10:37:57 [PerformanceMonitor] startMonitoring: Performance monitoring started {interval: '5000ms'}
vaultUIManager.ts:226 🔍 [VAULT-SETTING] ✅ Cached vault path exists: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'events:subscribe', success: undefined, hasData: false, hasError: false, responseType: 'object', …}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] getVaultRoot: Operation completed in 28ms: getVaultRoot {duration: 28}
vaultUIManager.ts:226 🔍 [VAULT-SETTING] ✅ Cached vault path exists: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault
vaultUIManager.ts:231 🔍 [VAULT-SETTING] ✅ All cached registry paths are valid
vaultUIManager.ts:232 🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY END ===
vaultUIManager.ts:117 🔍 [VAULT-SETTING] ✅ Cached registry paths are valid
vaultUIManager.ts:118 🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (CACHE) ===
ServiceLogger.ts:140 ℹ️ 10:37:57 [ContextVaultService] loadVaults: Operation completed successfully: loadVaults 
ServiceLogger.ts:140 ℹ️ 10:37:57 [ContextVaultService] loadVaults: Operation completed in 475ms: loadVaults {duration: 475}
ServiceLogger.ts:140 ℹ️ 10:37:57 [ContextVaultService] doInitialize: Context vault service initialized successfully {vaultCount: 2, contextCount: 2, selectedContextId: 'jz3n27nx7'}
ServiceLogger.ts:140 ℹ️ 10:37:57 [ContextVaultService] initialization: Operation completed in 476ms: initialization {duration: 476}
ServiceLogger.ts:140 ℹ️ 10:37:57 [ContextVaultService] initialize: Operation completed successfully: initialize {initializationTime: 476}
ChatArea.tsx:128 [DEBUG] No context selected, using shared-dropbox: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox/.intelligence/context-notes
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
index.ts:1310 Using existing models or fetching from OpenRouter...
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] getVaultRoot: Operation completed in 8ms: getVaultRoot {duration: 8}
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
modelUpdateLogic.ts:293 Using model manifest v2025.07.16
index.ts:1316 Loaded 318 models
index.ts:1358 Latest models found: (16) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
index.ts:1433 🔍 Checking local models...
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] loadFiles: Operation completed successfully: loadFiles 
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] loadFiles: Operation completed in 77ms: loadFiles {duration: 77}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] doInitialize: Shared dropbox initialized successfully {sharedPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\shared-dropbox'}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] initialization: Operation completed in 227ms: initialization {duration: 227}
ServiceLogger.ts:140 ℹ️ 10:37:57 [SharedDropboxService] initialize: Operation completed successfully: initialize {initializationTime: 227}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Operation completed in 14ms: checkOllama {duration: 14}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 2, models: Array(2)}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: Operation completed in 310ms: checkLMStudio {duration: 310}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkOllama: Operation completed in 7ms: checkOllama {duration: 7}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 2, models: Array(2)}
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:140 ℹ️ 10:37:57 [LocalModelService] checkLMStudio: Operation completed in 14ms: checkLMStudio {duration: 14}
index.ts:1439 📊 Provider Status: {ollama: {…}, lmstudio: {…}}
index.ts:1443 🤖 All Local Models: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
index.ts:1453 ✅ Local model check complete. Found 6 local models. Total external models: 318
ChatArea.tsx:690 Starting new draft conversation from welcome screen...
index.ts:543 Store: Creating draft conversation with ID: draft-1756118280839-ldo00ouf5
ChatArea.tsx:693 Created draft conversation: draft-1756118280839-ldo00ouf5
ChatArea.tsx:695 Welcome screen draft conversation setup complete
ContextVaultSelector.tsx:42 === STEP 3 DEBUG: CONTEXT VAULT SELECTOR INIT ===
ContextVaultSelector.tsx:46 🎯 ContextVaultSelector received update:
ContextVaultSelector.tsx:47   - Vaults count: 2
ContextVaultSelector.tsx:48   - Selected context ID: jz3n27nx7
ContextVaultSelector.tsx:49   - Vaults data: (2) [{…}, {…}]
ContextVaultSelector.tsx:59 🔍 Finding context by ID (from service): jz3n27nx7
ContextVaultSelector.tsx:62 ✅ Context found and set (from service):
ContextVaultSelector.tsx:63   - Context name: Work Projects
ContextVaultSelector.tsx:64   - Context path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
ContextVaultSelector.tsx:65   - Vault name: Work Vault
ContextVaultSelector.tsx:102 🚀 Initializing context vault service...
ContextVaultSelector.tsx:111 [CONTEXT-SELECTOR] 🎯 External selectedContextId changed: {selectedContextId: undefined, vaultsCount: 0, vaultsLoaded: false}
ContextVaultSelector.tsx:144 [CONTEXT-SELECTOR] 🧹 Clearing context selection
ChatArea.tsx:177 [CHAT-AREA] 🔍 Raw conversation parameter analysis: {conversationParam: undefined, conversationParamType: 'undefined', currentConversationId: 'draft-1756118280839-ldo00ouf5', currentConversationIdType: 'string', convIdRaw: 'draft-1756118280839-ldo00ouf5', …}
ChatArea.tsx:205 [CHAT-AREA] 🔧 Deep link conversation analysis: {conversationParam: undefined, currentConversationId: 'draft-1756118280839-ldo00ouf5', convIdRaw: 'draft-1756118280839-ldo00ouf5', convId: 'draft-1756118280839-ldo00ouf5', convIdType: 'string', …}
ChatArea.tsx:253 [CHAT-AREA] ❌ Missing convId or filePath: {convId: 'draft-1756118280839-ldo00ouf5', filePath: null}
handleDeepLink @ ChatArea.tsx:253
(anonymous) @ ChatArea.tsx:510
react-stack-bottom-frame @ react-dom_client.js?v=52d093b9:17478
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
commitHookEffectListMount @ react-dom_client.js?v=52d093b9:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=52d093b9:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9899
flushPassiveEffects @ react-dom_client.js?v=52d093b9:11302
flushPendingEffects @ react-dom_client.js?v=52d093b9:11276
flushSpawnedWork @ react-dom_client.js?v=52d093b9:11250
commitRoot @ react-dom_client.js?v=52d093b9:11081
commitRootWhenReady @ react-dom_client.js?v=52d093b9:10512
performWorkOnRoot @ react-dom_client.js?v=52d093b9:10457
performSyncWorkOnRoot @ react-dom_client.js?v=52d093b9:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=52d093b9:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=52d093b9:11558
(anonymous) @ react-dom_client.js?v=52d093b9:11649
<ChatArea>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=52d093b9:250
App @ App.tsx:147
react-stack-bottom-frame @ react-dom_client.js?v=52d093b9:17424
renderWithHooks @ react-dom_client.js?v=52d093b9:4206
updateFunctionComponent @ react-dom_client.js?v=52d093b9:6619
beginWork @ react-dom_client.js?v=52d093b9:7654
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
performUnitOfWork @ react-dom_client.js?v=52d093b9:10868
workLoopSync @ react-dom_client.js?v=52d093b9:10728
renderRootSync @ react-dom_client.js?v=52d093b9:10711
performWorkOnRoot @ react-dom_client.js?v=52d093b9:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=52d093b9:11623
performWorkUntilDeadline @ react-dom_client.js?v=52d093b9:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=52d093b9:250
(anonymous) @ main.tsx:23
ContextVaultSelector.tsx:111 [CONTEXT-SELECTOR] 🎯 External selectedContextId changed: {selectedContextId: undefined, vaultsCount: 2, vaultsLoaded: true}
ContextVaultSelector.tsx:144 [CONTEXT-SELECTOR] 🧹 Clearing context selection
conversationIntelligenceManager.ts:68 [CONVERSATION-INTELLIGENCE] 🆕 Initialized conversation context: draft-1756118280839-ldo00ouf5
conversationIntelligenceManager.ts:51 [CONVERSATION-INTELLIGENCE] 🎯 Active conversation set to: draft-1756118280839-ldo00ouf5
index.ts:611 Converting draft conversation to real conversation
index.ts:568 Store: Saved draft conversation with real ID: d417c163-830f-4243-893d-08fc6b992d30
ChatArea.tsx:177 [CHAT-AREA] 🔍 Raw conversation parameter analysis: {conversationParam: undefined, conversationParamType: 'undefined', currentConversationId: 'd417c163-830f-4243-893d-08fc6b992d30', currentConversationIdType: 'string', convIdRaw: 'd417c163-830f-4243-893d-08fc6b992d30', …}
ChatArea.tsx:205 [CHAT-AREA] 🔧 Deep link conversation analysis: {conversationParam: undefined, currentConversationId: 'd417c163-830f-4243-893d-08fc6b992d30', convIdRaw: 'd417c163-830f-4243-893d-08fc6b992d30', convId: 'd417c163-830f-4243-893d-08fc6b992d30', convIdType: 'string', …}
ChatArea.tsx:253 [CHAT-AREA] ❌ Missing convId or filePath: {convId: 'd417c163-830f-4243-893d-08fc6b992d30', filePath: null}
handleDeepLink @ ChatArea.tsx:253
(anonymous) @ ChatArea.tsx:510
react-stack-bottom-frame @ react-dom_client.js?v=52d093b9:17478
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
commitHookEffectListMount @ react-dom_client.js?v=52d093b9:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=52d093b9:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=52d093b9:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=52d093b9:9899
flushPassiveEffects @ react-dom_client.js?v=52d093b9:11302
flushPendingEffects @ react-dom_client.js?v=52d093b9:11276
flushSpawnedWork @ react-dom_client.js?v=52d093b9:11250
commitRoot @ react-dom_client.js?v=52d093b9:11081
commitRootWhenReady @ react-dom_client.js?v=52d093b9:10512
performWorkOnRoot @ react-dom_client.js?v=52d093b9:10457
performSyncWorkOnRoot @ react-dom_client.js?v=52d093b9:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=52d093b9:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=52d093b9:11558
(anonymous) @ react-dom_client.js?v=52d093b9:11649
conversationIntelligenceManager.ts:68 [CONVERSATION-INTELLIGENCE] 🆕 Initialized conversation context: d417c163-830f-4243-893d-08fc6b992d30
conversationIntelligenceManager.ts:51 [CONVERSATION-INTELLIGENCE] 🎯 Active conversation set to: d417c163-830f-4243-893d-08fc6b992d30
index.ts:1226 [STORE] 🔄 Rebuilding conversation intelligence context for: d417c163-830f-4243-893d-08fc6b992d30
index.ts:1251 [STORE] ✅ Rebuilt conversation intelligence context
index.ts:513 [STORE] 📄 Loaded messages and set conversation context for: d417c163-830f-4243-893d-08fc6b992d30
ServiceLogger.ts:140 ℹ️ 10:38:19 [OpenRouterService] setApiKey: API key updated {hasApiKey: true}
openrouter.ts:152  POST https://openrouter.ai/api/v1/chat/completions 403 (Forbidden)
executeOperationOrThrow.model @ openrouter.ts:152
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:266
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
createChatCompletion @ openrouter.ts:141
sendMessage @ index.ts:833
await in sendMessage
handleSubmit @ InputArea.tsx:87
handleKeyDown @ InputArea.tsx:102
executeDispatch @ react-dom_client.js?v=52d093b9:11736
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
processDispatchQueue @ react-dom_client.js?v=52d093b9:11772
(anonymous) @ react-dom_client.js?v=52d093b9:12182
batchedUpdates$1 @ react-dom_client.js?v=52d093b9:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=52d093b9:11877
dispatchEvent @ react-dom_client.js?v=52d093b9:14792
dispatchDiscreteEvent @ react-dom_client.js?v=52d093b9:14773
ServiceLogger.ts:146 ❌ 10:38:20 [OpenRouterService] handleApiError: OpenRouter API error {status: 403, statusText: '', errorData: {…}, headers: {…}}
outputToConsole @ ServiceLogger.ts:146
log @ ServiceLogger.ts:117
error @ ServiceLogger.ts:67
handleApiError @ openrouter.ts:204
await in handleApiError
executeOperationOrThrow.model @ openrouter.ts:175
await in executeOperationOrThrow.model
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:266
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
createChatCompletion @ openrouter.ts:141
sendMessage @ index.ts:833
await in sendMessage
handleSubmit @ InputArea.tsx:87
handleKeyDown @ InputArea.tsx:102
executeDispatch @ react-dom_client.js?v=52d093b9:11736
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
processDispatchQueue @ react-dom_client.js?v=52d093b9:11772
(anonymous) @ react-dom_client.js?v=52d093b9:12182
batchedUpdates$1 @ react-dom_client.js?v=52d093b9:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=52d093b9:11877
dispatchEvent @ react-dom_client.js?v=52d093b9:14792
dispatchDiscreteEvent @ react-dom_client.js?v=52d093b9:14773
ServiceLogger.ts:146 ❌ 10:38:20 [OpenRouterService] createChatCompletion: Operation failed: createChatCompletion ServiceError: OpenRouter API error (403): Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.
    at OpenRouterService.handleApiError (openrouter.ts:234:11)
    at async executeOperationOrThrow.model (openrouter.ts:175:11)
    at async wrapServiceOperation (ServiceError.ts:191:20)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:266:22)
    at async OpenRouterService.executeOperation (BaseService.ts:213:12)
    at async OpenRouterService.executeOperationOrThrow (BaseService.ts:234:20)
    at async OpenRouterService.createChatCompletion (openrouter.ts:141:12)
    at async sendMessage (index.ts:833:22)
    at async handleSubmit (InputArea.tsx:87:7)
outputToConsole @ ServiceLogger.ts:146
log @ ServiceLogger.ts:117
error @ ServiceLogger.ts:64
operationFailure @ ServiceLogger.ts:89
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:266
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
createChatCompletion @ openrouter.ts:141
sendMessage @ index.ts:833
await in sendMessage
handleSubmit @ InputArea.tsx:87
handleKeyDown @ InputArea.tsx:102
executeDispatch @ react-dom_client.js?v=52d093b9:11736
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
processDispatchQueue @ react-dom_client.js?v=52d093b9:11772
(anonymous) @ react-dom_client.js?v=52d093b9:12182
batchedUpdates$1 @ react-dom_client.js?v=52d093b9:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=52d093b9:11877
dispatchEvent @ react-dom_client.js?v=52d093b9:14792
dispatchDiscreteEvent @ react-dom_client.js?v=52d093b9:14773
ServiceLogger.ts:148 ServiceError: OpenRouter API error (403): Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.
    at OpenRouterService.handleApiError (openrouter.ts:234:11)
    at async executeOperationOrThrow.model (openrouter.ts:175:11)
    at async wrapServiceOperation (ServiceError.ts:191:20)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:266:22)
    at async OpenRouterService.executeOperation (BaseService.ts:213:12)
    at async OpenRouterService.executeOperationOrThrow (BaseService.ts:234:20)
    at async OpenRouterService.createChatCompletion (openrouter.ts:141:12)
    at async sendMessage (index.ts:833:22)
    at async handleSubmit (InputArea.tsx:87:7)
outputToConsole @ ServiceLogger.ts:148
log @ ServiceLogger.ts:117
error @ ServiceLogger.ts:64
operationFailure @ ServiceLogger.ts:89
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:266
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
createChatCompletion @ openrouter.ts:141
sendMessage @ index.ts:833
await in sendMessage
handleSubmit @ InputArea.tsx:87
handleKeyDown @ InputArea.tsx:102
executeDispatch @ react-dom_client.js?v=52d093b9:11736
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
processDispatchQueue @ react-dom_client.js?v=52d093b9:11772
(anonymous) @ react-dom_client.js?v=52d093b9:12182
batchedUpdates$1 @ react-dom_client.js?v=52d093b9:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=52d093b9:11877
dispatchEvent @ react-dom_client.js?v=52d093b9:14792
dispatchDiscreteEvent @ react-dom_client.js?v=52d093b9:14773
ServiceLogger.ts:140 ℹ️ 10:38:20 [OpenRouterService] createChatCompletion: Operation completed in 693ms: createChatCompletion {duration: 693}
index.ts:856 OpenRouter API error: ServiceError: OpenRouter API error (403): Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.
    at OpenRouterService.handleApiError (openrouter.ts:234:11)
    at async executeOperationOrThrow.model (openrouter.ts:175:11)
    at async wrapServiceOperation (ServiceError.ts:191:20)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:266:22)
    at async OpenRouterService.executeOperation (BaseService.ts:213:12)
    at async OpenRouterService.executeOperationOrThrow (BaseService.ts:234:20)
    at async OpenRouterService.createChatCompletion (openrouter.ts:141:12)
    at async sendMessage (index.ts:833:22)
    at async handleSubmit (InputArea.tsx:87:7)
sendMessage @ index.ts:856
await in sendMessage
handleSubmit @ InputArea.tsx:87
handleKeyDown @ InputArea.tsx:102
executeDispatch @ react-dom_client.js?v=52d093b9:11736
runWithFiberInDEV @ react-dom_client.js?v=52d093b9:1485
processDispatchQueue @ react-dom_client.js?v=52d093b9:11772
(anonymous) @ react-dom_client.js?v=52d093b9:12182
batchedUpdates$1 @ react-dom_client.js?v=52d093b9:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=52d093b9:11877
dispatchEvent @ react-dom_client.js?v=52d093b9:14792
dispatchDiscreteEvent @ react-dom_client.js?v=52d093b9:14773
conversationIntelligenceManager.ts:51 [CONVERSATION-INTELLIGENCE] 🎯 Active conversation set to: d417c163-830f-4243-893d-08fc6b992d30
index.ts:1226 [STORE] 🔄 Rebuilding conversation intelligence context for: d417c163-830f-4243-893d-08fc6b992d30
index.ts:1251 [STORE] ✅ Rebuilt conversation intelligence context
index.ts:513 [STORE] 📄 Loaded messages and set conversation context for: d417c163-830f-4243-893d-08fc6b992d30
performanceMonitor.ts:153 📊 [PERFORMANCE] JS Heap: 36MB, System RAM: 59.1% (9GB/16GB)
