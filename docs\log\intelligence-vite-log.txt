[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] settings:set - Duration: 1ms
[1] [API] db:createConversation - Request: ["New Conversation"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: New Conversation
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 200
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:createConversation - Duration: 1ms
[1] [API] db:getConversations - Request: []
[1] [API] db:getConversations - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["d417c163-830f-4243-893d-08fc6b992d30"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:addMessage - Request: ["d417c163-830f-4243-893d-08fc6b992d30",{"conversation_id":"d417c163-830f-4243-893d-08fc6b992d30","role":"user","content":"Hello"}]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:addMessage - Duration: 1ms
[1] [API] db:getMessages - Request: ["d417c163-830f-4243-893d-08fc6b992d30"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:getMessages - Duration: 1ms
[1] [API] files:getMessageFiles - Request: ["385d1dde-510d-45dd-9e7a-fd7f24e455b9"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: 385d1dde-510d-45dd-9e7a-fd7f24e455b9
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] files:getMessageFiles - Duration: 1ms
[1] [API] db:updateConversation - Request: ["d417c163-830f-4243-893d-08fc6b992d30","Hello"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: Hello
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 200
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:updateConversation - Duration: 1ms
[1] [API] db:getConversations - Request: []
[1] [API] db:getConversations - Duration: 0ms
[1] [API] db:addMessage - Request: ["d417c163-830f-4243-893d-08fc6b992d30",{"conversation_id":"d417c163-830f-4243-893d-08fc6b992d30","role":"assistant","content":"Error: OpenRouter API error (403): Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.","model":"system"}]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:addMessage - Duration: 0ms
[1] [API] db:getMessages - Request: ["d417c163-830f-4243-893d-08fc6b992d30"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: d417c163-830f-4243-893d-08fc6b992d30
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] db:getMessages - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["385d1dde-510d-45dd-9e7a-fd7f24e455b9"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: 385d1dde-510d-45dd-9e7a-fd7f24e455b9
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] files:getMessageFiles - Duration: 1ms
[1] [API] files:getMessageFiles - Request: ["8dfef175-f973-4347-b3c5-3c446c821573"]
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT START ===
[1] 🔍 [VAULT-SETTING] Input: 8dfef175-f973-4347-b3c5-3c446c821573
[1] 🔍 [VAULT-SETTING] Expected type: string
[1] 🔍 [VAULT-SETTING] Max length: 100
[1] 🔍 [VAULT-SETTING] Actual type: string
[1] 🔍 [VAULT-SETTING] Performing security validation for string input...
[1] 🔍 [VAULT-SETTING] Has directory traversal: false
[1] 🔍 [VAULT-SETTING] User security level: disabled
[1] 🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal
[1] 🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===
[1] [API] files:getMessageFiles - Duration: 0ms
