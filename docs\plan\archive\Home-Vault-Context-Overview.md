# Home Vault Context Overview - Focused Enhancement Plan

## Current State Analysis

### Existing Implementation
The current vault context overview modal (`HomePage.tsx` lines 678-760) displays:
- **Layout**: Fixed modal with `max-w-6xl max-h-[80vh]` dimensions
- **Structure**: 3-panel layout (Recent Chats 30% | Files 30% | Master.md Preview 40%)
- **Content**: Basic context information, file counts, and static master.md preview
- **Interaction**: Click-to-close overlay, minimal interactivity within panels

### Current Pain Points
1. **Static Information Display**: Shows placeholder content instead of live data
2. **No Navigation Actions**: Panels don't lead to actual work locations
3. **Poor Visual Design**: Basic styling without enriched UI elements
4. **Disconnected Workflow**: No vault-centric operations or focus

## Core Enhancement Strategy

### KEY PRINCIPLE: Vault-Centric Operations
**Anything that happens in context vaults should open to the exact location with the vault being focused.**

### 3-Panel Layout with Live Data & Actions

#### Left Column: Recent Chats (33%)
- **Data Source**: Live chat history filtered by selected context vault
- **Display**: Scrollable list, paginated if >20 items, ordered by recency
- **Actions**:
  - Click any chat → Navigate to chat detail with specific vault pre-selected
  - "+" button → Start new chat with specific vault pre-selected
- **Design**: Chat items with timestamps, message previews, vault context indicators

#### Middle Column: Recent Files (33%)
- **Data Source**: Live file list from context vault file system
- **Display**: File icons, names, types, last modified dates
- **Actions**:
  - Click file → Open directly in FilesPage (Explorer mode) with vault focused
  - "+" button → Open system file dialog to add files to vault
- **Design**: File type icons, hover states, quick action buttons

#### Right Column: Master.md Preview (34%)
- **Data Source**: Live master.md content from vault
- **Display**: Rendered markdown view with proper styling
- **Actions**:
  - "Edit Master.md" button → Open in FilesPage (Master.md mode) with vault focused
- **Design**: Clean markdown rendering, scroll if content is long

## Visual Design Enhancements

### Enhanced Header
```
┌─────────────────────────────────────────────────────────────┐
│ [Icon] Context Name                           [Status Badge] │
│ Description text                                             │
│ Files: 12 • Chats: 5 • Last activity: 2 hours ago          │
└─────────────────────────────────────────────────────────────┘
```

### Panel Layout
```
┌─────────────────────────────────────────────────────────────┐
│                    Enhanced Header                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│ Recent Chats    │ Recent Files    │ Master.md Preview       │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────┐ │
│ │ [+] New     │ │ │ [+] Add     │ │ │ # Project Name      │ │
│ │ Chat        │ │ │ Files       │ │ │                     │ │
│ └─────────────┘ │ └─────────────┘ │ │ ## Overview         │ │
│                 │                 │ │ Content here...     │ │
│ Chat Item 1     │ 📄 document.pdf │ │                     │ │
│ Chat Item 2     │ 🖼️ image.png    │ │ [Edit Master.md]    │ │
│ Chat Item 3     │ 📊 data.xlsx    │ │                     │ │
│ ...             │ ...             │ └─────────────────────┘ │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## Implementation Requirements

### Data Integration
1. **Chat History Service**: Fetch chats filtered by context vault ID
2. **File System Service**: Get real file list from vault directory
3. **Master.md Service**: Load and render actual master.md content
4. **Navigation Service**: Handle vault-focused routing to chat/files pages

### UI Components Needed
1. **ChatListItem**: Chat preview with timestamp and vault indicator
2. **FileListItem**: File with icon, name, size, and quick actions
3. **MarkdownRenderer**: Styled markdown display component
4. **ActionButton**: Consistent "+" buttons for new actions

### Navigation Integration
1. **Chat Navigation**: Route to `/chat` with vault context pre-selected
2. **File Navigation**: Route to `/files` with specific vault and file focused
3. **Master.md Navigation**: Route to `/files` in master.md mode with vault focused

## Design System Alignment

### Colors & Styling
- **Header**: `bg-gray-800` with `border-tertiary/50` bottom border
- **Panels**: `bg-gray-900` with subtle dividers
- **Action Buttons**: Primary color with hover states
- **File Icons**: Type-specific colors (blue for docs, green for images, etc.)
- **Chat Items**: Hover states with `bg-gray-700/50`

### Typography
- **Context Name**: `text-lg font-semibold text-supplement1`
- **Panel Headers**: `text-sm font-medium text-gray-300`
- **List Items**: `text-sm text-supplement1`
- **Metadata**: `text-xs text-gray-400`

### Spacing & Layout
- **Panel Padding**: `p-4` for consistent spacing
- **Item Spacing**: `space-y-2` between list items
- **Button Spacing**: `mb-3` for action buttons
- **Dividers**: `border-tertiary/30` for subtle separation

## Success Criteria

### Functional Goals
1. ✅ All data displays live information from actual vault
2. ✅ Every clickable element leads to the correct focused location
3. ✅ Vault context is maintained across all navigation actions
4. ✅ File operations integrate with existing file system

### Design Goals
1. ✅ Visual hierarchy clearly separates the three content areas
2. ✅ Action buttons are prominent and clearly labeled
3. ✅ Content is scannable with proper spacing and typography
4. ✅ Consistent with ChatLo design system and color palette

### User Experience Goals
1. ✅ Users can quickly see recent activity in their vault
2. ✅ One-click access to continue previous work or start new work
3. ✅ Clear understanding of vault contents and organization
4. ✅ Seamless transition from overview to detailed work

