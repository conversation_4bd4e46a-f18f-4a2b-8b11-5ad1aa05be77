### HomePage.tsx — Page-Level Interim Review (Alignment with API-Driven, Vault-Centric Explorer)

| Category | Current State | Evidence / Story (Experience, Services/Components, APIs) | Maturity (0–5) | Improvements |
|---|---|---|---:|---|
| UX Alignment with Vault-Centric Design | Strong vault-first homepage with context cards, upload/drop per vault, and contextual actions | Story: User lands on Home, sees hero cards (New Chat, Continue Thoughts, Organize) and a grid of vault contexts. Selecting a context opens `VaultContextOverview` (modal) for deeper actions. Services: `vaultUIManager.getVaultCards/getVaultRegistry`. Components: hero cards, context cards, modal. | 4.0 | Ensure the modal fully matches the 3-panel spec in `plan/Home-Vault-Context-Overview.md` (Recent Chats, Files, Master.md live). Add direct buttons to open FilesPage in Explorer/Master modes with vault focus carried in route params. |
| Vault-Centric Navigation | Consistent routing to chat/files with vault context | Story: From cards, users can jump to chat (`useNavigation.navigateToChat`) and view all files (`navigateToFile`). Context selection opens details modal. APIs: internal navigation hooks; renderer state. | 4.0 | Add deep links to a specific file/folder within the selected vault. Persist last selection to speed return workflows. |
| File Ingest & Upload UX | Drag-and-drop + system dialog per vault; duplicate-safe naming; small vs large file paths | Story: Drop zone on each context; click “Upload Files” opens `electronAPI.files.showOpenDialog`. For each file: unique name is generated via `generateUniqueFilename()` using `vault.pathExists`. Copy via `electronAPI.vault.copyFile`. For large files dropped into homepage, current design advises manual copy (for streaming path, uploads go through `vaultFileHandler` only when invoked by HomePage small-file streaming). | 3.5 | Use system IPC write for large files too (no manual step): expose/consume `files.saveContentAsFile`/vault write API for user-initiated large uploads, mirroring `vaultFileHandler.uploadFileSystemIPC`. Add bulk progress UI and cancellation. |
| Upload Streaming & Progress | Streaming path present for small files with detailed console progress | Story: For small files, `handleStreamingUpload()` uses `vaultFileHandler.replaceSharedDropboxService(...)` with per-chunk progress callback; user sees toasts; console logs progress in dev. | 3.5 | Surface progress UI in-card (not just toasts/console). Persist progress in Performance Monitor; handle retry/resume for flaky I/O. |
| Post-Upload Intelligence (Just-in-Time) | Optional, triggered after upload via batch service | Story: After successful small-file upload, invokes `batchFileProcessingService.processVault` for the new file (min ideas, auto-select). This is user-initiated (upload), aligned with Just-in-Time. | 4.0 | Add a per-upload toggle “Analyze after upload” and a global setting. Show a link to open the intelligence result in overlay once done. |
| Performance Monitoring Integration | Quick access to Performance Monitor, but no event bus wiring yet | Story: “Monitor” button opens `PerformanceMonitorPage`. Organize shows inline progress (state-driven) but not hooked to a global event stream. | 3.0 | Wire an IPC/event bus for file processing updates (planned chokidar/event system) and reflect in Monitor and per-card badges. |
| Organize Flow (Queue) | Queue-based vault processing from a single CTA | Story: “Organize” triggers `batchFileProcessingService.processAllVaultsWithQueue` with progress stored in component state; success toast suggests viewing Monitor. | 4.0 | Add queue summary in-page; allow pause/stop; persist queue state across reloads. |
| Error Handling & Feedback | Robust try/catch with toasts; detailed console logs for troubleshooting | Story: Clear toasts for success/failure in copy and upload; extensive logging for cleanup and registry regeneration. | 4.0 | Centralize error codes/messages for consistency; offer “View logs” link to a diagnostics panel. |
| Path Handling & Consistency | Some string concatenation for paths; relies on vault IPC normalization | Story: Builds `destinationBasePath = context.path + '/documents'` and uses `/` in concatenation; checks via `vault.pathExists` then `vault.copyFile`. | 2.5 | Adopt centralized path utilities (e.g., `src/utils/vaultPath.ts`) or main-process join helpers; avoid manual `'/'` concatenation; add unit tests for `generateUniqueFilename` across platforms. |
| Local-First, Privacy | All local FS ops via IPC; no cloud calls; respects rules | Story: Uses `window.electronAPI` for dialogs, copy, and path checks; intelligence uses local-first models via services. | 4.5 | Add privacy controls (e.g., “Do not analyze uploads by default”); show where files land and allow changing default folder per vault. |
| Icon Management Compliance | Uses centralized ICONS registry | Story: `FontAwesomeIcon` consumes icons from `ICONS` registry; no direct CDN/library leakage. | 5.0 | Periodically prune unused icons; ensure design mockups (HTML) do not ship with CDN references. |
| Accessibility & UX Polish | Good visual hierarchy and affordances; basic keyboard/motion | Story: Cards, buttons, toasts, and progress bars are present; drag-and-drop states are clear. | 3.5 | Add focus states, ARIA labels on actionable UI, and keyboard support for upload/organize/monitor. Consider reduced motion for spinning icons. |
| Testing Readiness | Page logic testable; path/duplication logic isolated | Story: `generateUniqueFilename` and upload flows lend themselves to unit/integration tests. | 3.0 | Add tests for: duplicate naming, large vs small routing, organize progress reducer, error paths for copy/upload, and navigation with vault focus params. |

### Overall Alignment
- HomePage delivers a vault-centric entry with clear ingest, organization, and navigation touchpoints—well aligned with the system’s API-driven goals. Main gaps are path unification, large-file IPC flow parity, richer realtime progress via events, and stronger accessibility/testing.

### High-Impact Improvements (Next Steps)
- Large files: enable system-IPC write path directly from HomePage for a seamless UX (no manual copy step).
- Path consistency: use centralized path resolver utilities and add cross-platform tests.
- Eventing: emit progress/status events from main process and subscribe in Home/Monitor.
- Accessibility: add ARIA/focus and keyboard shortcuts for primary actions.
- Tests: cover duplicate naming, streaming vs batch routing, and organize progress handling.
