### Core Service Plan 001 — Unified Main-Process Core with Typed IPC

#### Goals
- Single authoritative core in Electron main for file/vault/path/intelligence/plugin/event operations.
- Typed IPC namespaces; renderer uses thin typed clients only.
- Eliminate path duplication and write failures; centralize security, validation, monitoring, and eventing.

#### Architecture Overview
- Core Kernel (main)
  - Location: `electron/core/`
  - Registers all endpoints via `electron/api/APIRegistry.ts` (middleware: security, rate-limit, validation, perf).
- Domain Core Services (main)
  - `PathResolver`: platform-aware join/normalize, canonicalization
  - `VaultCoreService`: dirs/files CRUD, scanning, registry
  - `FileCoreService`: indexing, MIME, hashing, plugin processing
  - `IntelligenceCoreService`: Markdown→JSON parser, read/write `.intelligence`, session mgmt
  - `PluginCoreService`: discovery/loading, future plugin IPC namespaces
  - `EventBus`: file/intelligence/task events; subscribe/unsubscribe
- IPC Namespaces
  - `path:*`, `vault:*`, `files:*`, `intelligence:*`, `plugins:*`, `events:*`
- Renderer Clients (thin)
  - `src/api/UnifiedAPIClient.ts` + `*Client.ts` wrappers around `window.electronAPI`
- Shared Types
  - `src/types/index.ts` is single source (Rule 2.3); imported by both main and renderer
- Admin (`chatlo-admin`)
  - Reuse the same clients to test pipelines/perf

#### Service Map
- PathResolver → `path:join`, `path:normalize`, `path:resolveVault(vaultId, relative)`
- Vault → `vault:*` (dirs/files CRUD, readDir/scan, registry)
- Files → `files:*` (index/search/content/process/dialogs)
- Intelligence → `intelligence:*` (new; IO + parsing)
- Plugins → `plugins:*` (capabilities, config, API discovery)
- Events → `events:*` (new; subscribe/unsubscribe + categories)

#### New / Updated IPC Endpoints (Minimal Set)
- Events (`events`)
  - `events:subscribe(category: 'file'|'intelligence'|'task', filter?: any) → {subscriptionId}`
  - `events:unsubscribe(subscriptionId: string) → void`
  - Emitted events (main → renderer):
    - `file:added|changed|removed` { path, vaultId, meta }
    - `intelligence:updated` { filePath, vaultId, ideaCount }
    - `task:progress` { taskId, percent, message }
- Intelligence (`intelligence`)
  - `intelligence:read(filePath: string) → { success, data?, error? }`
  - `intelligence:write(filePath: string, data: IntelligenceData) → { success, error? }`
  - `intelligence:listSessions(filePath: string) → { success, sessions?, error? }`
  - `intelligence:parseMarkdown(markdown: string) → { success, data?, error? }`
  - Storage target: pick one and enforce (recommended for sessions: `<vault>/.intelligence/documents/<hash>/sessions/session_<ts>.json`; for single-file summary: `<context>/.context/files/<hash>.json`).

#### Implementation Plan & Milestones
1) Core Additions (main) — 4–6 days
- Implement `electron/core/PathResolver.ts` + expose `path:*` helpers.
- Add `events:*` with a simple pub/sub and categories.
- Add `IntelligenceCoreService` with read/write/parse; wire to vault writes using PathResolver.

2) IPC Registration & Docs — 1–2 days
- Register new namespaces/endpoints in `APIRegistry`.
- Update `docs/API_REFERENCE.md` with `events:*` and `intelligence:*` (usage examples, limits).

3) Renderer Migrations — 2–4 days
- Update `storageServiceAdapter` + `FilePageOverlay.tsx` to load persisted intelligence on open and write via `intelligence:*`/`vault:*` (no manual string joins).
- Subscribe to `events:*` in Files/History to refresh on file/intelligence updates.

4) Plugin API Extension (Incremental) — 3–4 days
- Allow plugins to register IPC endpoints via `APIRegistry` under namespaced categories (e.g., `plugin_{id}:*`).
- Document capability discovery and endpoint listing.

5) Security & Configuration — 1–2 days
- Enable `requireAuth` in production; provide minimal local auth context.
- Harden validators and size limits per `API_REFERENCE.md`.

6) Tests — 2–3 days
- Path normalization tests (Windows/Linux), write/read roundtrips to `.intelligence`.
- Markdown→JSON parser tests (json block, front-matter, bullet fallback).
- Overlay load/refresh integration; events roundtrip tests.

#### Navigation / URL Strategy (Optional, No Breakage)
- Keep existing routes; add canonical deep links only where useful:
  - Files: `/files/:vaultId?path=<relative>&mode=explorer|master`
  - Chat (opt): `/chat/:conversationId?vaultId=<id>`
- Add small adapter in `useNavigation` to parse legacy params; no mass changes required.

#### Alignment with System Architecture Interim Review
- Closes gaps in: Path Resolution, Vault IPC completeness, Eventing, Intelligence storage/loader, Plugin API extension.
- Keeps Rule 2.3 (single types) and Local‑First (main-process only for sensitive ops).

#### Risks & Mitigations
- Path drift persists → enforce PathResolver in all `vault:*` handlers; block raw FS ops in renderer.
- Parser brittleness → one parser service, strict tests, reject invalid payloads early.
- Event storms → basic debounce/coalesce in EventBus; opt-in subscriptions.

#### Acceptance Criteria
- Persisted intelligence loads instantly in Files overlay; newly generated labels appear without reload.
- No mixed-separator writes; `.intelligence` files exist on disk (Windows verified).
- Files/History auto-refresh on file/intelligence events.
- `docs/API_REFERENCE.md` includes `events:*` and `intelligence:*` with examples.

#### Effort Estimate (single dev)
- Core + IPC + migrations + docs/tests: ~8–12 person-days (~2–3 weeks with reviews).

