{"document_hash": "a1b2c3d4e5f6", "file_path": "/vault/documents/project-spec.pdf", "file_name": "project-spec.pdf", "file_type": "pdf", "vault_name": "Your First Context Vault", "context_id": "ctx_abc123_def456", "last_updated": "2024-01-15T12:30:00Z", "entity_selections": [{"entity_id": "ent_001", "entity_text": "UI Design", "entity_type": "content_category", "confidence": 0.95, "is_selected": true, "selection_timestamp": "2024-01-15T12:01:30Z", "color_category": "primary", "rank": 1, "context_snippet": "Document contains extensive UI design specifications"}, {"entity_id": "ent_002", "entity_text": "Component Library", "entity_type": "technical_concept", "confidence": 0.82, "is_selected": true, "selection_timestamp": "2024-01-15T12:01:35Z", "color_category": "secondary", "rank": 2, "context_snippet": "References to reusable UI components"}, {"entity_id": "ent_003", "entity_text": "Design Tokens", "entity_type": "technical_concept", "confidence": 0.78, "is_selected": true, "selection_timestamp": "2024-01-15T12:01:40Z", "color_category": "tertiary", "rank": 3, "context_snippet": "Color schemes, typography guidelines"}], "smart_annotations": [{"annotation_id": "note_001", "note_number": 1, "title": "Document Overview", "content": "This comprehensive project specification document outlines the complete design system requirements for the ChatLo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.", "is_ai_generated": true, "generation_source": "full_document", "timestamp": "2024-01-15T12:02:00Z", "last_edited": "2024-01-15T12:02:00Z", "tags": ["overview", "design-system", "specifications"], "confidence": 0.92, "ai_model_used": "gemma3-32k", "processing_time_ms": 1250}, {"annotation_id": "note_002", "note_number": 2, "title": "Component Architecture", "content": "The document emphasizes a modular component architecture with reusable UI elements. Key focus on component library structure and design token implementation.", "is_ai_generated": true, "generation_source": "selected_text", "timestamp": "2024-01-15T12:05:30Z", "last_edited": "2024-01-15T12:05:30Z", "selected_text": "Component Library section discussing reusable UI components and design tokens", "position_info": {"start_offset": 1250, "end_offset": 1580, "page_number": 3}, "tags": ["components", "architecture", "reusability"], "confidence": 0.87, "ai_model_used": "gemma3-32k", "processing_time_ms": 890}, {"annotation_id": "note_003", "note_number": 3, "title": "Implementation Strategy", "content": "Based on the specifications, the implementation should prioritize mobile-first responsive design with consistent color palette usage across all components.", "is_ai_generated": true, "generation_source": "user_prompt", "generation_prompt": "What implementation strategy should we follow based on this document?", "timestamp": "2024-01-15T12:08:15Z", "last_edited": "2024-01-15T12:10:22Z", "tags": ["implementation", "strategy", "mobile-first"], "confidence": 0.84, "ai_model_used": "gemma3-32k", "processing_time_ms": 1450}], "user_notes": [{"note_id": "user_001", "content": "Remember to validate color contrast ratios for accessibility compliance", "timestamp": "2024-01-15T12:12:00Z", "last_edited": "2024-01-15T12:12:00Z", "tags": ["accessibility", "colors", "compliance"]}], "annotation_navigation": {"total_notes": 3, "current_note_index": 2, "note_order": ["note_001", "note_002", "note_003"], "last_viewed_note": "note_003"}, "ai_analysis": {"session_id": "session_12345", "model_used": "gemma3-32k", "processing_time_ms": 3590, "confidence_score": 0.88, "summary": "Comprehensive design system specification with focus on component architecture and UI consistency", "key_insights": ["Document emphasizes modular design approach", "Strong focus on reusable component library", "Detailed color and typography specifications provided", "Implementation guidelines clearly defined"], "suggested_entities": [{"entity": "Design System", "type": "content_category", "confidence": 0.96, "user_selected": false, "context": "Primary theme throughout document"}], "content_analysis": {"document_type": "specification", "primary_topics": ["UI Design", "Component Library", "Design Tokens"], "complexity_score": 0.75, "readability_score": 0.82, "key_concepts": ["modularity", "reusability", "consistency"], "action_items": ["Implement component library", "Define design tokens", "Create style guide"], "relationships": []}, "timestamp": "2024-01-15T12:02:00Z"}, "interaction_history": [{"interaction_id": "int_001", "action_type": "entity_select", "timestamp": "2024-01-15T12:01:30Z", "data": {"entity_id": "ent_001", "entity_text": "UI Design"}, "session_id": "session_12345"}, {"interaction_id": "int_002", "action_type": "annotation_create", "timestamp": "2024-01-15T12:02:00Z", "data": {"annotation_id": "note_001", "generation_source": "full_document"}, "session_id": "session_12345"}, {"interaction_id": "int_003", "action_type": "text_select", "timestamp": "2024-01-15T12:05:00Z", "data": {"selected_text": "Component Library section discussing reusable UI components", "position_info": {"start_offset": 1250, "end_offset": 1580, "page_number": 3}}, "session_id": "session_12345"}, {"interaction_id": "int_004", "action_type": "annotation_create", "timestamp": "2024-01-15T12:05:30Z", "data": {"annotation_id": "note_002", "generation_source": "selected_text"}, "session_id": "session_12345"}]}