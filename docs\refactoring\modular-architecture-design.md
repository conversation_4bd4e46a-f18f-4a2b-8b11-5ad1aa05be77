# Modular API Registry Architecture Design

## Overview

This document outlines the new modular architecture that will replace the monolithic API registry system in ChatLo. The design focuses on domain separation, lazy loading, performance optimization, and maintainability.

## Architecture Principles

### 1. Domain-Driven Design
Each API module represents a distinct business domain with clear boundaries and responsibilities.

### 2. Lazy Loading
Modules are loaded on-demand to reduce startup time and memory footprint.

### 3. Dependency Injection
Modules declare their dependencies explicitly, enabling better testing and modularity.

### 4. Single Responsibility
Each module handles only one domain area, making code easier to understand and maintain.

### 5. Extensibility
The architecture supports easy addition of new modules without modifying existing code.

## Core Architecture Components

### 1. Base Module System

#### BaseAPIModule (Abstract Class)
```typescript
abstract class BaseAPIModule {
  abstract readonly name: string
  abstract readonly version: string
  abstract readonly dependencies: string[]

  protected registry: APIRegistry
  protected logger: Logger
  protected config: ModuleConfig

  abstract initialize(): Promise<void>
  abstract registerEndpoints(): void
  abstract cleanup(): Promise<void>

  // Shared validation utilities
  protected validateInput(value: any, type: string, maxLength?: number): boolean
  protected createValidator(schema: ValidationSchema): Function
  protected addMiddleware(middleware: MiddlewareFunction[]): void
}
```

#### ModuleRegistry (Singleton)
```typescript
class ModuleRegistry {
  private modules: Map<string, BaseAPIModule> = new Map()
  private loadedModules: Set<string> = new Set()
  private dependencies: Map<string, string[]> = new Map()

  async loadModule(moduleName: string): Promise<BaseAPIModule>
  async unloadModule(moduleName: string): Promise<void>
  getModule<T extends BaseAPIModule>(name: string): T
  isLoaded(name: string): boolean
  getDependencyGraph(): Map<string, string[]>
}
```

### 2. Module Auto-Discovery

#### ModuleDiscovery
```typescript
class ModuleDiscovery {
  private moduleDirectories: string[] = [
    'electron/api/modules',
    'electron/plugins/api-modules'
  ]

  async discoverModules(): Promise<ModuleManifest[]>
  async loadModuleManifest(path: string): Promise<ModuleManifest>
  validateModuleStructure(manifest: ModuleManifest): boolean
}
```

#### ModuleManifest
```typescript
interface ModuleManifest {
  name: string
  version: string
  description: string
  main: string
  dependencies: string[]
  optionalDependencies: string[]
  category: 'core' | 'optional' | 'plugin'
  loadPriority: number
  lazy: boolean
}
```

## Specific Module Designs

### 1. DatabaseAPIModule

**Responsibility**: All database operations (conversations, messages, files, artifacts, intelligence)
**Endpoints**: 35 endpoints
**Dependencies**: DatabaseManager, ValidationSchemas

```typescript
class DatabaseAPIModule extends BaseAPIModule {
  readonly name = 'database'
  readonly version = '1.0.0'
  readonly dependencies = ['core']

  private db: DatabaseManager

  async initialize(): Promise<void> {
    this.db = this.getDependency<DatabaseManager>('database-manager')
    this.registerEndpoints()
  }

  registerEndpoints(): void {
    this.registerConversationEndpoints()
    this.registerMessageEndpoints()
    this.registerFileEndpoints()
    this.registerArtifactEndpoints()
    this.registerIntelligenceEndpoints()
    this.registerSettingsEndpoints()
  }

  private registerConversationEndpoints(): void {
    this.registry.registerEndpoint('db', 'getConversations',
      () => this.db.getConversations(),
      { description: 'Get all conversations' }
    )
    // ... other conversation endpoints
  }
}
```

### 2. FileSystemAPIModule

**Responsibility**: File operations, indexing, processing, vault file management
**Endpoints**: 25 endpoints
**Dependencies**: FileSystem, FileCoreService, PathResolver

```typescript
class FileSystemAPIModule extends BaseAPIModule {
  readonly name = 'filesystem'
  readonly version = '1.0.0'
  readonly dependencies = ['core', 'vault']

  private fileSystem: FileSystem
  private fileCore: FileCoreService

  async initialize(): Promise<void> {
    this.fileSystem = this.getDependency<FileSystem>('file-system')
    this.fileCore = this.getDependency<FileCoreService>('file-core')
    this.registerEndpoints()
  }

  registerEndpoints(): void {
    this.registerIndexingEndpoints()
    this.registerProcessingEndpoints()
    this.registerSearchEndpoints()
    this.registerDialogEndpoints()
  }
}
```

### 3. VaultAPIModule

**Responsibility**: Vault creation, scanning, registry management, initialization
**Endpoints**: 15 endpoints
**Dependencies**: PathResolver, FileSystem

```typescript
class VaultAPIModule extends BaseAPIModule {
  readonly name = 'vault'
  readonly version = '1.0.0'
  readonly dependencies = ['core', 'filesystem']

  registerEndpoints(): void {
    this.registerVaultManagementEndpoints()
    this.registerRegistryEndpoints()
    this.registerInitializationEndpoints()
  }
}
```

### 4. IntelligenceAPIModule

**Responsibility**: Intelligence operations, analysis, session management
**Endpoints**: 12 endpoints
**Dependencies**: IntelligenceCoreService, PathResolver

```typescript
class IntelligenceAPIModule extends BaseAPIModule {
  readonly name = 'intelligence'
  readonly version = '1.0.0'
  readonly dependencies = ['core', 'vault']

  private intelCore: IntelligenceCoreService

  registerEndpoints(): void {
    this.registerIntelligenceIOEndpoints()
    this.registerAnalysisEndpoints()
    this.registerSessionEndpoints()
  }
}
```

### 5. PluginAPIModule

**Responsibility**: Plugin discovery, lifecycle management, configuration
**Endpoints**: 8 endpoints
**Dependencies**: PluginManager

```typescript
class PluginAPIModule extends BaseAPIModule {
  readonly name = 'plugins'
  readonly version = '1.0.0'
  readonly dependencies = ['core']

  private pluginManager: PluginManager

  registerEndpoints(): void {
    this.registerPluginLifecycleEndpoints()
    this.registerPluginConfigurationEndpoints()
    this.registerPluginDiscoveryEndpoints()
  }
}
```

### 6. SystemAPIModule

**Responsibility**: System monitoring, shell operations, updater, performance metrics
**Endpoints**: 8 endpoints
**Dependencies**: Shell, AutoUpdater, PerformanceMonitor

```typescript
class SystemAPIModule extends BaseAPIModule {
  readonly name = 'system'
  readonly version = '1.0.0'
  readonly dependencies = ['core']

  registerEndpoints(): void {
    this.registerMonitoringEndpoints()
    this.registerShellEndpoints()
    this.registerUpdaterEndpoints()
    this.registerPerformanceEndpoints()
  }
}
```

## Module Loading Strategy

### 1. Load Order Priority
```typescript
enum LoadPriority {
  CRITICAL = 0,    // Core system modules
  HIGH = 1,        // Database, FileSystem
  MEDIUM = 2,      // Vault, Intelligence
  LOW = 3,         // Plugins, System
  OPTIONAL = 4     // Non-essential modules
}
```

### 2. Dependency Resolution
```typescript
class DependencyResolver {
  resolveDependencies(modules: ModuleManifest[]): ModuleManifest[] {
    // Topological sort based on dependencies
    // Returns modules in load order
  }

  validateDependencies(modules: ModuleManifest[]): ValidationResult {
    // Check for circular dependencies
    // Verify all dependencies are available
  }
}
```

### 3. Lazy Loading Implementation
```typescript
class LazyModuleLoader {
  private loadPromises: Map<string, Promise<BaseAPIModule>> = new Map()

  async loadOnDemand(moduleName: string): Promise<BaseAPIModule> {
    if (this.loadPromises.has(moduleName)) {
      return this.loadPromises.get(moduleName)!
    }

    const loadPromise = this.loadModuleInternal(moduleName)
    this.loadPromises.set(moduleName, loadPromise)
    return loadPromise
  }
}
```

## Performance Optimizations

### 1. Module Caching
- Loaded modules cached in memory
- Shared dependencies reused across modules
- Lazy initialization of heavy resources

### 2. Endpoint Registration Optimization
- Batch registration to reduce IPC overhead
- Validation schema compilation at load time
- Middleware chain optimization

### 3. Memory Management
- Automatic cleanup of unused modules
- Resource pooling for common operations
- Garbage collection hints for large objects

## Directory Structure

```
electron/
├── api/
│   ├── modules/
│   │   ├── core/
│   │   │   ├── BaseAPIModule.ts
│   │   │   ├── ModuleRegistry.ts
│   │   │   └── ModuleDiscovery.ts
│   │   ├── database/
│   │   │   ├── DatabaseAPIModule.ts
│   │   │   ├── manifest.json
│   │   │   └── schemas/
│   │   ├── filesystem/
│   │   │   ├── FileSystemAPIModule.ts
│   │   │   ├── manifest.json
│   │   │   └── validators/
│   │   ├── vault/
│   │   │   ├── VaultAPIModule.ts
│   │   │   └── manifest.json
│   │   ├── intelligence/
│   │   │   ├── IntelligenceAPIModule.ts
│   │   │   └── manifest.json
│   │   ├── plugins/
│   │   │   ├── PluginAPIModule.ts
│   │   │   └── manifest.json
│   │   └── system/
│   │       ├── SystemAPIModule.ts
│   │       └── manifest.json
│   ├── APIRegistry.ts (updated)
│   └── types.ts
└── main.ts (simplified)
```

## Implementation Benefits

### 1. Performance Improvements
- **Startup Time**: 60-70% reduction through lazy loading
- **Memory Usage**: 30-40% reduction from unused module elimination
- **Hot Reload**: 80% faster development cycles
- **Bundle Size**: 25-35% reduction in main process bundle

### 2. Maintainability Improvements
- **Code Organization**: Clear domain separation
- **Testing**: Isolated unit tests per module
- **Development**: Parallel development on different modules
- **Debugging**: Easier to trace issues to specific modules

### 3. Scalability Improvements
- **Extensibility**: Easy addition of new modules
- **Plugin Support**: Better plugin API integration
- **Feature Flags**: Module-level feature toggling
- **Deployment**: Selective module deployment

## Migration Strategy

### Phase 1: Foundation (Days 1-3)
1. Create base module system and interfaces
2. Implement module discovery and registry
3. Set up directory structure and build system

### Phase 2: Core Modules (Days 4-10)
1. Extract DatabaseAPIModule (Days 4-5)
2. Extract FileSystemAPIModule (Days 6-7)
3. Extract VaultAPIModule (Days 8-9)
4. Extract IntelligenceAPIModule (Day 10)

### Phase 3: Supporting Modules (Days 11-14)
1. Extract PluginAPIModule (Day 11)
2. Extract SystemAPIModule (Day 12)
3. Update main.ts integration (Day 13)
4. Testing and validation (Day 14)

### Phase 4: Optimization (Days 15-17)
1. Performance monitoring implementation
2. Hot-reloading support
3. Final testing and documentation

## Risk Mitigation

### 1. Backward Compatibility
- Maintain existing API surface during transition
- Gradual migration with feature flags
- Comprehensive testing of existing functionality

### 2. Performance Regression
- Benchmark existing performance before changes
- Continuous performance monitoring during migration
- Rollback plan if performance degrades

### 3. Development Disruption
- Parallel development on feature branches
- Clear communication of changes to team
- Documentation and training for new architecture

This modular architecture will provide a solid foundation for ChatLo's continued growth while addressing current performance and maintainability challenges.
```
