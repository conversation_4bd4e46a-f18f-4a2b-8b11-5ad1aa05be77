## Text Selection → Annotation Data Flow (V03)

This document explains the end-to-end flow for turning selected text into annotations or context notes, and how the data is written locally in a safe, unified way. It also codifies V03 storage and path rules to prevent root-escape or erroneous root-level folders (e.g., accidental `root/C_`).

### Goals
- Single, unified writing approach under `.intelligence/` per vault
- No root escapes; no absolute paths used as relative targets
- Local-first, user-triggered only (no background processing) per Just-in-Time Intelligence
- Clear distinction between file-specific intelligence and general context notes

---

## High-Level Routes

- Route A: Chat text selection → General or file-scoped Context Note
  - Components: GlobalTextSelectionManager → (TextSelectionOverlay or GeneralTextSelectionOverlay) → contextAnnotationService → intelligenceClient.write
- Route B: File viewer text selection → Pre-filled annotation draft → User saves → annotationStorageService → intelligenceClient.write

---

## Route A: Chat Text Selection Flow

1) Selection capture
- Hook: useGlobalTextSelection detects selection within chat context
- Manager: GlobalTextSelectionManager decides which overlay to present
  - If a FilePage is open (currentFilePath available): treat as file-scoped note
  - Else: treat as general context note in the current or shared vault

2) User action
- Overlay: TextSelectionOverlay (simple) or GeneralTextSelectionOverlay (with tags/category)
- On confirm, GlobalTextSelectionManager calls contextAnnotationService.addContextAnnotation with:
  - selectedText
  - conversationId, chatMessageId (when available)
  - vaultContext (selected vault context ID or fallback)
  - filePath (when in file-scoped mode)

3) Path resolution and note construction
- contextAnnotationService:
  - Builds ContextNote object (title, content, metadata)
  - Computes a targetPath under `.intelligence/`:
    - File-scoped: `context-notes/file-${hash(filePath)}.json`
    - Conversation-scoped: `context-notes/${conversationId}.json`
    - Global fallback: `context-notes/global_context.json`
  - Resolves absolute vaultPath via the vault registry; if unknown, falls back to `<vaultRoot>/shared-dropbox` or literal `shared-dropbox` as a last resort

4) Local write
- Calls intelligenceClient.write(targetPath, vaultPath, { json: ContextNote })
- Expected location: `<vaultPath>/.intelligence/context-notes/<file>.json`

5) Result
- On success: listener notification and UI logs
- On failure: error log; no background retries (user re-triggers if needed)

Why this works well
- targetPath is always a relative path scoped to `.intelligence/`
- vaultPath is resolved from the registry (no UI-provided absolute paths)
- No direct string concatenation with drive letters in targetPath

---

## Route B: File Page Text Selection Flow

1) Selection capture inside file viewer
- FilePageOverlay detects selection and dispatches a `fileTextSelection` DOM event
- IntelligenceHub listens and pre-fills the annotation input with the selected snippet

2) User saves the annotation
- IntelligenceHub invokes annotation save via useFileIntelligence → annotationStorageService.saveAnnotation(filePath, note)

3) Local write
- annotationStorageService builds/merges the file’s intelligence structure and calls
  - intelligenceClient.write(filePath, vaultPath, { json: FileIntelligence, rawMarkdown? })
- Here, the first argument is the source filePath (not a `.intelligence` target). The client maps this to the correct `.intelligence` location for the file’s intelligence document.

4) Result
- Unified file intelligence JSON updated and preserved (smart_annotations, key_ideas, etc.) under the file’s intelligence record

Why this works well
- intelligenceClient.write is the single writer; services don’t handcraft `.intelligence` paths for file intelligence
- Avoids duplicating path logic and reduces TS error surface

---

## Storage Conventions (V03)

- All intelligence lives under: `<vaultPath>/.intelligence/`
- Two write modes via intelligenceClient.write:
  1) File Intelligence mode
     - Call signature: write(sourceFilePath, vaultPath, payload)
     - Client resolves to the file’s intelligence record path under `.intelligence`
  2) Context Notes mode
     - Call signature: write(relativeTargetPath, vaultPath, payload)
     - relativeTargetPath is a safe, normalized path like `context-notes/<name>.json`
- Never pass absolute paths as relative targets; never include drive letters in targetPath
- Never prepend leading slashes in targetPath

---

## Guardrails to Prevent Root Escapes and `root/C_` Folders

1) targetPath discipline (Context Notes)
- Only ever use relative paths under `.intelligence` (e.g., `context-notes/foo.json`)
- Disallow sequences like `..` or `:\` in targetPath
- Enforce a whitelist regex: `^[a-z0-9-]+(?:/[a-z0-9-_.]+)*\.json$` (tune as needed)

2) vaultPath derivation
- Always resolve via registry (vaultClient.getRegistry) and select the actual absolute path for the chosen context
- Fallback to `<vaultRoot>/shared-dropbox` using platform-aware joining
- Do not accept UI-provided absolute filesystem paths for vaultPath

3) filePath handling (File Intelligence mode)
- Pass the real source file’s absolute path as the first argument; let the client map to `.intelligence`
- Services should not compute or guess the `.intelligence` location for file intelligence

4) joining and normalization
- Use vaultPath utilities (e.g., in vaultPath.ts) for normalization
- Avoid duplicating path logic or mixing separators manually

Common pitfall that leads to `root/C_`
- Treating a Windows absolute path as a relative targetPath (e.g., `C:\…`) which then gets sanitized into a folder under the project root (like `C_`). Solution: in Context Notes mode, never pass absolute file paths as targetPath.

---

## Just-in-Time Intelligence Compliance
- All processing is explicitly triggered by user actions (selection + confirm)
- No background or continuous extraction
- User controls destination (via selected vault context); default/fallback is shared-dropbox

---

## Reference: Key Responsibilities

- GlobalTextSelectionManager
  - Routes selection to file-scoped vs general context flows
- TextSelectionOverlay / GeneralTextSelectionOverlay
  - Collects user confirmation and optional metadata
- contextAnnotationService
  - Constructs ContextNote, chooses `context-notes/<file>.json`, resolves vaultPath, writes via intelligenceClient
- FilePageOverlay → IntelligenceHub → annotationStorageService
  - Pre-fills draft from selection; saving writes file intelligence via intelligenceClient
- intelligenceClient (Unified API)
  - Single writer handling both “file intelligence” and “context notes” modes; maps inputs to `.intelligence/`

---

## Actionable Best Practices (V03)

- Use intelligenceClient.write exclusively for intelligence writes
- For context notes: pass a safe relative targetPath and a registry-derived vaultPath
- For file intelligence: pass the real filePath and vaultPath; do not compute `.intelligence` paths yourself
- Validate and normalize inputs (no `..`, no leading `/`, no drive letters in targetPath)
- Keep all writes local; do not perform any background writes or remote calls

---

## Next Checks (suggested)
- Verify registry contains the correct vaultRoot and context entries
- Add unit tests to simulate both routes and assert final paths:
  - `<vault>/.intelligence/context-notes/<file>.json`
  - `<vault>/.intelligence/<file intelligence path>`
- Add a small validator in contextAnnotationService to reject unsafe targetPaths

