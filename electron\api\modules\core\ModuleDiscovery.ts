/**
 * Module Discovery
 * Discovers and validates API modules in the filesystem
 */

import * as fs from 'fs'
import * as path from 'path'
import { ModuleManifest } from './ModuleRegistry'

export interface DiscoveryOptions {
  moduleDirectories: string[]
  manifestFileName: string
  validateStructure: boolean
}

export interface DiscoveryResult {
  manifests: ModuleManifest[]
  errors: string[]
  discoveryTime: number
}

export class ModuleDiscovery {
  private options: DiscoveryOptions

  constructor(options?: Partial<DiscoveryOptions>) {
    this.options = {
      moduleDirectories: [
        'electron/api/modules',
        'electron/plugins/api-modules'
      ],
      manifestFileName: 'manifest.json',
      validateStructure: true,
      ...options
    }
  }

  /**
   * Discover all modules in configured directories
   */
  async discoverModules(): Promise<DiscoveryResult> {
    const startTime = Date.now()
    const manifests: ModuleManifest[] = []
    const errors: string[] = []

    console.log('[DISCOVERY] Starting module discovery...')

    for (const directory of this.options.moduleDirectories) {
      try {
        const dirManifests = await this.discoverInDirectory(directory)
        manifests.push(...dirManifests)
      } catch (error: any) {
        errors.push(`Failed to discover modules in ${directory}: ${error.message}`)
      }
    }

    const discoveryTime = Date.now() - startTime
    console.log(`[DISCOVERY] Discovered ${manifests.length} modules in ${discoveryTime}ms`)

    if (errors.length > 0) {
      console.warn('[DISCOVERY] Discovery errors:', errors)
    }

    return {
      manifests,
      errors,
      discoveryTime
    }
  }

  /**
   * Discover modules in a specific directory
   */
  private async discoverInDirectory(directory: string): Promise<ModuleManifest[]> {
    const manifests: ModuleManifest[] = []

    if (!fs.existsSync(directory)) {
      console.warn(`[DISCOVERY] Directory not found: ${directory}`)
      return manifests
    }

    const entries = fs.readdirSync(directory, { withFileTypes: true })

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const modulePath = path.join(directory, entry.name)
        const manifestPath = path.join(modulePath, this.options.manifestFileName)

        if (fs.existsSync(manifestPath)) {
          try {
            const manifest = await this.loadModuleManifest(manifestPath)

            if (this.options.validateStructure) {
              const validation = this.validateModuleStructure(manifest, modulePath)
              if (!validation.valid) {
                console.warn(`[DISCOVERY] Invalid module structure for ${manifest.name}:`, validation.errors)
                continue
              }
            }

            manifests.push(manifest)
            console.log(`[DISCOVERY] Found module: ${manifest.name} v${manifest.version}`)
          } catch (error: any) {
            console.error(`[DISCOVERY] Failed to load manifest ${manifestPath}:`, error.message)
          }
        }
      }
    }

    return manifests
  }

  /**
   * Load and parse a module manifest file
   */
  async loadModuleManifest(manifestPath: string): Promise<ModuleManifest> {
    try {
      const content = fs.readFileSync(manifestPath, 'utf8')
      const manifest = JSON.parse(content) as ModuleManifest

      // Validate required fields
      this.validateManifestFields(manifest)

      // Set defaults
      manifest.dependencies = manifest.dependencies || []
      manifest.optionalDependencies = manifest.optionalDependencies || []
      manifest.category = manifest.category || 'optional'
      manifest.loadPriority = manifest.loadPriority ?? 3
      manifest.lazy = manifest.lazy ?? false

      return manifest
    } catch (error: any) {
      throw new Error(`Failed to load manifest from ${manifestPath}: ${error.message}`)
    }
  }

  /**
   * Validate required manifest fields
   */
  private validateManifestFields(manifest: any): void {
    const requiredFields = ['name', 'version', 'description', 'main']

    for (const field of requiredFields) {
      if (!manifest[field]) {
        throw new Error(`Missing required field: ${field}`)
      }
    }

    // Validate field types
    if (typeof manifest.name !== 'string') {
      throw new Error('Field "name" must be a string')
    }

    if (typeof manifest.version !== 'string') {
      throw new Error('Field "version" must be a string')
    }

    if (typeof manifest.main !== 'string') {
      throw new Error('Field "main" must be a string')
    }

    if (manifest.dependencies && !Array.isArray(manifest.dependencies)) {
      throw new Error('Field "dependencies" must be an array')
    }

    if (manifest.loadPriority !== undefined && typeof manifest.loadPriority !== 'number') {
      throw new Error('Field "loadPriority" must be a number')
    }
  }

  /**
   * Validate module directory structure
   */
  validateModuleStructure(manifest: ModuleManifest, modulePath: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check if main file exists
    const mainFilePath = path.resolve(modulePath, manifest.main)
    if (!fs.existsSync(mainFilePath)) {
      errors.push(`Main file not found: ${manifest.main}`)
    }

    // Check for TypeScript files
    if (manifest.main.endsWith('.ts')) {
      const jsFilePath = mainFilePath.replace('.ts', '.js')
      if (!fs.existsSync(jsFilePath)) {
        console.warn(`[DISCOVERY] TypeScript file found but no compiled JS: ${manifest.main}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get module directories
   */
  getModuleDirectories(): string[] {
    return [...this.options.moduleDirectories]
  }

  /**
   * Add module directory
   */
  addModuleDirectory(directory: string): void {
    if (!this.options.moduleDirectories.includes(directory)) {
      this.options.moduleDirectories.push(directory)
    }
  }

  /**
   * Remove module directory
   */
  removeModuleDirectory(directory: string): void {
    const index = this.options.moduleDirectories.indexOf(directory)
    if (index > -1) {
      this.options.moduleDirectories.splice(index, 1)
    }
  }
}
