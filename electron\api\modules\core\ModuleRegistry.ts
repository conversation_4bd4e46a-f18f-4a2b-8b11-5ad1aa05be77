/**
 * Module Registry
 * Manages the lifecycle of API modules in the modular registry system
 */

import { BaseAPIModule, APIModuleInterface, ModuleConfig, ModuleHealth } from './BaseAPIModule'
import { APIRegistry } from '../../APIRegistry'
import { ModuleManifest, LoadPriority, ModuleLoadResult } from '../../types'

// Re-export types for backward compatibility
export { ModuleManifest, LoadPriority, ModuleLoadResult } from '../../types'

interface ModuleRegistryLoadResult extends ModuleLoadResult {
  module?: BaseAPIModule
}

export class ModuleRegistry {
  private static instance: ModuleRegistry
  private modules: Map<string, BaseAPIModule> = new Map()
  private manifests: Map<string, ModuleManifest> = new Map()
  private loadedModules: Set<string> = new Set()
  private loadPromises: Map<string, Promise<ModuleLoadResult>> = new Map()
  private dependencies: Map<string, string[]> = new Map()
  private apiRegistry: APIRegistry

  private constructor(apiRegistry: APIRegistry) {
    this.apiRegistry = apiRegistry
  }

  /**
   * Get singleton instance
   */
  static getInstance(apiRegistry?: APIRegistry): ModuleRegistry {
    if (!ModuleRegistry.instance) {
      if (!apiRegistry) {
        throw new Error('APIRegistry required for first initialization')
      }
      ModuleRegistry.instance = new ModuleRegistry(apiRegistry)
    }
    return ModuleRegistry.instance
  }

  /**
   * Register a module manifest
   */
  registerManifest(manifest: ModuleManifest): void {
    this.manifests.set(manifest.name, manifest)
    this.dependencies.set(manifest.name, manifest.dependencies)
    console.log(`[REGISTRY] Registered manifest for module: ${manifest.name}`)
  }

  /**
   * Load a module by name
   */
  async loadModule(moduleName: string): Promise<ModuleLoadResult> {
    // Return existing load promise if already loading
    if (this.loadPromises.has(moduleName)) {
      return this.loadPromises.get(moduleName)!
    }

    // Return existing module if already loaded
    if (this.loadedModules.has(moduleName)) {
      return {
        success: true,
        module: this.modules.get(moduleName)!
      }
    }

    const loadPromise = this.loadModuleInternal(moduleName)
    this.loadPromises.set(moduleName, loadPromise)

    try {
      const result = await loadPromise
      if (result.success) {
        this.loadedModules.add(moduleName)
      }
      return result
    } finally {
      this.loadPromises.delete(moduleName)
    }
  }

  /**
   * Internal module loading logic
   */
  private async loadModuleInternal(moduleName: string): Promise<ModuleLoadResult> {
    const startTime = Date.now()

    try {
      console.log(`[REGISTRY] Loading module: ${moduleName}`)

      // Get manifest
      const manifest = this.manifests.get(moduleName)
      if (!manifest) {
        throw new Error(`Module manifest not found: ${moduleName}`)
      }

      // Load dependencies first
      await this.loadDependencies(manifest.dependencies)

      // Dynamic import of module
      const modulePath = manifest.main
      const ModuleClass = await this.importModule(modulePath)

      // Create module instance
      const moduleInstance = new ModuleClass() as BaseAPIModule

      // Initialize module
      const config: ModuleConfig = {
        enabled: true,
        lazy: manifest.lazy,
        priority: manifest.loadPriority,
        ...manifest.config
      }

      await moduleInstance.initialize(this.apiRegistry, config)

      // Store module
      this.modules.set(moduleName, moduleInstance)

      const loadTime = Date.now() - startTime
      console.log(`[REGISTRY] Module ${moduleName} loaded successfully in ${loadTime}ms`)

      return {
        success: true,
        module: moduleInstance,
        loadTime
      }
    } catch (error: any) {
      const loadTime = Date.now() - startTime
      console.error(`[REGISTRY] Failed to load module ${moduleName}:`, error)

      return {
        success: false,
        error: error.message,
        loadTime
      }
    }
  }

  /**
   * Load module dependencies
   */
  private async loadDependencies(dependencies: string[]): Promise<void> {
    for (const dep of dependencies) {
      if (!this.loadedModules.has(dep)) {
        const result = await this.loadModule(dep)
        if (!result.success) {
          throw new Error(`Failed to load dependency: ${dep}`)
        }
      }
    }
  }

  /**
   * Dynamic module import
   */
  private async importModule(modulePath: string): Promise<any> {
    try {
      const module = await import(modulePath)
      return module.default || module
    } catch (error) {
      throw new Error(`Failed to import module from ${modulePath}: ${error}`)
    }
  }

  /**
   * Unload a module
   */
  async unloadModule(moduleName: string): Promise<void> {
    const module = this.modules.get(moduleName)
    if (!module) {
      console.warn(`[REGISTRY] Module not loaded: ${moduleName}`)
      return
    }

    try {
      console.log(`[REGISTRY] Unloading module: ${moduleName}`)

      // Cleanup module
      await module.cleanup()

      // Remove from registry
      this.modules.delete(moduleName)
      this.loadedModules.delete(moduleName)

      console.log(`[REGISTRY] Module ${moduleName} unloaded successfully`)
    } catch (error) {
      console.error(`[REGISTRY] Failed to unload module ${moduleName}:`, error)
      throw error
    }
  }

  /**
   * Get a loaded module
   */
  getModule<T extends BaseAPIModule>(name: string): T | undefined {
    return this.modules.get(name) as T
  }

  /**
   * Check if module is loaded
   */
  isLoaded(name: string): boolean {
    return this.loadedModules.has(name)
  }

  /**
   * Get dependency graph
   */
  getDependencyGraph(): Map<string, string[]> {
    return new Map(this.dependencies)
  }

  /**
   * Get all loaded modules
   */
  getLoadedModules(): string[] {
    return Array.from(this.loadedModules)
  }

  /**
   * Get all registered manifests
   */
  getManifests(): ModuleManifest[] {
    return Array.from(this.manifests.values())
  }

  /**
   * Get module health status
   */
  getModuleHealth(moduleName: string): ModuleHealth | undefined {
    const module = this.modules.get(moduleName)
    return module?.getHealth()
  }

  /**
   * Get health status of all modules
   */
  getAllModuleHealth(): Map<string, ModuleHealth> {
    const health = new Map<string, ModuleHealth>()
    for (const [name, module] of this.modules) {
      health.set(name, module.getHealth())
    }
    return health
  }

  /**
   * Load modules by priority
   */
  async loadModulesByPriority(): Promise<Map<string, ModuleLoadResult>> {
    const results = new Map<string, ModuleLoadResult>()

    // Sort manifests by priority
    const sortedManifests = Array.from(this.manifests.values())
      .sort((a, b) => a.loadPriority - b.loadPriority)

    // Load non-lazy modules first
    const nonLazyModules = sortedManifests.filter(m => !m.lazy)
    for (const manifest of nonLazyModules) {
      const result = await this.loadModule(manifest.name)
      results.set(manifest.name, result)
    }

    return results
  }

  /**
   * Validate dependency graph for circular dependencies
   */
  validateDependencies(): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    const visited = new Set<string>()
    const recursionStack = new Set<string>()

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) {
        errors.push(`Circular dependency detected involving: ${node}`)
        return true
      }

      if (visited.has(node)) {
        return false
      }

      visited.add(node)
      recursionStack.add(node)

      const deps = this.dependencies.get(node) || []
      for (const dep of deps) {
        if (hasCycle(dep)) {
          return true
        }
      }

      recursionStack.delete(node)
      return false
    }

    // Check all modules for cycles
    for (const moduleName of this.manifests.keys()) {
      if (hasCycle(moduleName)) {
        break
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Cleanup all modules
   */
  async cleanup(): Promise<void> {
    console.log('[REGISTRY] Cleaning up all modules')

    const cleanupPromises = Array.from(this.modules.keys()).map(name =>
      this.unloadModule(name).catch(error =>
        console.error(`[REGISTRY] Failed to cleanup module ${name}:`, error)
      )
    )

    await Promise.all(cleanupPromises)

    // Clear all state
    this.modules.clear()
    this.loadedModules.clear()
    this.loadPromises.clear()

    console.log('[REGISTRY] All modules cleaned up')
  }
}
