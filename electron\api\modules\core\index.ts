/**
 * Core Module System Exports
 * Central export point for all core module system components
 */

// Import and re-export types from the main types file
export {
  ValidationSchema,
  MiddlewareFunction,
  MiddlewareContext,
  ModuleManifest,
  LoadPriority,
  ModuleLoadResult
} from '../../types'

// Base classes and interfaces
export {
  BaseAPIModule,
  APIModuleInterface,
  ModuleConfig,
  ModuleDependency,
  ModuleHealth
} from './BaseAPIModule'

// Module registry
export {
  ModuleRegistry
} from './ModuleRegistry'

// Module discovery
export {
  ModuleDiscovery,
  DiscoveryOptions,
  DiscoveryResult
} from './ModuleDiscovery'

// Import types for use in utilities
import { ModuleManifest, LoadPriority } from '../../types'

// Module system utilities
export class ModuleSystemUtils {
  /**
   * Create a module manifest template
   */
  static createManifestTemplate(name: string, version: string = '1.0.0'): ModuleManifest {
    return {
      name,
      version,
      description: `${name} API module`,
      main: `${name}APIModule.ts`,
      dependencies: [],
      optionalDependencies: [],
      category: 'optional',
      loadPriority: LoadPriority.LOW,
      lazy: false
    }
  }

  /**
   * Validate module name format
   */
  static validateModuleName(name: string): boolean {
    // Module names should be lowercase, alphanumeric with hyphens
    return /^[a-z][a-z0-9-]*$/.test(name)
  }

  /**
   * Validate version format (semver)
   */
  static validateVersion(version: string): boolean {
    // Basic semver validation
    return /^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+)?$/.test(version)
  }

  /**
   * Sort modules by load priority
   */
  static sortByPriority(manifests: ModuleManifest[]): ModuleManifest[] {
    return manifests.sort((a, b) => a.loadPriority - b.loadPriority)
  }

  /**
   * Check for circular dependencies
   */
  static hasCircularDependencies(manifests: ModuleManifest[]): boolean {
    const graph = new Map<string, string[]>()

    // Build dependency graph
    for (const manifest of manifests) {
      graph.set(manifest.name, manifest.dependencies)
    }

    // DFS to detect cycles
    const visited = new Set<string>()
    const recursionStack = new Set<string>()

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) return true
      if (visited.has(node)) return false

      visited.add(node)
      recursionStack.add(node)

      const deps = graph.get(node) || []
      for (const dep of deps) {
        if (hasCycle(dep)) return true
      }

      recursionStack.delete(node)
      return false
    }

    for (const manifest of manifests) {
      if (hasCycle(manifest.name)) return true
    }

    return false
  }
}
