/**
 * Database API Module
 * Handles all database-related operations including conversations, messages, files, artifacts, and intelligence
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class DatabaseAPIModule extends BaseAPIModule {
  readonly name = 'database'
  readonly version = '1.0.0'
  readonly description = 'Database operations for conversations, messages, files, artifacts, and intelligence'
  readonly dependencies: ModuleDependency[] = []

  private db: any // DatabaseManager instance will be injected

  protected async onInitialize(): Promise<void> {
    // Get database manager from main process
    this.db = this.getDependency('database-manager')
    this.log('info', 'Database API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering database endpoints...')

    // Register all database endpoint categories
    this.registerConversationEndpoints()
    this.registerMessageEndpoints()
    this.registerFileEndpoints()
    this.registerArtifactEndpoints()
    this.registerIntelligenceEndpoints()
    this.registerSettingsEndpoints()
    this.registerDiagnosticEndpoints()

    this.log('info', `Registered ${this.endpoints.size} database endpoints`)
  }

  /**
   * Register conversation-related endpoints
   */
  private registerConversationEndpoints(): void {
    // Get all conversations
    this.registerEndpoint('db', 'getConversations',
      () => this.db.getConversations(),
      { description: 'Get all conversations' }
    )

    // Get specific conversation
    this.registerEndpoint('db', 'getConversation',
      (id: string) => this.db.getConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get a specific conversation'
      }
    )

    // Create new conversation
    this.registerEndpoint('db', 'createConversation',
      (title: string) => this.db.createConversation(title),
      {
        validator: (title: string) => {
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Create a new conversation'
      }
    )

    // Update conversation
    this.registerEndpoint('db', 'updateConversation',
      (id: string, title: string) => this.db.updateConversation(id, title),
      {
        validator: (id: string, title: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Update a conversation'
      }
    )

    // Delete conversation
    this.registerEndpoint('db', 'deleteConversation',
      (id: string) => this.db.deleteConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Delete a conversation'
      }
    )

    // Toggle pin conversation
    this.registerEndpoint('db', 'togglePinConversation',
      (id: string) => this.db.togglePinConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Toggle pin status of a conversation'
      }
    )

    // Search conversations
    this.registerEndpoint('db', 'searchConversations',
      (searchTerm: string) => this.db.searchConversationsAndMessages(searchTerm),
      {
        validator: (searchTerm: string) => {
          if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
        },
        description: 'Search conversations and messages'
      }
    )

    // Get conversations with artifacts
    this.registerEndpoint('db', 'getConversationsWithArtifacts',
      () => this.db.getConversationsWithArtifacts(),
      { description: 'Get conversations that have artifacts' }
    )
  }

  /**
   * Register message-related endpoints
   */
  private registerMessageEndpoints(): void {
    // Get messages for conversation
    this.registerEndpoint('db', 'getMessages',
      (conversationId: string) => this.db.getMessages(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get messages for a conversation'
      }
    )

    // Add new message
    this.registerEndpoint('db', 'addMessage',
      (conversationId: string, message: any) => this.db.addMessage(conversationId, message),
      {
        validator: (conversationId: string, message: any) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!message || typeof message !== 'object') throw new Error('Invalid message object')
        },
        description: 'Add a new message'
      }
    )

    // Toggle pin message
    this.registerEndpoint('db', 'togglePinMessage',
      (id: string) => this.db.togglePinMessage(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Toggle pin status of a message'
      }
    )

    // Update message intelligence
    this.registerEndpoint('db', 'updateMessageIntelligence',
      (messageId: string, entities: string, topics: string, confidence: number) =>
        this.db.updateMessageIntelligence(messageId, entities, topics, confidence),
      {
        validator: (messageId: string, entities: string, topics: string, confidence: number) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(entities, 'string', 5000)) throw new Error('Invalid entities')
          if (!this.validateInput(topics, 'string', 5000)) throw new Error('Invalid topics')
          if (typeof confidence !== 'number' || confidence < 0 || confidence > 1) throw new Error('Invalid confidence')
        },
        description: 'Update message intelligence data'
      }
    )
  }

  /**
   * Register file-related endpoints
   */
  private registerFileEndpoints(): void {
    // Get all files
    this.registerEndpoint('db', 'getFiles',
      () => this.db.getFiles(),
      { description: 'Get all files' }
    )

    // Get specific file
    this.registerEndpoint('db', 'getFile',
      (id: string) => this.db.getFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Get a specific file'
      }
    )

    // Add new file
    this.registerEndpoint('db', 'addFile',
      (file: any) => this.db.addFile(file),
      {
        validator: (file: any) => {
          if (!file || typeof file !== 'object') throw new Error('Invalid file object')
          if (!this.validateInput(file.name, 'string', 255)) throw new Error('Invalid file name')
        },
        description: 'Add a new file'
      }
    )

    // Update file
    this.registerEndpoint('db', 'updateFile',
      (id: string, updates: any) => this.db.updateFile(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update a file'
      }
    )

    // Delete file
    this.registerEndpoint('db', 'deleteFile',
      (id: string) => this.db.deleteFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Delete a file'
      }
    )
  }

  /**
   * Register artifact-related endpoints
   */
  private registerArtifactEndpoints(): void {
    // Get all artifacts
    this.registerEndpoint('db', 'getArtifacts',
      () => this.db.getArtifacts(),
      { description: 'Get all artifacts' }
    )

    // Add new artifact
    this.registerEndpoint('db', 'addArtifact',
      (artifact: any) => this.db.addArtifact(artifact),
      {
        validator: (artifact: any) => {
          if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
          if (!this.validateInput(artifact.title, 'string', 200)) throw new Error('Invalid artifact title')
        },
        description: 'Add a new artifact'
      }
    )

    // Update artifact
    this.registerEndpoint('db', 'updateArtifact',
      (id: string, updates: any) => this.db.updateArtifact(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update an artifact'
      }
    )

    // Remove artifact
    this.registerEndpoint('db', 'removeArtifact',
      (id: string) => this.db.removeArtifact(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
        },
        description: 'Remove an artifact'
      }
    )

    // Get conversation artifacts
    this.registerEndpoint('db', 'getConversationArtifacts',
      (conversationId: string) => this.db.getConversationArtifacts(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get artifacts for a conversation'
      }
    )
  }

  /**
   * Register intelligence-related endpoints
   */
  private registerIntelligenceEndpoints(): void {
    // Add pinned intelligence
    this.registerEndpoint('db', 'addPinnedIntelligence',
      (intelligence: any) => this.db.addPinnedIntelligence(intelligence),
      {
        validator: (intelligence: any) => {
          if (!intelligence || typeof intelligence !== 'object') throw new Error('Invalid intelligence object')
        },
        description: 'Add pinned intelligence'
      }
    )

    // Get pinned intelligence
    this.registerEndpoint('db', 'getPinnedIntelligence',
      (messageId: string) => this.db.getPinnedIntelligence(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get pinned intelligence for a message'
      }
    )

    // Get all pinned intelligence
    this.registerEndpoint('db', 'getAllPinnedIntelligence',
      () => this.db.getAllPinnedIntelligence(),
      { description: 'Get all pinned intelligence' }
    )
  }

  /**
   * Register settings-related endpoints
   */
  private registerSettingsEndpoints(): void {
    // Connect portable database
    this.registerEndpoint('db', 'connectPortableDB',
      (dbPath: string) => this.db.connectPortableDB(dbPath),
      {
        validator: (dbPath: string) => {
          if (!this.validateInput(dbPath, 'string', 500)) throw new Error('Invalid database path')
        },
        description: 'Connect to portable database'
      }
    )

    // Migrate to portable path
    this.registerEndpoint('db', 'migrateToPortablePath',
      (newPath: string) => this.db.migrateToPortablePath(newPath),
      {
        validator: (newPath: string) => {
          if (!this.validateInput(newPath, 'string', 500)) throw new Error('Invalid path')
        },
        description: 'Migrate database to portable path'
      }
    )

    // Open database at path
    this.registerEndpoint('db', 'openAtPath',
      (dbPath: string) => this.db.openAtPath(dbPath),
      {
        validator: (dbPath: string) => {
          if (!this.validateInput(dbPath, 'string', 500)) throw new Error('Invalid database path')
        },
        description: 'Open database at specific path'
      }
    )
  }

  /**
   * Register diagnostic and maintenance endpoints
   */
  private registerDiagnosticEndpoints(): void {
    // Get database health
    this.registerEndpoint('db', 'getDatabaseHealth',
      () => this.db.getDatabaseHealth(),
      { description: 'Get database health status' }
    )

    // Create backup
    this.registerEndpoint('db', 'createBackup',
      (backupPath?: string) => this.db.createBackup(backupPath),
      {
        validator: (backupPath?: string) => {
          if (backupPath && !this.validateInput(backupPath, 'string', 500)) {
            throw new Error('Invalid backup path')
          }
        },
        description: 'Create database backup'
      }
    )

    // Safe close
    this.registerEndpoint('db', 'safeClose',
      () => this.db.safeClose(),
      { description: 'Safely close database connection' }
    )

    // Prepare for disconnect
    this.registerEndpoint('db', 'prepareForDisconnect',
      () => this.db.prepareForDisconnect(),
      { description: 'Prepare database for disconnect' }
    )
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up Database API Module')
    // Cleanup database connections if needed
    if (this.db && typeof this.db.safeClose === 'function') {
      try {
        await this.db.safeClose()
      } catch (error) {
        this.log('error', 'Error closing database connection:', error)
      }
    }
  }
}
