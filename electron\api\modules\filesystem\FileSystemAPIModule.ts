/**
 * File System API Module
 * Handles all file system operations including indexing, processing, searching, and vault file management
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class FileSystemAPIModule extends BaseAPIModule {
  readonly name = 'filesystem'
  readonly version = '1.0.0'
  readonly description = 'File system operations including indexing, processing, searching, and vault management'
  readonly dependencies: ModuleDependency[] = [
    { name: 'vault', optional: false }
  ]

  private fileSystem: any // FileSystem service
  private fileCore: any // FileCoreService
  private pathResolver: any // PathResolver service

  protected async onInitialize(): Promise<void> {
    // Get required services
    this.fileSystem = this.getDependency('file-system')
    this.fileCore = this.getDependency('file-core')
    this.pathResolver = this.getDependency('path-resolver')
    this.log('info', 'File System API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering file system endpoints...')

    // Register all file system endpoint categories
    this.registerPathEndpoints()
    this.registerIndexingEndpoints()
    this.registerProcessingEndpoints()
    this.registerSearchEndpoints()
    this.registerDialogEndpoints()
    this.registerAttachmentEndpoints()
    this.registerContentEndpoints()

    this.log('info', `Registered ${this.endpoints.size} file system endpoints`)
  }

  /**
   * Register path-related endpoints
   */
  private registerPathEndpoints(): void {
    // Get vault root path
    this.registerEndpoint('files', 'getVaultRootPath',
      () => this.pathResolver.getVaultRootPath(),
      { description: 'Get the vault root path' }
    )

    // Set vault root path
    this.registerEndpoint('files', 'setVaultRootPath',
      (path: string) => this.pathResolver.setVaultRootPath(path),
      {
        validator: (path: string) => {
          if (!this.validateInput(path, 'string', 500)) throw new Error('Invalid vault root path')
        },
        description: 'Set the vault root path'
      }
    )

    // Get ChatLo folder path
    this.registerEndpoint('files', 'getChatloFolderPath',
      () => this.pathResolver.getChatloFolderPath(),
      { description: 'Get the ChatLo folder path' }
    )

    // Set ChatLo folder path
    this.registerEndpoint('files', 'setChatloFolderPath',
      (path: string) => this.pathResolver.setChatloFolderPath(path),
      {
        validator: (path: string) => {
          if (!this.validateInput(path, 'string', 500)) throw new Error('Invalid ChatLo folder path')
        },
        description: 'Set the ChatLo folder path'
      }
    )
  }

  /**
   * Register processing-related endpoints
   */
  private registerProcessingEndpoints(): void {
    // Get file processor plugins
    this.registerEndpoint('files', 'getFileProcessorPlugins',
      () => this.fileCore.getFileProcessorPlugins(),
      { description: 'Get available file processor plugins' }
    )

    // Set file processor plugin enabled
    this.registerEndpoint('files', 'setFileProcessorPluginEnabled',
      (pluginId: string, enabled: boolean) => this.fileCore.setFileProcessorPluginEnabled(pluginId, enabled),
      {
        validator: (pluginId: string, enabled: boolean) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
          if (!this.validateInput(enabled, 'boolean')) throw new Error('Invalid enabled value')
        },
        description: 'Enable or disable a file processor plugin'
      }
    )

    // Process file content
    this.registerEndpoint('files', 'processFileContent',
      (filePath: string, options?: any) => this.fileCore.processFileContent(filePath, options),
      {
        validator: (filePath: string, options?: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Process file content with available processors'
      }
    )

    // Process file
    this.registerEndpoint('files', 'processFile',
      (filePath: string, processorType?: string) => this.fileCore.processFile(filePath, processorType),
      {
        validator: (filePath: string, processorType?: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (processorType && !this.validateInput(processorType, 'string', 50)) {
            throw new Error('Invalid processor type')
          }
        },
        description: 'Process a file with specific processor'
      }
    )
  }

  /**
   * Register search-related endpoints
   */
  private registerSearchEndpoints(): void {
    // Search files
    this.registerEndpoint('files', 'searchFiles',
      (query: string, options?: any) => this.fileSystem.searchFiles(query, options),
      {
        validator: (query: string, options?: any) => {
          if (!this.validateInput(query, 'string', 200)) throw new Error('Invalid search query')
          if (options && typeof options !== 'object') throw new Error('Invalid search options')
        },
        description: 'Search files by query'
      }
    )

    // Get metadata
    this.registerEndpoint('files', 'getMetadata',
      (filePath: string) => this.fileSystem.getMetadata(filePath),
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Get file metadata'
      }
    )
  }

  /**
   * Register dialog-related endpoints
   */
  private registerDialogEndpoints(): void {
    // Show open dialog
    this.registerEndpoint('files', 'showOpenDialog',
      (options?: any) => this.fileSystem.showOpenDialog(options),
      {
        validator: (options?: any) => {
          if (options && typeof options !== 'object') throw new Error('Invalid dialog options')
        },
        description: 'Show file open dialog'
      }
    )

    // Show save dialog
    this.registerEndpoint('files', 'showSaveDialog',
      (options?: any) => this.fileSystem.showSaveDialog(options),
      {
        validator: (options?: any) => {
          if (options && typeof options !== 'object') throw new Error('Invalid dialog options')
        },
        description: 'Show file save dialog'
      }
    )
  }

  /**
   * Register attachment-related endpoints
   */
  private registerAttachmentEndpoints(): void {
    // Add file attachment
    this.registerEndpoint('files', 'addFileAttachment',
      (messageId: string, filePath: string) => this.fileCore.addFileAttachment(messageId, filePath),
      {
        validator: (messageId: string, filePath: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Add file attachment to message'
      }
    )

    // Get file attachments
    this.registerEndpoint('files', 'getFileAttachments',
      (messageId: string) => this.fileCore.getFileAttachments(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get file attachments for message'
      }
    )

    // Get message files
    this.registerEndpoint('files', 'getMessageFiles',
      (messageId: string) => this.fileCore.getMessageFiles(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get files associated with message'
      }
    )

    // Remove file attachment
    this.registerEndpoint('files', 'removeFileAttachment',
      (messageId: string, filePath: string) => this.fileCore.removeFileAttachment(messageId, filePath),
      {
        validator: (messageId: string, filePath: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Remove file attachment from message'
      }
    )
  }

  /**
   * Register content-related endpoints
   */
  private registerContentEndpoints(): void {
    // Copy file to uploads
    this.registerEndpoint('files', 'copyFileToUploads',
      (filePath: string, newName?: string) => this.fileCore.copyFileToUploads(filePath, newName),
      {
        validator: (filePath: string, newName?: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (newName && !this.validateInput(newName, 'string', 255)) throw new Error('Invalid new name')
        },
        description: 'Copy file to uploads directory'
      }
    )

    // Save content to vault
    this.registerEndpoint('files', 'saveContentToVault',
      (content: string, fileName: string, vaultName: string) =>
        this.fileCore.saveContentToVault(content, fileName, vaultName),
      {
        validator: (content: string, fileName: string, vaultName: string) => {
          if (!this.validateInput(content, 'string', 1000000)) throw new Error('Invalid content')
          if (!this.validateInput(fileName, 'string', 255)) throw new Error('Invalid file name')
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Save content to vault as file'
      }
    )

    // Save content as file
    this.registerEndpoint('files', 'saveContentAsFile',
      (content: string, filePath: string) => this.fileCore.saveContentAsFile(content, filePath),
      {
        validator: (content: string, filePath: string) => {
          if (!this.validateInput(content, 'string', 1000000)) throw new Error('Invalid content')
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Save content as file at specified path'
      }
    )

    // Delete file
    this.registerEndpoint('files', 'deleteFile',
      (filePath: string) => this.fileCore.deleteFile(filePath),
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Delete a file'
      }
    )

    // Get file content
    this.registerEndpoint('files', 'getFileContent',
      (filePath: string, encoding?: string) => this.fileCore.getFileContent(filePath, encoding),
      {
        validator: (filePath: string, encoding?: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (encoding && !this.validateInput(encoding, 'string', 20)) throw new Error('Invalid encoding')
        },
        description: 'Get file content'
      }
    )
  }

  /**
   * Register indexing-related endpoints
   */
  private registerIndexingEndpoints(): void {
    // Get indexed files
    this.registerEndpoint('files', 'getIndexedFiles',
      () => this.fileSystem.getIndexedFiles(),
      { description: 'Get all indexed files' }
    )

    // Reindex tree
    this.registerEndpoint('files', 'reindexTree',
      (rootPath?: string) => this.fileSystem.reindexTree(rootPath),
      {
        validator: (rootPath?: string) => {
          if (rootPath && !this.validateInput(rootPath, 'string', 500)) {
            throw new Error('Invalid root path')
          }
        },
        description: 'Reindex file tree'
      }
    )

    // Index file
    this.registerEndpoint('files', 'indexFile',
      (filePath: string) => this.fileSystem.indexFile(filePath),
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Index a specific file'
      }
    )

    // Index vault file
    this.registerEndpoint('files', 'indexVaultFile',
      (filePath: string, vaultName: string) => this.fileSystem.indexVaultFile(filePath, vaultName),
      {
        validator: (filePath: string, vaultName: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Index a file in a specific vault'
      }
    )

    // Index all files
    this.registerEndpoint('files', 'indexAllFiles',
      () => this.fileSystem.indexAllFiles(),
      { description: 'Index all files in the system' }
    )
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up File System API Module')
    // Cleanup any file system resources if needed
    if (this.fileSystem && typeof this.fileSystem.cleanup === 'function') {
      try {
        await this.fileSystem.cleanup()
      } catch (error) {
        this.log('error', 'Error cleaning up file system:', error)
      }
    }
  }
}
