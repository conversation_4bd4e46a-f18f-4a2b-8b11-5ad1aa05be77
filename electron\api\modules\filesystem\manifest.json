{"name": "filesystem", "version": "1.0.0", "description": "File system operations including indexing, processing, searching, and vault management", "main": "FileSystemAPIModule.ts", "dependencies": ["vault"], "optionalDependencies": [], "category": "core", "loadPriority": 1, "lazy": false, "config": {"maxFileSize": 104857600, "allowedExtensions": [".txt", ".md", ".json", ".js", ".ts", ".py", ".html", ".css"], "indexingEnabled": true, "processingTimeout": 30000}}