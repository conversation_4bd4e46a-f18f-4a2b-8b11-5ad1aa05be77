/**
 * Intelligence API Module
 * Handles intelligence operations, analysis, and session management
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class IntelligenceAPIModule extends BaseAPIModule {
  readonly name = 'intelligence'
  readonly version = '1.0.0'
  readonly description = 'Intelligence operations including analysis, session management, and data processing'
  readonly dependencies: ModuleDependency[] = [
    { name: 'vault', optional: false }
  ]

  private intelCore: any // IntelligenceCoreService
  private pathResolver: any // PathResolver service

  protected async onInitialize(): Promise<void> {
    this.intelCore = this.getDependency('intelligence-core')
    this.pathResolver = this.getDependency('path-resolver')
    this.log('info', 'Intelligence API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering intelligence endpoints...')

    this.registerIntelligenceIOEndpoints()
    this.registerAnalysisEndpoints()
    this.registerSessionEndpoints()

    this.log('info', `Registered ${this.endpoints.size} intelligence endpoints`)
  }

  private registerIntelligenceIOEndpoints(): void {
    // Read intelligence
    this.registerEndpoint('intelligence', 'readIntelligence',
      (filePath: string) => this.intelCore.readIntelligence(filePath),
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Read intelligence data from file'
      }
    )

    // Write intelligence
    this.registerEndpoint('intelligence', 'writeIntelligence',
      (filePath: string, data: any) => this.intelCore.writeIntelligence(filePath, data),
      {
        validator: (filePath: string, data: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!data || typeof data !== 'object') throw new Error('Invalid intelligence data')
        },
        description: 'Write intelligence data to file'
      }
    )

    // Get intelligence summary
    this.registerEndpoint('intelligence', 'getIntelligenceSummary',
      (filePath: string) => this.intelCore.getIntelligenceSummary(filePath),
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Get intelligence summary for file'
      }
    )
  }

  private registerAnalysisEndpoints(): void {
    // Analyze content
    this.registerEndpoint('intelligence', 'analyzeContent',
      (content: string, options?: any) => this.intelCore.analyzeContent(content, options),
      {
        validator: (content: string, options?: any) => {
          if (!this.validateInput(content, 'string', 100000)) throw new Error('Invalid content')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Analyze content for intelligence extraction'
      }
    )

    // Extract entities
    this.registerEndpoint('intelligence', 'extractEntities',
      (content: string) => this.intelCore.extractEntities(content),
      {
        validator: (content: string) => {
          if (!this.validateInput(content, 'string', 100000)) throw new Error('Invalid content')
        },
        description: 'Extract entities from content'
      }
    )

    // Generate insights
    this.registerEndpoint('intelligence', 'generateInsights',
      (data: any) => this.intelCore.generateInsights(data),
      {
        validator: (data: any) => {
          if (!data || typeof data !== 'object') throw new Error('Invalid data')
        },
        description: 'Generate insights from intelligence data'
      }
    )
  }

  private registerSessionEndpoints(): void {
    // Create session
    this.registerEndpoint('intelligence', 'createSession',
      (sessionData: any) => this.intelCore.createSession(sessionData),
      {
        validator: (sessionData: any) => {
          if (!sessionData || typeof sessionData !== 'object') throw new Error('Invalid session data')
        },
        description: 'Create intelligence session'
      }
    )

    // Get session
    this.registerEndpoint('intelligence', 'getSession',
      (sessionId: string) => this.intelCore.getSession(sessionId),
      {
        validator: (sessionId: string) => {
          if (!this.validateInput(sessionId, 'string', 100)) throw new Error('Invalid session ID')
        },
        description: 'Get intelligence session'
      }
    )

    // Update session
    this.registerEndpoint('intelligence', 'updateSession',
      (sessionId: string, updates: any) => this.intelCore.updateSession(sessionId, updates),
      {
        validator: (sessionId: string, updates: any) => {
          if (!this.validateInput(sessionId, 'string', 100)) throw new Error('Invalid session ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates')
        },
        description: 'Update intelligence session'
      }
    )

    // Delete session
    this.registerEndpoint('intelligence', 'deleteSession',
      (sessionId: string) => this.intelCore.deleteSession(sessionId),
      {
        validator: (sessionId: string) => {
          if (!this.validateInput(sessionId, 'string', 100)) throw new Error('Invalid session ID')
        },
        description: 'Delete intelligence session'
      }
    )
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up Intelligence API Module')
    if (this.intelCore && typeof this.intelCore.cleanup === 'function') {
      try {
        await this.intelCore.cleanup()
      } catch (error) {
        this.log('error', 'Error cleaning up intelligence core:', error)
      }
    }
  }
}
