/**
 * Plugin API Module
 * Handles plugin discovery, lifecycle management, and configuration
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class PluginAPIModule extends BaseAPIModule {
  readonly name = 'plugins'
  readonly version = '1.0.0'
  readonly description = 'Plugin management including discovery, lifecycle, and configuration'
  readonly dependencies: ModuleDependency[] = []

  private pluginManager: any

  protected async onInitialize(): Promise<void> {
    this.pluginManager = this.getDependency('plugin-manager')
    this.log('info', 'Plugin API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering plugin endpoints...')

    // Plugin discovery
    this.registerEndpoint('plugins', 'discoverPlugins',
      () => this.pluginManager.discoverPlugins(),
      { description: 'Discover available plugins' }
    )

    // Plugin lifecycle
    this.registerEndpoint('plugins', 'enablePlugin',
      (pluginId: string) => this.pluginManager.enablePlugin(pluginId),
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Enable a plugin'
      }
    )

    this.registerEndpoint('plugins', 'disablePlugin',
      (pluginId: string) => this.pluginManager.disablePlugin(pluginId),
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Disable a plugin'
      }
    )

    // Plugin configuration
    this.registerEndpoint('plugins', 'getPluginConfig',
      (pluginId: string) => this.pluginManager.getPluginConfig(pluginId),
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Get plugin configuration'
      }
    )

    this.registerEndpoint('plugins', 'setPluginConfig',
      (pluginId: string, config: any) => this.pluginManager.setPluginConfig(pluginId, config),
      {
        validator: (pluginId: string, config: any) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
          if (!config || typeof config !== 'object') throw new Error('Invalid config')
        },
        description: 'Set plugin configuration'
      }
    )

    // Plugin status
    this.registerEndpoint('plugins', 'getPluginStatus',
      (pluginId: string) => this.pluginManager.getPluginStatus(pluginId),
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Get plugin status'
      }
    )

    this.registerEndpoint('plugins', 'listPlugins',
      () => this.pluginManager.listPlugins(),
      { description: 'List all plugins' }
    )

    this.registerEndpoint('plugins', 'reloadPlugin',
      (pluginId: string) => this.pluginManager.reloadPlugin(pluginId),
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Reload a plugin'
      }
    )

    this.log('info', `Registered ${this.endpoints.size} plugin endpoints`)
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up Plugin API Module')
  }
}
