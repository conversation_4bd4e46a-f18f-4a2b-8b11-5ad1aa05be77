/**
 * System API Module
 * Handles system operations including monitoring, performance metrics, shell operations, and updater
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class SystemAPIModule extends BaseAPIModule {
  readonly name = 'system'
  readonly version = '1.0.0'
  readonly description = 'System operations including monitoring, performance metrics, shell operations, and updater'
  readonly dependencies: ModuleDependency[] = []

  private systemMonitor: any
  private shell: any
  private autoUpdater: any

  protected async onInitialize(): Promise<void> {
    this.systemMonitor = this.getDependency('system-monitor')
    this.shell = this.getDependency('shell')
    this.autoUpdater = this.getDependency('auto-updater')
    this.log('info', 'System API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering system endpoints...')

    this.registerMonitoringEndpoints()
    this.registerShellEndpoints()
    this.registerUpdaterEndpoints()
    this.registerPerformanceEndpoints()

    this.log('info', `Registered ${this.endpoints.size} system endpoints`)
  }

  private registerMonitoringEndpoints(): void {
    // Get system info
    this.registerEndpoint('system', 'getSystemInfo',
      () => this.systemMonitor.getSystemInfo(),
      { description: 'Get system information' }
    )

    // Get performance metrics
    this.registerEndpoint('system', 'getPerformanceMetrics',
      () => this.systemMonitor.getPerformanceMetrics(),
      { description: 'Get performance metrics' }
    )

    // Monitor system health
    this.registerEndpoint('system', 'monitorSystemHealth',
      (interval?: number) => this.systemMonitor.monitorSystemHealth(interval),
      {
        validator: (interval?: number) => {
          if (interval !== undefined && (typeof interval !== 'number' || interval < 1000)) {
            throw new Error('Invalid monitoring interval')
          }
        },
        description: 'Monitor system health'
      }
    )
  }

  private registerShellEndpoints(): void {
    // Execute shell command
    this.registerEndpoint('system', 'executeShellCommand',
      (command: string, options?: any) => this.shell.executeCommand(command, options),
      {
        validator: (command: string, options?: any) => {
          if (!this.validateInput(command, 'string', 1000)) throw new Error('Invalid command')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Execute shell command',
        requiresAuth: true,
        requiredPermission: 'system:shell'
      }
    )

    // Get shell environment
    this.registerEndpoint('system', 'getShellEnvironment',
      () => this.shell.getEnvironment(),
      { description: 'Get shell environment variables' }
    )
  }

  private registerUpdaterEndpoints(): void {
    // Check for updates
    this.registerEndpoint('system', 'checkForUpdates',
      () => this.autoUpdater.checkForUpdates(),
      { description: 'Check for application updates' }
    )

    // Download update
    this.registerEndpoint('system', 'downloadUpdate',
      () => this.autoUpdater.downloadUpdate(),
      { description: 'Download available update' }
    )

    // Install update
    this.registerEndpoint('system', 'installUpdate',
      () => this.autoUpdater.quitAndInstall(),
      {
        description: 'Install downloaded update',
        requiresAuth: true,
        requiredPermission: 'system:update'
      }
    )

    // Get update status
    this.registerEndpoint('system', 'getUpdateStatus',
      () => this.autoUpdater.getUpdateStatus(),
      { description: 'Get update status' }
    )
  }

  private registerPerformanceEndpoints(): void {
    // Get memory usage
    this.registerEndpoint('system', 'getMemoryUsage',
      () => process.memoryUsage(),
      { description: 'Get memory usage statistics' }
    )

    // Get CPU usage
    this.registerEndpoint('system', 'getCPUUsage',
      () => this.systemMonitor.getCPUUsage(),
      { description: 'Get CPU usage statistics' }
    )

    // Clear cache
    this.registerEndpoint('system', 'clearCache',
      () => this.systemMonitor.clearCache(),
      {
        description: 'Clear system cache',
        requiresAuth: true,
        requiredPermission: 'system:cache'
      }
    )

    // Garbage collect
    this.registerEndpoint('system', 'forceGarbageCollection',
      () => {
        if (global.gc) {
          global.gc()
          return { success: true, message: 'Garbage collection completed' }
        }
        return { success: false, message: 'Garbage collection not available' }
      },
      {
        description: 'Force garbage collection',
        requiresAuth: true,
        requiredPermission: 'system:gc'
      }
    )
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up System API Module')
    if (this.systemMonitor && typeof this.systemMonitor.cleanup === 'function') {
      try {
        await this.systemMonitor.cleanup()
      } catch (error) {
        this.log('error', 'Error cleaning up system monitor:', error)
      }
    }
  }
}
