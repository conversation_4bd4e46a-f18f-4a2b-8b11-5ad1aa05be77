/**
 * Vault API Module
 * Handles vault creation, scanning, registry management, and initialization
 */

import { BaseAPIModule, ModuleDependency } from '../core/BaseAPIModule'

export class VaultAPIModule extends BaseAPIModule {
  readonly name = 'vault'
  readonly version = '1.0.0'
  readonly description = 'Vault operations including creation, scanning, registry management, and initialization'
  readonly dependencies: ModuleDependency[] = []

  private vaultService: any // VaultService
  private pathResolver: any // PathResolver service
  private vaultRegistry: any // VaultRegistry service

  protected async onInitialize(): Promise<void> {
    // Get required services
    this.vaultService = this.getDependency('vault-service')
    this.pathResolver = this.getDependency('path-resolver')
    this.vaultRegistry = this.getDependency('vault-registry')
    this.log('info', 'Vault API Module initialized')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering vault endpoints...')

    // Register all vault endpoint categories
    this.registerVaultManagementEndpoints()
    this.registerRegistryEndpoints()
    this.registerInitializationEndpoints()
    this.registerScanningEndpoints()

    this.log('info', `Registered ${this.endpoints.size} vault endpoints`)
  }

  /**
   * Register vault management endpoints
   */
  private registerVaultManagementEndpoints(): void {
    // Create vault
    this.registerEndpoint('vault', 'createVault',
      (vaultName: string, vaultPath: string) => this.vaultService.createVault(vaultName, vaultPath),
      {
        validator: (vaultName: string, vaultPath: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Create a new vault'
      }
    )

    // Delete vault
    this.registerEndpoint('vault', 'deleteVault',
      (vaultName: string) => this.vaultService.deleteVault(vaultName),
      {
        validator: (vaultName: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Delete a vault'
      }
    )

    // Get vault info
    this.registerEndpoint('vault', 'getVaultInfo',
      (vaultName: string) => this.vaultService.getVaultInfo(vaultName),
      {
        validator: (vaultName: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Get vault information'
      }
    )

    // List vaults
    this.registerEndpoint('vault', 'listVaults',
      () => this.vaultService.listVaults(),
      { description: 'List all available vaults' }
    )

    // Rename vault
    this.registerEndpoint('vault', 'renameVault',
      (oldName: string, newName: string) => this.vaultService.renameVault(oldName, newName),
      {
        validator: (oldName: string, newName: string) => {
          if (!this.validateInput(oldName, 'string', 100)) throw new Error('Invalid old vault name')
          if (!this.validateInput(newName, 'string', 100)) throw new Error('Invalid new vault name')
        },
        description: 'Rename a vault'
      }
    )
  }

  /**
   * Register registry management endpoints
   */
  private registerRegistryEndpoints(): void {
    // Get vault registry
    this.registerEndpoint('vault', 'getVaultRegistry',
      () => this.vaultRegistry.getRegistry(),
      { description: 'Get the vault registry' }
    )

    // Update vault registry
    this.registerEndpoint('vault', 'updateVaultRegistry',
      (registry: any) => this.vaultRegistry.updateRegistry(registry),
      {
        validator: (registry: any) => {
          if (!registry || typeof registry !== 'object') throw new Error('Invalid registry object')
        },
        description: 'Update the vault registry'
      }
    )

    // Add vault to registry
    this.registerEndpoint('vault', 'addVaultToRegistry',
      (vaultName: string, vaultPath: string, metadata?: any) =>
        this.vaultRegistry.addVault(vaultName, vaultPath, metadata),
      {
        validator: (vaultName: string, vaultPath: string, metadata?: any) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (metadata && typeof metadata !== 'object') throw new Error('Invalid metadata')
        },
        description: 'Add vault to registry'
      }
    )

    // Remove vault from registry
    this.registerEndpoint('vault', 'removeVaultFromRegistry',
      (vaultName: string) => this.vaultRegistry.removeVault(vaultName),
      {
        validator: (vaultName: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Remove vault from registry'
      }
    )

    // Validate vault registry
    this.registerEndpoint('vault', 'validateVaultRegistry',
      () => this.vaultRegistry.validateRegistry(),
      { description: 'Validate the vault registry' }
    )
  }

  /**
   * Register initialization endpoints
   */
  private registerInitializationEndpoints(): void {
    // Initialize vault
    this.registerEndpoint('vault', 'initializeVault',
      (vaultName: string, options?: any) => this.vaultService.initializeVault(vaultName, options),
      {
        validator: (vaultName: string, options?: any) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Initialize a vault with default structure'
      }
    )

    // Initialize vault structure
    this.registerEndpoint('vault', 'initializeVaultStructure',
      (vaultPath: string, template?: string) => this.vaultService.initializeVaultStructure(vaultPath, template),
      {
        validator: (vaultPath: string, template?: string) => {
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (template && !this.validateInput(template, 'string', 50)) throw new Error('Invalid template')
        },
        description: 'Initialize vault directory structure'
      }
    )

    // Create vault master file
    this.registerEndpoint('vault', 'createVaultMasterFile',
      (vaultName: string, content?: string) => this.vaultService.createVaultMasterFile(vaultName, content),
      {
        validator: (vaultName: string, content?: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (content && !this.validateInput(content, 'string', 100000)) throw new Error('Invalid content')
        },
        description: 'Create master.md file for vault'
      }
    )
  }

  /**
   * Register scanning endpoints
   */
  private registerScanningEndpoints(): void {
    // Scan vault
    this.registerEndpoint('vault', 'scanVault',
      (vaultName: string, options?: any) => this.vaultService.scanVault(vaultName, options),
      {
        validator: (vaultName: string, options?: any) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Scan vault for files and structure'
      }
    )

    // Scan all vaults
    this.registerEndpoint('vault', 'scanAllVaults',
      (options?: any) => this.vaultService.scanAllVaults(options),
      {
        validator: (options?: any) => {
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Scan all vaults for files and structure'
      }
    )

    // Get vault files
    this.registerEndpoint('vault', 'getVaultFiles',
      (vaultName: string, recursive?: boolean) => this.vaultService.getVaultFiles(vaultName, recursive),
      {
        validator: (vaultName: string, recursive?: boolean) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (recursive !== undefined && !this.validateInput(recursive, 'boolean')) {
            throw new Error('Invalid recursive value')
          }
        },
        description: 'Get files in a vault'
      }
    )

    // Get vault statistics
    this.registerEndpoint('vault', 'getVaultStatistics',
      (vaultName: string) => this.vaultService.getVaultStatistics(vaultName),
      {
        validator: (vaultName: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Get vault statistics (file count, size, etc.)'
      }
    )

    // Validate vault structure
    this.registerEndpoint('vault', 'validateVaultStructure',
      (vaultName: string) => this.vaultService.validateVaultStructure(vaultName),
      {
        validator: (vaultName: string) => {
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
        },
        description: 'Validate vault directory structure'
      }
    )
  }

  protected async onCleanup(): Promise<void> {
    this.log('info', 'Cleaning up Vault API Module')
    // Cleanup vault resources if needed
    if (this.vaultService && typeof this.vaultService.cleanup === 'function') {
      try {
        await this.vaultService.cleanup()
      } catch (error) {
        this.log('error', 'Error cleaning up vault service:', error)
      }
    }
  }
}
