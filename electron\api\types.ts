/**
 * API Types
 * Common type definitions for the API system
 */

// Re-export validation types from validation.ts
export { ValidationSchema, ValidationRule, ValidationResult } from './validation'

// Middleware function type
export interface MiddlewareFunction {
  (context: MiddlewareContext): Promise<void> | void
}

export interface MiddlewareContext {
  event: any
  category: string
  endpoint: string
  args: any[]
  startTime: number
  metadata: Record<string, any>
}

// Module system types
export interface ModuleManifest {
  name: string
  version: string
  description: string
  main: string
  dependencies: string[]
  optionalDependencies: string[]
  category: 'core' | 'optional' | 'plugin'
  loadPriority: LoadPriority
  lazy: boolean
  config?: Record<string, any>
}

export enum LoadPriority {
  CRITICAL = 0,
  HIGH = 1,
  NORMAL = 2,
  LOW = 3,
  LAZY = 4
}

export interface ModuleLoadResult {
  success: boolean
  module?: any
  error?: Error
  loadTime?: number
}
