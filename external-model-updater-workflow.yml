# GitHub Actions Workflow for External Model Updater
# This file should be placed in the external chatlo-model-updater repository
# Path: .github/workflows/update-models.yml

name: Update Model Manifest

on:
  # Run daily at 6 AM UTC
  schedule:
    - cron: '0 6 * * *'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      force_update:
        description: 'Force update even if no changes detected'
        required: false
        default: 'false'
        type: boolean

  # Run on push to main branch (for testing)
  push:
    branches: [ main ]
    paths: 
      - 'modelCrawler.js'
      - 'generateManifest.js'
      - 'updateLogic.ts'

jobs:
  update-models:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run model crawler
      env:
        OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
        FORCE_UPDATE: ${{ github.event.inputs.force_update }}
      run: |
        echo "Starting model crawl..."
        node modelCrawler.js
        
    - name: Check for changes
      id: changes
      run: |
        if git diff --quiet models-manifest.json; then
          echo "No changes detected in manifest"
          echo "has_changes=false" >> $GITHUB_OUTPUT
        else
          echo "Changes detected in manifest"
          echo "has_changes=true" >> $GITHUB_OUTPUT
          
          # Get version from manifest
          VERSION=$(node -p "require('./models-manifest.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          # Get model count
          MODEL_COUNT=$(node -p "require('./models-manifest.json').statistics.total_models")
          echo "model_count=$MODEL_COUNT" >> $GITHUB_OUTPUT
        fi
        
    - name: Commit and push changes
      if: steps.changes.outputs.has_changes == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add models-manifest.json
        git commit -m "Update model manifest to v${{ steps.changes.outputs.version }} (${{ steps.changes.outputs.model_count }} models)"
        git push
        
    - name: Create release
      if: steps.changes.outputs.has_changes == 'true'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.changes.outputs.version }}
        release_name: Model Manifest v${{ steps.changes.outputs.version }}
        body: |
          ## Model Manifest Update
          
          **Version:** ${{ steps.changes.outputs.version }}
          **Total Models:** ${{ steps.changes.outputs.model_count }}
          **Updated:** ${{ github.run_id }}
          
          ### Changes
          - Updated model manifest with latest OpenRouter data
          - Automatic categorization and flagship detection
          - Ready for deployment to ChatLo applications
          
          ### Deployment
          This manifest is automatically deployed to:
          - GitHub Pages: https://chatlo.github.io/chatlo-model-updater/models-manifest.json
          - CDN: https://cdn.chatlo.com/models-manifest.json (if configured)
          
        draft: false
        prerelease: false
        
    - name: Deploy to GitHub Pages
      if: steps.changes.outputs.has_changes == 'true'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: .
        publish_branch: gh-pages
        keep_files: false
        
    - name: Deploy to CDN (Optional)
      if: steps.changes.outputs.has_changes == 'true' && secrets.CDN_DEPLOY_KEY != ''
      run: |
        # Example: Deploy to AWS S3 + CloudFront
        # aws s3 cp models-manifest.json s3://chatlo-cdn/models-manifest.json
        # aws cloudfront create-invalidation --distribution-id $CDN_DISTRIBUTION_ID --paths "/models-manifest.json"
        
        # Example: Deploy to Vercel
        # vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
        
        # Example: Deploy to Netlify
        # netlify deploy --prod --dir . --auth ${{ secrets.NETLIFY_AUTH_TOKEN }}
        
        echo "CDN deployment would happen here"
        
    - name: Notify ChatLo App (Optional)
      if: steps.changes.outputs.has_changes == 'true'
      run: |
        # Optional: Webhook to notify main ChatLo app of model updates
        # curl -X POST "${{ secrets.CHATLO_WEBHOOK_URL }}" \
        #   -H "Content-Type: application/json" \
        #   -d '{"version": "${{ steps.changes.outputs.version }}", "model_count": ${{ steps.changes.outputs.model_count }}}'
        
        echo "App notification would happen here"
        
    - name: Update status badge
      if: always()
      run: |
        # Create status badge data
        if [ "${{ job.status }}" = "success" ]; then
          STATUS="passing"
          COLOR="brightgreen"
        else
          STATUS="failing"
          COLOR="red"
        fi
        
        echo "Status: $STATUS"
        # Badge would be generated here for README

  # Job to validate manifest format
  validate-manifest:
    runs-on: ubuntu-latest
    needs: update-models
    if: needs.update-models.outputs.has_changes == 'true'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Validate manifest format
      run: |
        echo "Validating manifest format..."
        node -e "
          const manifest = require('./models-manifest.json');
          
          // Validate required fields
          if (!manifest.version) throw new Error('Missing version');
          if (!manifest.models) throw new Error('Missing models array');
          if (!manifest.statistics) throw new Error('Missing statistics');
          
          // Validate model structure
          manifest.models.forEach((model, i) => {
            if (!model.id) throw new Error(\`Model \${i} missing id\`);
            if (!model.name) throw new Error(\`Model \${i} missing name\`);
            if (!model.pricing) throw new Error(\`Model \${i} missing pricing\`);
          });
          
          console.log('✅ Manifest validation passed');
          console.log(\`📊 Version: \${manifest.version}\`);
          console.log(\`📊 Models: \${manifest.models.length}\`);
          console.log(\`📊 Flagship: \${manifest.statistics.flagship_models}\`);
        "
        
    - name: Test manifest loading
      run: |
        echo "Testing manifest loading..."
        curl -f -s "https://raw.githubusercontent.com/${{ github.repository }}/main/models-manifest.json" > /dev/null
        echo "✅ Manifest is accessible via GitHub raw URL"
