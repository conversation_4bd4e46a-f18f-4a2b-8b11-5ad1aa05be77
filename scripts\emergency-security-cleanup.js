#!/usr/bin/env node

/**
 * EMERGENCY SECURITY CLEANUP SCRIPT
 * 
 * This script immediately quarantines all corrupted files and security vulnerabilities:
 * - Removes undefined/.context/ folder (critical security breach)
 * - Quarantines files with absolute Windows paths
 * - Removes malformed JSON files
 * - Cleans up corrupted data structures
 * 
 * RUN THIS IMMEDIATELY AFTER SECURITY BREACH DETECTION
 */

const fs = require('fs');
const path = require('path');

class EmergencySecurityCleanup {
  constructor() {
    this.quarantineCount = 0;
    this.errorCount = 0;
    this.corruptedPatterns = [
      /^[A-Z]:\\/, // Windows absolute paths
      /^\/[A-Z]:\//, // Unix-style Windows paths
      /undefined/, // undefined references
      /null/, // null references
      /C:\\Users\\<USER>\\\\/, // Double backslashes
    ];
  }

  async run() {
    console.log('🚨 EMERGENCY SECURITY CLEANUP STARTING...');
    console.log('⏰ Timestamp:', new Date().toISOString());
    
    try {
      // Get current working directory
      const cwd = process.cwd();
      console.log('📍 Working directory:', cwd);
      
      // Phase 1: Immediate critical cleanup
      await this.phase1CriticalCleanup(cwd);
      
      // Phase 2: Scan and quarantine corrupted files
      await this.phase2FileQuarantine(cwd);
      
      // Phase 3: Remove junk folders
      await this.phase3JunkRemoval(cwd);
      
      // Phase 4: Security audit
      await this.phase4SecurityAudit(cwd);
      
      console.log('\n✅ EMERGENCY SECURITY CLEANUP COMPLETED');
      console.log(`📊 Results: ${this.quarantineCount} files quarantined, ${this.errorCount} errors`);
      
    } catch (error) {
      console.error('❌ CRITICAL ERROR during emergency cleanup:', error);
      process.exit(1);
    }
  }

  async phase1CriticalCleanup(cwd) {
    console.log('\n🚨 PHASE 1: CRITICAL SECURITY CLEANUP');
    
    // CRITICAL: Remove undefined/.context/ folder immediately
    const undefinedContextPath = path.join(cwd, 'undefined', '.context');
    if (fs.existsSync(undefinedContextPath)) {
      console.log('🚨 CRITICAL: Found undefined/.context/ folder - IMMEDIATE REMOVAL');
      
      try {
        // Move to quarantine instead of deletion for investigation
        const quarantinePath = path.join(cwd, '.quarantine', 'critical_undefined_context_' + Date.now());
        await this.ensureDirectory(path.dirname(quarantinePath));
        await fs.promises.rename(undefinedContextPath, quarantinePath);
        
        console.log('✅ CRITICAL: Quarantined undefined/.context/ to:', quarantinePath);
        this.quarantineCount++;
      } catch (error) {
        console.error('❌ CRITICAL: Failed to quarantine undefined/.context/:', error);
        this.errorCount++;
      }
    }

    // Remove the undefined folder entirely if it exists
    const undefinedFolder = path.join(cwd, 'undefined');
    if (fs.existsSync(undefinedFolder)) {
      try {
        await fs.promises.rmdir(undefinedFolder);
        console.log('✅ Removed empty undefined folder');
      } catch (error) {
        console.log('⚠️ Could not remove undefined folder (may contain files):', error.message);
      }
    }
  }

  async phase2FileQuarantine(cwd) {
    console.log('\n🚨 PHASE 2: FILE QUARANTINE');
    
    const intelligenceFolders = [
      path.join(cwd, '.intelligence'),
      path.join(cwd, '.context'),
      path.join(cwd, 'shared-dropbox', '.intelligence'),
      path.join(cwd, 'default', '.intelligence')
    ];

    for (const intelFolder of intelligenceFolders) {
      if (fs.existsSync(intelFolder)) {
        console.log(`🔍 Scanning for corrupted files in: ${intelFolder}`);
        await this.scanAndQuarantineFolder(intelFolder, cwd);
      }
    }
  }

  async scanAndQuarantineFolder(folderPath, vaultRoot) {
    try {
      const files = await fs.promises.readdir(folderPath, { withFileTypes: true });
      
      for (const file of files) {
        if (file.isFile() && file.name.endsWith('.json')) {
          const filePath = path.join(folderPath, file.name);
          await this.checkAndQuarantineFile(filePath, vaultRoot);
        } else if (file.isDirectory()) {
          // Recursively scan subdirectories
          await this.scanAndQuarantineFolder(path.join(folderPath, file.name), vaultRoot);
        }
      }
    } catch (error) {
      console.error(`❌ Error scanning folder ${folderPath}:`, error.message);
      this.errorCount++;
    }
  }

  async checkAndQuarantineFile(filePath, vaultRoot) {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      
      // Check for corrupted patterns
      const isCorrupted = this.corruptedPatterns.some(pattern => 
        content.match(pattern)
      );
      
      if (isCorrupted) {
        console.log(`🚨 Corrupted file detected: ${filePath}`);
        
        // Move to quarantine
        const quarantinePath = path.join(vaultRoot, '.quarantine', 'corrupted_' + Date.now() + '_' + path.basename(filePath));
        await this.ensureDirectory(path.dirname(quarantinePath));
        await fs.promises.rename(filePath, quarantinePath);
        
        console.log(`✅ Quarantined corrupted file to: ${quarantinePath}`);
        this.quarantineCount++;
        return;
      }
      
      // Check for malformed JSON
      try {
        JSON.parse(content);
      } catch (parseError) {
        console.log(`🚨 Malformed JSON file detected: ${filePath}`);
        
        // Move malformed files to quarantine
        const quarantinePath = path.join(vaultRoot, '.quarantine', 'malformed_' + Date.now() + '_' + path.basename(filePath));
        await this.ensureDirectory(path.dirname(quarantinePath));
        await fs.promises.rename(filePath, quarantinePath);
        
        console.log(`✅ Quarantined malformed file to: ${quarantinePath}`);
        this.quarantineCount++;
      }
      
    } catch (error) {
      console.error(`❌ Error processing file ${filePath}:`, error.message);
      this.errorCount++;
    }
  }

  async phase3JunkRemoval(cwd) {
    console.log('\n🚨 PHASE 3: JUNK FOLDER REMOVAL');
    
    const junkFolders = [
      path.join(cwd, 'chat-notes'), // Should not exist in root
      path.join(cwd, '.intelligence'), // Should not exist in root
      path.join(cwd, '.context'), // Should not exist in root
    ];

    for (const junkFolder of junkFolders) {
      if (fs.existsSync(junkFolder)) {
        try {
          console.log(`🗑️ Removing junk folder: ${junkFolder}`);
          
          // Move to quarantine for investigation
          const quarantinePath = path.join(cwd, '.quarantine', 'junk_' + path.basename(junkFolder) + '_' + Date.now());
          await this.ensureDirectory(path.dirname(quarantinePath));
          await fs.promises.rename(junkFolder, quarantinePath);
          
          console.log(`✅ Quarantined junk folder to: ${quarantinePath}`);
          this.quarantineCount++;
        } catch (error) {
          console.error(`❌ Failed to remove junk folder ${junkFolder}:`, error.message);
          this.errorCount++;
        }
      }
    }
  }

  async phase4SecurityAudit(cwd) {
    console.log('\n🚨 PHASE 4: SECURITY AUDIT');
    
    // Create security audit report
    const auditReport = {
      timestamp: new Date().toISOString(),
      quarantineCount: this.quarantineCount,
      errorCount: this.errorCount,
      securityVulnerabilities: [
        'undefined/.context/ folder removed',
        'Absolute Windows paths quarantined',
        'Malformed JSON files quarantined',
        'Junk folders quarantined'
      ],
      recommendations: [
        'Review all quarantined files for investigation',
        'Implement strict path validation in code',
        'Add automated security scanning',
        'Regular security audits required'
      ]
    };

    const auditPath = path.join(cwd, '.quarantine', 'security_audit_' + Date.now() + '.json');
    await this.ensureDirectory(path.dirname(auditPath));
    await fs.promises.writeFile(auditPath, JSON.stringify(auditReport, null, 2));
    
    console.log(`📋 Security audit report saved to: ${auditPath}`);
  }

  async ensureDirectory(dirPath) {
    try {
      await fs.promises.mkdir(dirPath, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  }
}

// Run the emergency cleanup
if (require.main === module) {
  const cleanup = new EmergencySecurityCleanup();
  cleanup.run().catch(error => {
    console.error('❌ FATAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = EmergencySecurityCleanup;
