#!/usr/bin/env node

/**
 * IMMEDIATE SECURITY FIX SCRIPT
 * 
 * This script immediately quarantines the security breach folders:
 * - .intelligence folder in project root
 * - C_ folder in project root
 * 
 * RUN THIS IMMEDIATELY to fix the security vulnerability
 */

const fs = require('fs');
const path = require('path');

class ImmediateSecurityFix {
  constructor() {
    this.quarantineCount = 0;
    this.errorCount = 0;
  }

  async run() {
    console.log('🚨 IMMEDIATE SECURITY FIX STARTING...');
    console.log('⏰ Timestamp:', new Date().toISOString());
    
    try {
      // Get current working directory (project root)
      const projectRoot = process.cwd();
      console.log('📍 Project root:', projectRoot);
      
      // Create quarantine directory
      const quarantineDir = path.join(projectRoot, '.quarantine');
      if (!fs.existsSync(quarantineDir)) {
        fs.mkdirSync(quarantineDir, { recursive: true });
        console.log('✅ Created quarantine directory');
      }
      
      // Phase 1: Quarantine .intelligence folder in project root
      await this.quarantineRootIntelligence(projectRoot, quarantineDir);
      
      // Phase 2: Quarantine C_ folder in project root
      await this.quarantineRootCPath(projectRoot, quarantineDir);
      
      // Phase 3: Security audit
      await this.securityAudit(projectRoot);
      
      console.log('\n✅ IMMEDIATE SECURITY FIX COMPLETED');
      console.log(`📊 Results: ${this.quarantineCount} folders quarantined, ${this.errorCount} errors`);
      
    } catch (error) {
      console.error('❌ CRITICAL ERROR during security fix:', error);
      process.exit(1);
    }
  }

  async quarantineRootIntelligence(projectRoot, quarantineDir) {
    console.log('\n🚨 PHASE 1: Quarantining root .intelligence folder');
    
    const rootIntelligencePath = path.join(projectRoot, '.intelligence');
    if (fs.existsSync(rootIntelligencePath)) {
      console.log('🚨 CRITICAL: Found .intelligence folder in project root - IMMEDIATE QUARANTINE');
      
      try {
        const quarantinePath = path.join(quarantineDir, 'root_intelligence_breach_' + Date.now());
        await fs.promises.rename(rootIntelligencePath, quarantinePath);
        
        console.log('✅ Quarantined root .intelligence folder to:', quarantinePath);
        this.quarantineCount++;
      } catch (error) {
        console.error('❌ Failed to quarantine root .intelligence folder:', error);
        this.errorCount++;
      }
    } else {
      console.log('✅ No root .intelligence folder found');
    }
  }

  async quarantineRootCPath(projectRoot, quarantineDir) {
    console.log('\n🚨 PHASE 2: Quarantining root C_ folder');
    
    const rootCPath = path.join(projectRoot, 'C_');
    if (fs.existsSync(rootCPath)) {
      console.log('🚨 CRITICAL: Found C_ folder in project root - IMMEDIATE QUARANTINE');
      
      try {
        const quarantinePath = path.join(quarantineDir, 'root_c_path_breach_' + Date.now());
        await fs.promises.rename(rootCPath, quarantinePath);
        
        console.log('✅ Quarantined root C_ folder to:', quarantinePath);
        this.quarantineCount++;
      } catch (error) {
        console.error('❌ Failed to quarantine root C_ folder:', error);
        this.errorCount++;
      }
    } else {
      console.log('✅ No root C_ folder found');
    }
  }

  async securityAudit(projectRoot) {
    console.log('\n🔍 PHASE 3: Security Audit');
    
    // Check for other suspicious folders
    const suspiciousFolders = [
      'undefined',
      'null',
      'shared-dropbox',
      'default',
      'mock-vaults'
    ];
    
    for (const folder of suspiciousFolders) {
      const folderPath = path.join(projectRoot, folder);
      if (fs.existsSync(folderPath)) {
        console.log(`⚠️  Found suspicious folder: ${folder}`);
        
        // Check if it contains .intelligence or .context
        const intelligencePath = path.join(folderPath, '.intelligence');
        const contextPath = path.join(folderPath, '.context');
        
        if (fs.existsSync(intelligencePath) || fs.existsSync(contextPath)) {
          console.log(`🚨 CRITICAL: ${folder} contains intelligence data outside vault boundaries`);
        }
      }
    }
    
    // Check for any remaining .intelligence folders in project root
    const items = fs.readdirSync(projectRoot, { withFileTypes: true });
    for (const item of items) {
      if (item.isDirectory() && item.name.startsWith('.intelligence')) {
        console.log(`🚨 CRITICAL: Found additional .intelligence folder: ${item.name}`);
      }
    }
  }
}

// Run the security fix immediately
const securityFix = new ImmediateSecurityFix();
securityFix.run().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
