#!/usr/bin/env node

/**
 * Emergency Quarantine Script
 * Moves corrupted escape folders into .quarantine directory
 * 
 * Usage: node scripts/quarantine-corrupted-folders.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const PROJECT_ROOT = process.cwd();
const QUARANTINE_DIR = path.join(PROJECT_ROOT, '.quarantine');
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');

// Folders to quarantine (corrupted escape folders)
const CORRUPTED_FOLDERS = [
  '.intelligence',  // Security breach - intelligence folder in project root
  'C_'             // Corrupted drive letter causing path injection
];

console.log('🚨 [QUARANTINE] Starting emergency quarantine of corrupted escape folders...');
console.log(`📁 Project root: ${PROJECT_ROOT}`);
console.log(`🏥 Quarantine directory: ${QUARANTINE_DIR}`);

async function ensureQuarantineDirectory() {
  try {
    if (!fs.existsSync(QUARANTINE_DIR)) {
      await fs.promises.mkdir(QUARANTINE_DIR, { recursive: true });
      console.log('✅ Created quarantine directory');
    } else {
      console.log('✅ Quarantine directory already exists');
    }
  } catch (error) {
    console.error('❌ Failed to create quarantine directory:', error);
    throw error;
  }
}

async function quarantineFolder(folderName) {
  const sourcePath = path.join(PROJECT_ROOT, folderName);
  const quarantinePath = path.join(QUARANTINE_DIR, `${folderName}_quarantined_${TIMESTAMP}`);
  
  try {
    if (!fs.existsSync(sourcePath)) {
      console.log(`⚠️ Folder ${folderName} does not exist, skipping...`);
      return false;
    }
    
    // Check if it's a directory
    const stats = await fs.promises.stat(sourcePath);
    if (!stats.isDirectory()) {
      console.log(`⚠️ ${folderName} is not a directory, skipping...`);
      return false;
    }
    
    console.log(`🚨 Quarantining corrupted folder: ${folderName}`);
    console.log(`   Source: ${sourcePath}`);
    console.log(`   Destination: ${quarantinePath}`);
    
    // Move folder to quarantine
    await fs.promises.rename(sourcePath, quarantinePath);
    
    console.log(`✅ Successfully quarantined: ${folderName}`);
    return true;
    
  } catch (error) {
    console.error(`❌ Failed to quarantine ${folderName}:`, error);
    return false;
  }
}

async function main() {
  try {
    console.log('\n🔍 [QUARANTINE] Scanning for corrupted folders...');
    
    // Ensure quarantine directory exists
    await ensureQuarantineDirectory();
    
    let quarantinedCount = 0;
    let errorCount = 0;
    
    // Quarantine each corrupted folder
    for (const folder of CORRUPTED_FOLDERS) {
      const success = await quarantineFolder(folder);
      if (success) {
        quarantinedCount++;
      } else {
        errorCount++;
      }
    }
    
    console.log('\n📊 [QUARANTINE] Quarantine Summary:');
    console.log(`   ✅ Successfully quarantined: ${quarantinedCount} folders`);
    console.log(`   ❌ Failed to quarantine: ${errorCount} folders`);
    
    if (quarantinedCount > 0) {
      console.log('\n🎉 [QUARANTINE] Emergency quarantine completed successfully!');
      console.log('   Corrupted escape folders have been moved to .quarantine directory');
      console.log('   The application should now be safe from path injection attacks');
    } else {
      console.log('\n⚠️ [QUARANTINE] No folders were quarantined');
    }
    
    // List quarantine contents
    if (fs.existsSync(QUARANTINE_DIR)) {
      console.log('\n📋 [QUARANTINE] Quarantine directory contents:');
      const quarantineContents = await fs.promises.readdir(QUARANTINE_DIR);
      if (quarantineContents.length === 0) {
        console.log('   (empty)');
      } else {
        quarantineContents.forEach(item => {
          console.log(`   📁 ${item}`);
        });
      }
    }
    
  } catch (error) {
    console.error('\n💥 [QUARANTINE] Emergency quarantine failed:', error);
    process.exit(1);
  }
}

// Run quarantine
if (require.main === module) {
  main();
}

module.exports = { main, quarantineFolder };
