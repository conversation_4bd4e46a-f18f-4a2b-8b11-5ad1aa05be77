#!/usr/bin/env node

/**
 * SECURITY MONITORING SCRIPT
 * 
 * Continuous monitoring for security vulnerabilities:
 * - Watches for new corrupted files
 * - Monitors for unauthorized path access
 * - Validates data integrity
 * - Logs security events
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('fs');

class SecurityMonitor {
  constructor() {
    this.securityEvents = [];
    this.corruptedPatterns = [
      /^[A-Z]:\\/, // Windows absolute paths
      /^\/[A-Z]:\//, // Unix-style Windows paths
      /undefined/, // undefined references
      /null/, // null references
      /C:\\Users\\<USER>\\\\/, // Double backslashes
    ];
    this.watchedPaths = [];
    this.isMonitoring = false;
  }

  async startMonitoring() {
    console.log('🛡️ Starting Security Monitoring...');
    
    try {
      // Get current working directory
      const cwd = process.cwd();
      
      // Define paths to monitor
      this.watchedPaths = [
        path.join(cwd, '.intelligence'),
        path.join(cwd, '.context'),
        path.join(cwd, 'shared-dropbox'),
        path.join(cwd, 'default'),
        path.join(cwd, 'mock-vaults')
      ];

      // Start file system watching
      await this.startFileWatching();
      
      // Start periodic security scans
      this.startPeriodicScans();
      
      // Start security event logging
      this.startEventLogging();
      
      this.isMonitoring = true;
      console.log('✅ Security monitoring active');
      
    } catch (error) {
      console.error('❌ Failed to start security monitoring:', error);
      process.exit(1);
    }
  }

  async startFileWatching() {
    console.log('👁️ Setting up file system monitoring...');
    
    // Monitor for new files in intelligence folders
    for (const watchPath of this.watchedPaths) {
      if (fs.existsSync(watchPath)) {
        this.watchDirectory(watchPath);
      }
    }
  }

  watchDirectory(dirPath) {
    try {
      fs.watch(dirPath, { recursive: true }, (eventType, filename) => {
        if (filename && filename.endsWith('.json')) {
          this.handleFileEvent(eventType, path.join(dirPath, filename));
        }
      });
      console.log(`👁️ Monitoring directory: ${dirPath}`);
    } catch (error) {
      console.warn(`⚠️ Could not watch directory ${dirPath}:`, error.message);
    }
  }

  async handleFileEvent(eventType, filePath) {
    try {
      if (eventType === 'rename' && fs.existsSync(filePath)) {
        // New file created
        await this.validateNewFile(filePath);
      }
    } catch (error) {
      console.error(`❌ Error handling file event for ${filePath}:`, error);
    }
  }

  async validateNewFile(filePath) {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      
      // Check for security violations
      const violations = this.detectSecurityViolations(content, filePath);
      
      if (violations.length > 0) {
        console.log(`🚨 SECURITY VIOLATION DETECTED in ${filePath}:`, violations);
        
        // Quarantine the file immediately
        await this.quarantineFile(filePath, violations);
        
        // Log security event
        this.logSecurityEvent('FILE_VIOLATION', {
          filePath,
          violations,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`❌ Error validating new file ${filePath}:`, error);
    }
  }

  detectSecurityViolations(content, filePath) {
    const violations = [];
    
    // Skip checking file paths - only check content
    // The filePath parameter is just for logging, not for validation
    
    try {
      // Parse JSON content to check for violations
      const data = JSON.parse(content);
      
      // Check for absolute Windows paths in content
      if (typeof data === 'object' && data !== null) {
        const checkForViolations = (obj, path = '') => {
          for (const [key, value] of Object.entries(obj)) {
            const currentPath = path ? `${path}.${key}` : key;
            
            if (typeof value === 'string') {
              // Check for absolute Windows paths
              if (/^[A-Z]:\\/.test(value)) {
                violations.push(`Absolute Windows path detected in ${currentPath}: ${value}`);
              }
              
              // Check for Unix-style Windows paths
              if (/^\/[A-Z]:\//.test(value)) {
                violations.push(`Unix-style Windows path detected in ${currentPath}: ${value}`);
              }
              
              // Check for undefined/null references
              if (value === 'undefined' || value === 'null') {
                violations.push(`Undefined/null reference detected in ${currentPath}: ${value}`);
              }
              
              // Check for user directory paths
              if (/C:\\Users\\<USER>\\\\/.test(value)) {
                violations.push(`Double backslash detected in ${currentPath}: ${value}`);
              }
            } else if (typeof value === 'object' && value !== null) {
              checkForViolations(value, currentPath);
            }
          }
        };
        
        checkForViolations(data);
      }
    } catch (parseError) {
      // If JSON parsing fails, check for violations in raw content
      if (/^[A-Z]:\\/.test(content)) {
        violations.push('Absolute Windows path detected in raw content');
      }
      if (/^\/[A-Z]:\//.test(content)) {
        violations.push('Unix-style Windows path detected in raw content');
      }
      if (/undefined/.test(content)) {
        violations.push('Undefined reference detected in raw content');
      }
      if (/null/.test(content)) {
        violations.push('Null reference detected in raw content');
      }
      if (/C:\\Users\\<USER>\\\\/.test(content)) {
        violations.push('Double backslash detected in raw content');
      }
    }
    
    return violations;
  }

  async quarantineFile(filePath, violations) {
    try {
      const quarantinePath = path.join(
        path.dirname(filePath), 
        '.quarantine', 
        'security_violation_' + Date.now() + '_' + path.basename(filePath)
      );
      
      await this.ensureDirectory(path.dirname(quarantinePath));
      await fs.promises.rename(filePath, quarantinePath);
      
      console.log(`✅ File quarantined to: ${quarantinePath}`);
      
    } catch (error) {
      console.error(`❌ Failed to quarantine file ${filePath}:`, error);
    }
  }

  startPeriodicScans() {
    console.log('🔍 Starting periodic security scans...');
    
    // Scan every 5 minutes
    setInterval(async () => {
      await this.performSecurityScan();
    }, 5 * 60 * 1000);
  }

  async performSecurityScan() {
    try {
      console.log('🔍 Performing periodic security scan...');
      
      for (const scanPath of this.watchedPaths) {
        if (fs.existsSync(scanPath)) {
          await this.scanDirectory(scanPath);
        }
      }
      
      console.log('✅ Periodic security scan completed');
      
    } catch (error) {
      console.error('❌ Error during periodic security scan:', error);
    }
  }

  async scanDirectory(dirPath) {
    try {
      const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      for (const file of files) {
        if (file.isFile() && file.name.endsWith('.json')) {
          const filePath = path.join(dirPath, file.name);
          await this.validateExistingFile(filePath);
        } else if (file.isDirectory()) {
          await this.scanDirectory(path.join(dirPath, file.name));
        }
      }
    } catch (error) {
      console.error(`❌ Error scanning directory ${dirPath}:`, error);
    }
  }

  async validateExistingFile(filePath) {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      const violations = this.detectSecurityViolations(content, filePath);
      
      if (violations.length > 0) {
        console.log(`🚨 EXISTING FILE VIOLATION in ${filePath}:`, violations);
        
        // Log security event
        this.logSecurityEvent('EXISTING_VIOLATION', {
          filePath,
          violations,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`❌ Error validating existing file ${filePath}:`, error);
    }
  }

  startEventLogging() {
    console.log('📝 Starting security event logging...');
    
    // Log events every minute
    setInterval(() => {
      this.flushSecurityEvents();
    }, 60 * 1000);
  }

  logSecurityEvent(type, data) {
    this.securityEvents.push({
      type,
      data,
      timestamp: new Date().toISOString()
    });
    
    // Keep only last 1000 events
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }
  }

  async flushSecurityEvents() {
    if (this.securityEvents.length === 0) return;
    
    try {
      const logPath = path.join(process.cwd(), '.quarantine', 'security_events.log');
      await this.ensureDirectory(path.dirname(logPath));
      
      const logEntry = {
        timestamp: new Date().toISOString(),
        events: this.securityEvents
      };
      
      await fs.promises.appendFile(logPath, JSON.stringify(logEntry) + '\n');
      
      // Clear events after logging
      this.securityEvents = [];
      
    } catch (error) {
      console.error('❌ Error flushing security events:', error);
    }
  }

  async ensureDirectory(dirPath) {
    try {
      await fs.promises.mkdir(dirPath, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  }

  stopMonitoring() {
    this.isMonitoring = false;
    console.log('🛑 Security monitoring stopped');
  }
}

// Run the security monitor
if (require.main === module) {
  const monitor = new SecurityMonitor();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down security monitor...');
    monitor.stopMonitoring();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down security monitor...');
    monitor.stopMonitoring();
    process.exit(0);
  });
  
  monitor.startMonitoring().catch(error => {
    console.error('❌ FATAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = SecurityMonitor;
