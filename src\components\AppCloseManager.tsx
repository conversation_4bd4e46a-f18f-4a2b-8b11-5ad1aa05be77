import React, { useEffect, useState } from 'react'
import ModalConfirmClose from './common/ModalConfirmClose'

// Lightweight manager that listens to close requests and shows a modal
const AppCloseManager: React.FC = () => {
  const [open, setOpen] = useState(false)
  const [variant, setVariant] = useState<'local' | 'usb'>('local')

  useEffect(() => {
    const handler = async () => {
      try {
        // Check portable mode flag
        const enabled = await window.electronAPI.settings.get('portable-mode-enabled')
        setVariant(enabled ? 'usb' : 'local')
      } catch {}
      setOpen(true)
    }

    window.addEventListener('chatlo:request-close', handler as any)
    return () => window.removeEventListener('chatlo:request-close', handler as any)
  }, [])

  const onCancel = () => setOpen(false)

  const onConfirm = async () => {
    try {
      // Let main process handle the shutdown flow (includes USB preparation)
      window.electronAPI.windowControls?.confirmClose?.() ?? window.electronAPI.windowControls?.close()
    } finally {
      setOpen(false)
    }
  }

  return (
    <ModalConfirmClose variant={variant} open={open} onCancel={onCancel} onConfirm={onConfirm} />
  )
}

export default AppCloseManager

