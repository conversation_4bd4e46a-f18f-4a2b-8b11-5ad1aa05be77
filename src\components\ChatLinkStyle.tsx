import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { navigationManager } from '../services/navigationService'

interface ChatLinkStyleProps {
  filename: string
  filePath: string
  vaultName?: string
  onClick?: (e: React.MouseEvent) => void
  className?: string
}

/**
 * Chat Link Style Component
 * 
 * Renders filenames in the distinctive "chat link style" that matches
 * the current chat interface design. When clicked, it opens the
 * FilePageOverlay for file intelligence analysis using the navigation service.
 */
export const ChatLinkStyle: React.FC<ChatLinkStyleProps> = ({
  filename,
  filePath,
  vaultName,
  onClick,
  className = ''
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    // Use navigation service to open file overlay
    navigationManager.openFileOverlay(filePath, filename, {
      page: 'chat',
      context: vaultName
    })
    
    // Call custom onClick if provided (for additional side effects)
    onClick?.(e)
  }

  return (
    <span className={`inline-flex items-center gap-2 ${className}`}>
      <FontAwesomeIcon 
        icon={ICONS.file} 
        className="text-primary text-sm flex-shrink-0" 
      />
      <button
        onClick={handleClick}
        className="text-primary hover:text-primary/80 underline decoration-primary/30 hover:decoration-primary/60 transition-all duration-200 font-medium cursor-pointer"
        title={`Click to open ${filename} in FilePageOverlay`}
      >
        {filename}
      </button>
      {vaultName && vaultName !== 'Shared Dropbox' && (
        <span className="text-xs text-gray-400 opacity-75">
          ({vaultName})
        </span>
      )}
    </span>
  )
}

/**
 * Chat Link Style with Back Navigation Context
 * 
 * Enhanced version that includes context about where the link was clicked
 * for proper navigation back to chat.
 */
export const ChatLinkStyleWithContext: React.FC<ChatLinkStyleProps & {
  chatMessageId?: string
  conversationId?: string
}> = ({
  filename,
  filePath,
  vaultName,
  chatMessageId,
  conversationId,
  onClick,
  className = ''
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    // Use navigation service with full chat context
    navigationManager.openFileOverlay(filePath, filename, {
      page: 'chat',
      context: vaultName,
      chatMessageId,
      conversationId
    })
    
    // Call custom onClick if provided (for additional side effects)
    onClick?.(e)
  }

  return (
    <ChatLinkStyle
      filename={filename}
      filePath={filePath}
      vaultName={vaultName}
      onClick={handleClick}
      className={className}
    />
  )
}
