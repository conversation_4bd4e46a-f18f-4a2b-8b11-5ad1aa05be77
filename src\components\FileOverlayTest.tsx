import React from 'react'
import { ChatLinkStyle } from './ChatLinkStyle'

/**
 * Test component to demonstrate the file overlay navigation system
 * 
 * This component shows how ChatLinkStyle components can trigger
 * the FilePageOverlay through the navigation service.
 */
export const FileOverlayTest: React.FC = () => {
  return (
    <div className="p-6 bg-gray-800 rounded-lg">
      <h2 className="text-xl font-bold mb-4 text-white">File Overlay Navigation Test</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2 text-gray-200">Chat Context Links</h3>
          <div className="space-y-2">
            <ChatLinkStyle
              filename="example-document.md"
              filePath="/path/to/example-document.md"
              vaultName="personal-vault"
            />
            
            <ChatLinkStyleWithContext
              filename="research-notes.pdf"
              filePath="/path/to/research-notes.pdf"
              vaultName="work-vault"
              chatMessageId="msg-123"
              conversationId="conv-456"
            />
          </div>
        </div>
        
        <div className="text-sm text-gray-400">
          <p>Click on any file link above to test the navigation system.</p>
          <p>The FilePageOverlay should open with proper context preservation.</p>
        </div>
      </div>
    </div>
  )
}

/**
 * Enhanced version with full chat context
 */
const ChatLinkStyleWithContext: React.FC<{
  filename: string
  filePath: string
  vaultName?: string
  chatMessageId: string
  conversationId: string
}> = ({ filename, filePath, vaultName, chatMessageId, conversationId }) => {
  return (
    <ChatLinkStyle
      filename={filename}
      filePath={filePath}
      vaultName={vaultName}
      chatMessageId={chatMessageId}
      conversationId={conversationId}
    />
  )
}
