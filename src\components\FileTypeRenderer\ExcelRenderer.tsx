/**
 * ExcelRenderer - Excel Spreadsheet File Type Plugin
 * 
 * YOLO Phase 4: Extracted from FilePageOverlay
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from '../Icons/index';
import { FileRenderProps } from './index';

export const ExcelRenderer: React.FC<FileRenderProps> = ({
  content,
  fileTypeInfo,
  filePath,
  fileName
}) => {
  const [showSystemAppPrompt, setShowSystemAppPrompt] = useState(false);
  const [processedContent, setProcessedContent] = useState<string>('');
  const [isBinary, setIsBinary] = useState(false);

  // Process content to handle encoding issues
  useEffect(() => {
    if (content) {
      try {
        // If content looks like extracted text (not base64), trust it and display
        const looksLikeExtractedText = content.length > 100 && 
          content.includes(' ') && 
          content.includes('\n') &&
          !/^[A-Za-z0-9+/=\r\n]+$/.test(content);
        
        if (looksLikeExtractedText) {
          // This is extracted text from the kernel processor - display it
          setIsBinary(false);
          setProcessedContent(content);
          return;
        }
        
        // Legacy binary detection for raw files (fallback)
        const looksBase64 = /^[A-Za-z0-9+/=\r\n]+$/.test(content) && content.length % 4 === 0;
        if (looksBase64) {
          const sample = content.slice(0, 2000);
          let bin = '';
          try { bin = atob(sample.replace(/\r|\n/g, '')); } catch { /* ignore */ }
          if (bin) {
            const header = bin.slice(0, 4);
            const nonPrintable = (bin.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g) || []).length;
            if (header.includes('PK') || nonPrintable > bin.length * 0.05) {
              setIsBinary(true);
              setProcessedContent('');
              return;
            }
          }
        } else {
          // If not base64, consider it binary when many non-printables present or typical OOXML markers appear
          const nonPrintable = (content.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g) || []).length;
          if (nonPrintable > content.length * 0.01 || content.includes('[Content_Types].xml') || content.includes('PK')) {
            setIsBinary(true);
            setProcessedContent('');
            return;
          }
        }

        // Otherwise try to present as plain text (CSV, TSV etc.)
        let processed = content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        setIsBinary(false);
        setProcessedContent(processed);
      } catch (error) {
        console.error('Error processing Excel content:', error);
        setIsBinary(true);
        setProcessedContent('');
      }
    } else {
      setIsBinary(false);
      setProcessedContent('');
    }
  }, [content]);

  const handleOpenInSystemApp = async () => {
    try {
      if (window.electronAPI?.shell?.openPath) {
        await window.electronAPI.shell.openPath(filePath);
      } else {
        console.error('No method available to open file in system app');
        setShowSystemAppPrompt(true);
      }
    } catch (error) {
      console.error('Failed to open Excel spreadsheet in system app:', error);
      setShowSystemAppPrompt(true);
    }
  };



  return (
    <div className="flex flex-col items-center justify-center h-full p-6">
      {/* File Header */}
      <div className="text-center mb-8">
        <div className="w-20 h-20 bg-green-600/20 rounded-2xl flex items-center justify-center mb-4 mx-auto">
          <FontAwesomeIcon icon={ICONS.fileExcel} className="text-green-500 text-3xl" />
        </div>
        <h1 className="text-2xl font-bold text-supplement1 mb-2">{fileName}</h1>
        <p className="text-gray-400">{fileTypeInfo?.displayName || 'Excel Spreadsheet'}</p>
        <p className="text-sm text-gray-500 mt-1">{filePath}</p>
      </div>

      {/* Content Preview */}
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl mb-6">
        <div className="flex items-center gap-3 mb-4">
          <FontAwesomeIcon icon={ICONS.infoCircle} className="text-green-400" />
          <h3 className="text-lg font-semibold text-supplement1">Spreadsheet Preview</h3>
        </div>
        
        {!isBinary && processedContent && processedContent.length > 0 ? (
          <div className="bg-gray-900 rounded p-4 max-h-64 overflow-y-auto">
            <p className="text-gray-300 text-sm leading-relaxed">
              {processedContent.length > 500
                ? `${processedContent.substring(0, 500)}...`
                : processedContent
              }
            </p>
            {processedContent.length > 500 && (
              <p className="text-gray-500 text-xs mt-2">
                Content truncated. Full spreadsheet available for download.
              </p>
            )}
          </div>
        ) : (
          <div className="bg-gray-900 rounded p-4 text-center">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-yellow-400 text-2xl mb-2" />
            <p className="text-gray-400">Preview not available</p>
            <p className="text-sm text-gray-500 mt-1">
              {content && content.length > 0
                ? 'This appears to be a binary Excel file. Use the buttons below to open in Excel or download.'
                : 'Content extraction failed or file is empty'
              }
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <button
          onClick={handleOpenInSystemApp}
          className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <FontAwesomeIcon icon={ICONS.externalLink} className="text-sm" />
          Open in Excel
        </button>
        

      </div>

      {/* System App Prompt */}
      {showSystemAppPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md mx-4">
            <div className="text-center mb-4">
              <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-yellow-400 text-3xl mb-3" />
              <h3 className="text-lg font-semibold text-supplement1 mb-2">System App Required</h3>
              <p className="text-gray-400 text-sm">
                To view this Excel spreadsheet, you need Microsoft Excel or a compatible application installed on your system.
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowSystemAppPrompt(false)}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
              >
                Close
              </button>

            </div>
          </div>
        </div>
      )}
    </div>
  );
};
