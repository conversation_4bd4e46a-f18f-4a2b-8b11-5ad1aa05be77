/**
 * PowerPointRenderer - PowerPoint Presentation File Type Plugin
 * 
 * YOLO Phase 4: Extracted from FilePageOverlay
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from '../Icons/index';
import { FileRenderProps } from './index';

export const PowerPointRenderer: React.FC<FileRenderProps> = ({
  content,
  fileTypeInfo,
  filePath,
  fileName
}) => {
  const [showSystemAppPrompt, setShowSystemAppPrompt] = useState(false);
  const [isBinary, setIsBinary] = useState(false);

  // Detect if content is binary/base64 (e.g., pptx zip) and should not be shown as text
  useEffect(() => {
    if (!content) { setIsBinary(false); return; }
    try {
      // If content looks like extracted text (not base64), trust it and display
      const looksLikeExtractedText = content.length > 100 && 
        content.includes(' ') && 
        content.includes('\n') &&
        !/^[A-Za-z0-9+/=\r\n]+$/.test(content);
      
      if (looksLikeExtractedText) {
        // This is extracted text from the kernel processor - display it
        setIsBinary(false);
        return;
      }
      
      // Legacy binary detection for raw files (fallback)
      const looksBase64 = /^[A-Za-z0-9+/=\r\n]+$/.test(content) && content.length % 4 === 0;
      let binary = false;
      if (looksBase64) {
        // Decode first chunk to test header; pptx/xlsx/docx usually start with 'PK' when decoded
        const sample = content.slice(0, 2000);
        let bin = '';
        try { bin = atob(sample.replace(/\r|\n/g, '')); } catch { /* ignore */ }
        if (bin) {
          const header = bin.slice(0, 4);
          if (header.includes('PK')) binary = true;
          // Heuristic: high ratio of non-printable chars
          const nonPrintable = (bin.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g) || []).length;
          if (nonPrintable > bin.length * 0.05) binary = true;
        }
      } else {
        // If not base64, check raw content for many non-printable characters that indicate mis-decoded binary
        const nonPrintable = (content.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g) || []).length;
        if (nonPrintable > content.length * 0.01 || content.includes('[Content_Types].xml') || content.includes('PK')) {
          binary = true;
        }
      }
      setIsBinary(false);
    } catch {
      setIsBinary(false);
    }
  }, [content]);

  const handleOpenInSystemApp = async () => {
    try {
      if (window.electronAPI?.shell?.openPath) {
        await window.electronAPI.shell.openPath(filePath);
      } else {
        console.error('No method available to open file in system app');
        setShowSystemAppPrompt(true);
      }
    } catch (error) {
      console.error('Failed to open PowerPoint presentation in system app:', error);
      setShowSystemAppPrompt(true);
    }
  };



  return (
    <div className="flex flex-col items-center justify-center h-full p-6">
      {/* File Header */}
      <div className="text-center mb-8">
        <div className="w-20 h-20 bg-orange-600/20 rounded-2xl flex items-center justify-center mb-4 mx-auto">
          <FontAwesomeIcon icon={ICONS.filePowerpoint} className="text-orange-500 text-3xl" />
        </div>
        <h1 className="text-2xl font-bold text-supplement1 mb-2">{fileName}</h1>
        <p className="text-gray-400">{fileTypeInfo?.displayName || 'PowerPoint Presentation'}</p>
        <p className="text-sm text-gray-500 mt-1">{filePath}</p>
      </div>

      {/* Content Preview */}
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl mb-6">
        <div className="flex items-center gap-3 mb-4">
          <FontAwesomeIcon icon={ICONS.infoCircle} className="text-orange-400" />
          <h3 className="text-lg font-semibold text-supplement1">Presentation Preview</h3>
        </div>
        
        {!isBinary && content && content.length > 0 ? (
          <div className="bg-gray-900 rounded p-4 max-h-64 overflow-y-auto">
            <p className="text-gray-300 text-sm leading-relaxed">
              {content.length > 500
                ? `${content.substring(0, 500)}...`
                : content
              }
            </p>
            {content.length > 500 && (
              <p className="text-gray-500 text-xs mt-2">
                Content truncated. Full presentation available for download.
              </p>
            )}
          </div>
        ) : (
          <div className="bg-gray-900 rounded p-4 text-center">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-yellow-400 text-2xl mb-2" />
            <p className="text-gray-400">Preview not available</p>
            <p className="text-sm text-gray-500 mt-1">
              This appears to be a binary PowerPoint file. Use the buttons below to open in PowerPoint or download.
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <button
          onClick={handleOpenInSystemApp}
          className="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <FontAwesomeIcon icon={ICONS.externalLink} className="text-sm" />
          Open in PowerPoint
        </button>
        

      </div>

      {/* System App Prompt */}
      {showSystemAppPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md mx-4">
            <div className="text-center mb-4">
              <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-yellow-400 text-3xl mb-3" />
              <h3 className="text-lg font-semibold text-supplement1 mb-2">System App Required</h3>
              <p className="text-gray-400 text-sm">
                To view this PowerPoint presentation, you need Microsoft PowerPoint or a compatible application installed on your system.
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowSystemAppPrompt(false)}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
              >
                Close
              </button>

            </div>
          </div>
        </div>
      )}
    </div>
  );
};
