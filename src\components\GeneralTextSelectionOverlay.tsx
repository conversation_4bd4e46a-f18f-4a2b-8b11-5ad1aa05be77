import React, { useEffect, useState, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from './Icons/index';
import { contextAnnotationService, ContextAnnotationData } from '../services/contextAnnotationService';

interface GeneralTextSelectionOverlayProps {
  selectedText: string;
  position: { x: number; y: number };
  onClose: () => void;
  conversationId?: string;
  chatMessageId?: string;
  vaultContext?: string;
}

export const GeneralTextSelectionOverlay: React.FC<GeneralTextSelectionOverlayProps> = ({
  selectedText,
  position,
  onClose,
  conversationId,
  chatMessageId,
  vaultContext
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [userInput, setUserInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [importance, setImportance] = useState<'high' | 'medium' | 'low'>('medium');
  const [category, setCategory] = useState('general_insight');

  // Show overlay with a small delay to prevent flickering
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Auto-generate tags when component mounts
  useEffect(() => {
    const autoTags = contextAnnotationService['autoGenerateTags'](selectedText);
    setTags(autoTags);
  }, [selectedText]);

  const handleAddToContext = async () => {
    if (!selectedText.trim()) return;

    setIsSubmitting(true);
    try {
      const annotationData: ContextAnnotationData = {
        selectedText: selectedText.trim(),
        conversationId,
        chatMessageId,
        vaultContext,
        userInput: userInput.trim() || undefined,
        tags,
        importance,
        category
      };

      const success = await contextAnnotationService.addContextAnnotation(annotationData);
      
      if (success) {
        console.log('[GENERAL-TEXT-SELECTION] ✅ Successfully added context annotation');
        onClose();
      } else {
        console.error('[GENERAL-TEXT-SELECTION] ❌ Failed to add context annotation');
      }
    } catch (error) {
      console.error('[GENERAL-TEXT-SELECTION] ❌ Error adding context annotation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTagChange = (tag: string, checked: boolean) => {
    if (checked) {
      setTags(prev => [...prev, tag]);
    } else {
      setTags(prev => prev.filter(t => t !== tag));
    }
  };

  const addCustomTag = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && event.currentTarget.value.trim()) {
      const newTag = event.currentTarget.value.trim();
      if (!tags.includes(newTag)) {
        setTags(prev => [...prev, newTag]);
      }
      event.currentTarget.value = '';
    }
  };

  const displayText = selectedText.length > 100
    ? selectedText.substring(0, 100) + '...'
    : selectedText;

  return (
    <div
      ref={overlayRef}
      className={`
        fixed z-[9999] bg-gray-800/95 backdrop-blur-sm border border-tertiary/50 
        rounded-lg shadow-lg p-4 max-w-md transition-all duration-200
        ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
      `}
      style={{
        left: Math.min(position.x, window.innerWidth - 400),
        top: Math.max(position.y - 80, 10)
      }}
    >
      {/* Header */}
      <div className="mb-4">
        <div className="text-lg font-semibold text-gray-200 mb-2">Add to Context Notes</div>
        <div className="text-xs text-gray-400 mb-1">Selected Text:</div>
        <div className="text-sm text-gray-300 bg-gray-900/50 p-3 rounded border border-gray-700/50 max-h-24 overflow-y-auto">
          {displayText}
        </div>
        <div className="text-xs text-gray-500 mt-1 text-right">
          {selectedText.length} characters
        </div>
      </div>

      {/* User Input */}
      <div className="mb-4">
        <label className="block text-xs text-gray-400 mb-2 font-medium">
          Additional Notes (Optional):
        </label>
        <textarea
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          placeholder="Add your thoughts, insights, or context..."
          className="w-full h-20 bg-gray-900 border border-gray-600 rounded p-2 text-gray-300 text-sm resize-none focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/20"
        />
      </div>

      {/* Tags */}
      <div className="mb-4">
        <label className="block text-xs text-gray-400 mb-2 font-medium">
          Tags:
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {['design', 'development', 'business', 'research', 'general'].map(tag => (
            <label key={tag} className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={tags.includes(tag)}
                onChange={(e) => handleTagChange(tag, e.target.checked)}
                className="rounded border-gray-600 text-primary focus:ring-primary/20"
              />
              <span className="text-xs text-gray-300 capitalize">{tag}</span>
            </label>
          ))}
        </div>
        <input
          type="text"
          placeholder="Add custom tag (press Enter)"
          onKeyDown={addCustomTag}
          className="w-full bg-gray-900 border border-gray-600 rounded px-2 py-1 text-gray-300 text-sm focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/20"
        />
      </div>

      {/* Importance and Category */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-xs text-gray-400 mb-1 font-medium">Importance:</label>
          <select
            value={importance}
            onChange={(e) => setImportance(e.target.value as 'high' | 'medium' | 'low')}
            className="w-full bg-gray-900 border border-gray-600 rounded px-2 py-1 text-gray-300 text-sm focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/20"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
        </div>
        <div>
          <label className="block text-xs text-gray-400 mb-1 font-medium">Category:</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="w-full bg-gray-900 border border-gray-600 rounded px-2 py-1 text-gray-300 text-sm focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/20"
          >
            <option value="general_insight">General Insight</option>
            <option value="design_insight">Design Insight</option>
            <option value="technical_insight">Technical Insight</option>
            <option value="business_insight">Business Insight</option>
            <option value="research_finding">Research Finding</option>
          </select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <button
          onClick={handleAddToContext}
          disabled={isSubmitting}
          className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/80 disabled:bg-gray-600 text-gray-900 rounded text-sm font-medium transition-colors"
        >
          <FontAwesomeIcon icon={ICONS.stickyNote} className="text-xs" />
          {isSubmitting ? 'Adding...' : 'Add to Context'}
        </button>
        
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700/50 rounded text-sm transition-colors"
        >
          Cancel
        </button>
      </div>

      {/* Context Info */}
      <div className="text-xs text-gray-500 mt-3 text-center">
        {conversationId ? `Chat: ${conversationId.substring(0, 8)}...` : 'General Context'}
        {vaultContext && ` • Vault: ${vaultContext}`}
      </div>
    </div>
  );
};
