import React from 'react';
import { useGlobalTextSelection } from '../hooks/useGlobalTextSelection';
import { TextSelectionOverlay } from './TextSelectionOverlay';
import { GeneralTextSelectionOverlay } from './GeneralTextSelectionOverlay';
import { chatAnnotationService } from '../services/chatAnnotationService';
import { contextAnnotationService } from '../services/contextAnnotationService';
import { navigationManager } from '../services/navigationService';
import { useAppStore } from '../store';
import { contextVaultService } from '../services/contextVaultService';

export const GlobalTextSelectionManager: React.FC = () => {
  const { selection, clearSelection } = useGlobalTextSelection();
  const { selectedContextId: storeSelectedContextId } = useAppStore();
  const serviceSelectedContextId = contextVaultService.getSelectedContextId?.();
  const effectiveSelectedContextId = serviceSelectedContextId || storeSelectedContextId || null;
  
  // Get current file context from navigation state for Route 1
  const navigationState = navigationManager.getState();
  const currentFilePath = navigationState.fileOverlay?.filePath;

  if (!selection) {
    return null;
  }

  // Route 1: File-specific text selection
  if (selection.context === 'file') {
    // For file context, we don't show an overlay here
    // Instead, the FilePageOverlay handles it via onTextSelection
    // and creates annotations directly in IntelligenceHub
    return null;
  }

  // Chat-specific text selection - Route 1 or Route 2 based on context
  if (selection.context === 'chat') {
    const handleAddToAnnotation = async (selectedText: string) => {
      try {
        console.log('[GLOBAL-TEXT-SELECTION] 📝 Adding chat content to annotation:', {
          messageId: selection.chatMessageId,
          conversationId: selection.conversationId,
          selectedTextLength: selectedText.length,
          currentFilePath,
          isRoute1: !!currentFilePath
        });

        let success = false;

        if (currentFilePath) {
          // Route 1: FilePageOverlay is open - save to the current file's context notes
          console.log('[GLOBAL-TEXT-SELECTION] 📁 Route 1: Saving to file context notes:', currentFilePath);
          success = await contextAnnotationService.addContextAnnotation({
            selectedText,
            conversationId: selection.conversationId,
            chatMessageId: selection.chatMessageId,
            vaultContext: effectiveSelectedContextId || 'shared-dropbox',
            filePath: currentFilePath, // Add filePath for Route 1
            category: 'file_annotation' // Distinguish from general context
          });
        } else {
          // Route 2: No file open - save to context notes in current vault
          const vaultContext = effectiveSelectedContextId || 'shared-dropbox';
          console.log('[GLOBAL-TEXT-SELECTION] 🌍 Route 2: Saving to context notes in vault:', {
            vaultContext,
            serviceSelectedContextId,
            storeSelectedContextId
          });
          success = await contextAnnotationService.addContextAnnotation({
            selectedText,
            conversationId: selection.conversationId,
            chatMessageId: selection.chatMessageId,
            vaultContext: vaultContext
          });
        }

        if (success) {
          console.log('[GLOBAL-TEXT-SELECTION] ✅ Successfully added chat content to annotation');
          clearSelection();
        } else {
          console.error('[GLOBAL-TEXT-SELECTION] ❌ Failed to add chat content to annotation');
        }
      } catch (error) {
        console.error('[GLOBAL-TEXT-SELECTION] ❌ Error adding chat content to annotation:', error);
      }
    };

    return (
      <TextSelectionOverlay
        selectedText={selection.text}
        position={selection.position}
        onAddToAnnotation={handleAddToAnnotation}
        onClose={clearSelection}
      />
    );
  }

  // Route 2: General context text selection
  if (selection.context === 'general') {
    const handleAddToContext = async (selectedText: string) => {
      try {
        console.log('[GLOBAL-TEXT-SELECTION] 🌍 Adding general context annotation:', {
          selectedTextLength: selectedText.length,
          vaultContext: selection.vaultContext
        });

        const success = await contextAnnotationService.addContextAnnotation({
          selectedText,
          vaultContext: selection.vaultContext
        });

        if (success) {
          console.log('[GLOBAL-TEXT-SELECTION] ✅ Successfully added general context annotation');
          clearSelection();
        } else {
          console.error('[GLOBAL-TEXT-SELECTION] ❌ Failed to add general context annotation');
        }
      } catch (error) {
        console.error('[GLOBAL-TEXT-SELECTION] ❌ Error adding general context annotation:', error);
      }
    };

    return (
      <GeneralTextSelectionOverlay
        selectedText={selection.text}
        position={selection.position}
        onClose={clearSelection}
        vaultContext={selection.vaultContext}
      />
    );
  }

  return null;
};
