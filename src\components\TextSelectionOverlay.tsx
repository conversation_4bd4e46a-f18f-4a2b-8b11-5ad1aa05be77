import React, { useEffect, useState, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from './Icons/index';

interface TextSelectionOverlayProps {
  selectedText: string;
  position: { x: number; y: number };
  onAddToAnnotation: (text: string) => void;
  onClose: () => void;
}

export const TextSelectionOverlay: React.FC<TextSelectionOverlayProps> = ({
  selectedText,
  position,
  onAddToAnnotation,
  onClose
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Show overlay with a small delay to prevent flickering
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleAddToAnnotation = () => {
    onAddToAnnotation(selectedText);
    onClose();
  };

  // Truncate text for display
  const displayText = selectedText.length > 50 
    ? selectedText.substring(0, 50) + '...' 
    : selectedText;

  return (
    <div
      ref={overlayRef}
      className={`
        fixed z-[9999] bg-gray-800/95 backdrop-blur-sm border border-tertiary/50 
        rounded-lg shadow-lg p-3 max-w-sm transition-all duration-200
        ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
      `}
      style={{
        left: Math.min(position.x, window.innerWidth - 300),
        top: Math.max(position.y - 60, 10)
      }}
    >
      {/* Selected Text Preview */}
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-1 font-medium">Selected Text:</div>
        <div className="text-sm text-gray-300 bg-gray-900/50 p-2 rounded border border-gray-700/50 max-h-20 overflow-y-auto">
          {displayText}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <button
          onClick={handleAddToAnnotation}
          className="flex items-center gap-2 px-3 py-2 bg-primary hover:bg-primary/80 text-gray-900 rounded text-sm font-medium transition-colors"
        >
          <FontAwesomeIcon icon={ICONS.stickyNote} className="text-xs" />
          Add to Annotation
        </button>
        
        <button
          onClick={onClose}
          className="px-3 py-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700/50 rounded text-sm transition-colors"
        >
          Cancel
        </button>
      </div>

      {/* Character Count */}
      <div className="text-xs text-gray-500 mt-2 text-center">
        {selectedText.length} characters
      </div>
    </div>
  );
};
