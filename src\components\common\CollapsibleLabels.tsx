/**
 * Collapsible Labels Component
 * Shows 2 lines initially (3 labels per line = 6 total), then expands with "more (x)" functionality
 * Uses compact text sizing: 10px labels, 8px scores
 */

import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from '../Icons/index'

export interface Label {
  id: string
  text: string
  confidence: number
  category?: 'theme' | 'topic' | 'concept'
  isUserLabel?: boolean
}

interface CollapsibleLabelsProps {
  labels: Label[]
  maxVisibleLines?: number
  onLabelClick?: (label: Label) => void
  selectedLabels?: Set<string>
  onLabelToggle?: (labelId: string, selected: boolean) => void
  language?: string
  className?: string
  showUserLabels?: boolean
  userLabels?: Label[]
}

export const CollapsibleLabels: React.FC<CollapsibleLabelsProps> = ({
  labels,
  maxVisibleLines = 2,
  onLabelClick,
  selectedLabels = new Set(),
  onLabelToggle,
  language = 'en',
  className = '',
  showUserLabels = false,
  userLabels = []
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  
  if (!labels || labels.length === 0) {
    return null
  }

  // Sort labels by confidence to highlight top scorer
  const sortedLabels = [...labels].sort((a, b) => b.confidence - a.confidence)
  
  // FIXED: Realistic layout - 3 labels per line, 6 total visible in 2 lines
  const labelsPerLine = 3 // Realistic: 3 labels per line (not 4)
  const maxVisibleLabels = maxVisibleLines * labelsPerLine // 2 × 3 = 6 labels
  const visibleLabels = isExpanded ? sortedLabels : sortedLabels.slice(0, maxVisibleLabels)
  const hiddenCount = sortedLabels.length - visibleLabels.length

  const handleLabelClick = (label: Label) => {
    if (onLabelClick) {
      onLabelClick(label)
    }
    
    if (onLabelToggle) {
      const isSelected = selectedLabels.has(label.id)
      onLabelToggle(label.id, !isSelected)
    }
  }

  const getLanguageText = (key: string) => {
    const uiTexts = {
      zh: { more: '更多', less: '收起', selected: '已选择' },
      en: { more: 'more', less: 'less', selected: 'selected' },
      ja: { more: 'もっと', less: '收起', selected: '選択済み' },
      ko: { more: '더보기', less: '접기', selected: '선택됨' }
    }
    
    const currentLanguage = language as keyof typeof uiTexts
    const currentTexts = uiTexts[currentLanguage] || uiTexts.en
    return currentTexts[key as keyof typeof currentTexts] || uiTexts.en[key as keyof typeof uiTexts.en]
  }

  // Get the top-scoring label for highlighting
  const topLabel = sortedLabels[0]

  return (
    <div className={`space-y-3 ${className}`}>
      {/* AI-Generated Labels Grid - Realistic 2-line layout (3 per line) */}
      <div className="space-y-2">
        {/* First line of labels (3 labels) */}
        <div className="flex flex-wrap gap-2">
          {visibleLabels.slice(0, labelsPerLine).map((label, index) => {
            const isSelected = selectedLabels.has(label.id)
            const isTopScorer = label.id === topLabel?.id
            
            return (
              <div
                key={label.id}
                className={`
                  inline-flex items-center gap-2 px-3 py-1.5 rounded-full border cursor-pointer transition-all duration-200
                  ${isTopScorer 
                    ? 'bg-red-500/20 border-red-400/40 text-red-300' // Highlight top scorer
                    : isSelected 
                    ? 'bg-primary/20 border-primary/40 text-primary' 
                    : 'bg-gray-800/50 border-gray-600/40 text-gray-200 hover:bg-gray-700/50'
                  }
                `}
                onClick={() => handleLabelClick(label)}
              >
                {/* Label Text - FIXED: 10px size */}
                <span className="text-[10px] font-medium whitespace-nowrap leading-tight">
                  {label.text}
                </span>
                
                {/* Confidence Score - FIXED: 8px size */}
                <span className={`
                  text-[8px] px-1.5 py-0.5 rounded-full leading-none
                  ${isTopScorer 
                    ? 'bg-red-500/30 text-red-200' 
                    : 'bg-gray-700/50 text-gray-300'
                  }
                `}>
                  {Math.round(label.confidence * 100)}%
                </span>
              </div>
            )
          })}
        </div>

        {/* Second line of labels (3 labels) */}
        {visibleLabels.length > labelsPerLine && (
          <div className="flex flex-wrap gap-2">
            {visibleLabels.slice(labelsPerLine, labelsPerLine * 2).map((label) => {
              const isSelected = selectedLabels.has(label.id)
              const isTopScorer = label.id === topLabel?.id
              
              return (
                <div
                  key={label.id}
                  className={`
                    inline-flex items-center gap-2 px-3 py-1.5 rounded-full border cursor-pointer transition-all duration-200
                    ${isTopScorer 
                      ? 'bg-red-500/20 border-red-400/40 text-red-300' // Highlight top scorer
                      : isSelected 
                      ? 'bg-primary/20 border-primary/40 text-primary' 
                      : 'bg-gray-800/50 border-gray-600/40 text-gray-200 hover:bg-gray-700/50'
                    }
                  `}
                  onClick={() => handleLabelClick(label)}
                >
                  {/* Label Text - FIXED: 10px size */}
                  <span className="text-[10px] font-medium whitespace-nowrap leading-tight">
                    {label.text}
                  </span>
                  
                  {/* Confidence Score - FIXED: 8px size */}
                  <span className={`
                    text-[8px] px-1.5 py-0.5 rounded-full leading-none
                    ${isTopScorer 
                      ? 'bg-red-500/30 text-red-200' 
                      : 'bg-gray-700/50 text-gray-300'
                    }
                  `}>
                    {Math.round(label.confidence * 100)}%
                  </span>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Expand/Collapse Button - Now triggers correctly after 6 labels */}
      {hiddenCount > 0 && (
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full px-3 py-2 text-[10px] text-gray-400 hover:text-gray-200 hover:bg-gray-700/30 rounded-lg transition-colors flex items-center justify-center gap-2 border border-dashed border-gray-600"
        >
          <FontAwesomeIcon 
            icon={isExpanded ? ICONS.chevronUp : ICONS.chevronDown} 
            className="text-[10px]" 
          />
          {isExpanded ? getLanguageText('less') : `...${getLanguageText('more')} (${hiddenCount})`}
        </button>
      )}

      {/* User Labels Section - Below the collapsible area */}
      {showUserLabels && userLabels.length > 0 && (
        <div className="pt-2 border-t border-gray-600/30">
          <div className="flex flex-wrap gap-2">
            {userLabels.map((label) => {
              const isSelected = selectedLabels.has(label.id)
              
              return (
                <div
                  key={label.id}
                  className={`
                    inline-flex items-center gap-2 px-3 py-1.5 rounded-full border cursor-pointer transition-all duration-200
                    ${isSelected 
                      ? 'bg-secondary/20 border-secondary/40 text-secondary' 
                      : 'bg-gray-700/30 border-gray-500/40 text-gray-300 hover:bg-gray-600/30'
                    }
                  `}
                  onClick={() => handleLabelClick(label)}
                >
                  {/* User Label Icon */}
                  <FontAwesomeIcon 
                    icon={ICONS.user} 
                    className="text-[10px] text-gray-400" 
                  />
                  
                  {/* Label Text - FIXED: 10px size */}
                  <span className="text-[10px] font-medium whitespace-nowrap leading-tight">
                    {label.text}
                  </span>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Selected Count */}
      {selectedLabels.size > 0 && (
        <div className="text-[10px] text-gray-400 text-center pt-1">
          {selectedLabels.size} {getLanguageText('selected')}
        </div>
      )}
    </div>
  )
}

export default CollapsibleLabels
