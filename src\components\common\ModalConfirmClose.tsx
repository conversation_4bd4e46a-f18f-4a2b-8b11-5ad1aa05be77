import React from 'react'

interface ModalConfirmCloseProps {
  variant: 'local' | 'usb'
  open: boolean
  onCancel: () => void
  onConfirm: () => void
}

const ModalConfirmClose: React.FC<ModalConfirmCloseProps> = ({ variant, open, onCancel, onConfirm }) => {
  if (!open) return null

  const title = variant === 'usb'
    ? 'Close ChatLo and prepare for safe USB removal?'
    : 'Are you sure you want to close ChatLo? Any in-progress operations will stop.'

  const subtitle = variant === 'usb'
    ? "We’ll flush and close your portable database so it’s safe to unplug."
    : 'Potential lost: generating responses, unsaved notes..etc.'

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      <div className="absolute inset-0 backdrop-blur-md bg-black/40" />
      <div className="relative w-full max-w-md mx-4 rounded-xl border border-gray-700 bg-gray-900 shadow-xl">
        <div className="p-5 space-y-3">
          <h3 className="text-supplement1 text-lg font-semibold">{title}</h3>
          <p className="text-gray-400 text-xs">{subtitle}</p>
          <div className="flex items-center justify-end gap-2 pt-2">
            <button onClick={onCancel} className="px-3 py-1.5 rounded-md bg-gray-700 text-gray-200 hover:bg-gray-600 transition-colors text-sm">Cancel</button>
            <button onClick={onConfirm} className="px-3 py-1.5 rounded-md bg-primary text-gray-900 hover:bg-primary/90 transition-colors text-sm">
              {variant === 'usb' ? 'Close and Prepare' : 'Close'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ModalConfirmClose

