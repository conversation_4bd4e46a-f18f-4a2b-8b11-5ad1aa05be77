import { useState, useEffect } from 'react'
import { navigationManager, NavigationState } from '../services/navigationService'

/**
 * Hook to manage file overlay state using the navigation service
 * 
 * This hook provides a clean interface for components to:
 * - Check if file overlay is open
 * - Get current file overlay context
 * - Open/close file overlay
 * - Subscribe to navigation state changes
 */
export const useFileOverlay = () => {
  const [navigationState, setNavigationState] = useState<NavigationState>(navigationManager.getState())

  useEffect(() => {
    const unsubscribe = navigationManager.subscribe(setNavigationState)
    return unsubscribe
  }, [])

  const fileOverlay = navigationState.fileOverlay

  const isOpen = fileOverlay?.isOpen || false
  const filePath = fileOverlay?.filePath || ''
  const fileName = fileOverlay?.fileName || ''
  const origin = fileOverlay?.origin

  const openFileOverlay = (
    filePath: string, 
    fileName: string, 
    origin?: {
      page: string
      context?: string
      chatMessageId?: string
      conversationId?: string
    }
  ) => {
    navigationManager.openFileOverlay(filePath, fileName, origin)
  }

  const closeFileOverlay = () => {
    navigationManager.closeFileOverlay()
  }

  const navigateBack = () => {
    if (origin) {
      const returnTarget = {
        page: origin.page,
        context: origin.context,
        resource: origin.conversationId,
        params: {}
      }
      
      navigationManager.navigateTo(returnTarget)
      closeFileOverlay()
    }
  }

  return {
    // State
    isOpen,
    filePath,
    fileName,
    origin,
    
    // Actions
    openFileOverlay,
    closeFileOverlay,
    navigateBack,
    
    // Full navigation state for advanced usage
    navigationState
  }
}
