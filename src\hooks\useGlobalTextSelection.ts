import { useState, useEffect, useCallback } from 'react';

interface GlobalTextSelection {
  text: string;
  position: { x: number; y: number };
  element: HTMLElement | null;
  context: 'file' | 'chat' | 'general';
  filePath?: string;
  conversationId?: string;
  chatMessageId?: string;
  vaultContext?: string;
}

export const useGlobalTextSelection = () => {
  const [selection, setSelection] = useState<GlobalTextSelection | null>(null);

  const getSelectionPosition = useCallback((selectedText: string): { x: number; y: number } => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return { x: 0, y: 0 };
    }
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2,
      y: rect.top
    };
  }, []);

  const determineSelectionContext = useCallback((element: HTMLElement): {
    context: 'file' | 'chat' | 'general';
    filePath?: string;
    conversationId?: string;
    chatMessageId?: string;
    vaultContext?: string;
  } | null => {
    // CRITICAL FIX: Exclude interactive form elements from text selection overlay
    // Check if selection is within form elements (textarea, input, contenteditable)
    const isFormElement = element.tagName === 'TEXTAREA' || 
                         element.tagName === 'INPUT' || 
                         element.contentEditable === 'true' ||
                         element.closest('textarea') ||
                         element.closest('input') ||
                         element.closest('[contenteditable="true"]');
    
    if (isFormElement) {
      console.log('[GLOBAL-TEXT-SELECTION] 🚫 Ignoring selection in form element:', element.tagName);
      return null; // Return null to indicate this selection should be ignored
    }

    // Check if selection is within a file viewer
    const fileViewer = element.closest('[data-file-path]');
    if (fileViewer) {
      return {
        context: 'file',
        filePath: fileViewer.getAttribute('data-file-path') || undefined
      };
    }

    // Check if selection is within a chat message
    const chatMessage = element.closest('[data-message-id]');
    if (chatMessage) {
      const messageId = chatMessage.getAttribute('data-message-id');
      const conversationId = chatMessage.getAttribute('data-conversation-id');
      const vaultContext = chatMessage.getAttribute('data-vault-context');
      
      return {
        context: 'chat',
        chatMessageId: messageId || undefined,
        conversationId: conversationId || undefined,
        vaultContext: vaultContext || undefined
      };
    }

    // Check if selection is within a specific vault context
    const vaultContext = element.closest('[data-vault-context]');
    if (vaultContext) {
      return {
        context: 'general',
        vaultContext: vaultContext.getAttribute('data-vault-context') || undefined
      };
    }

    // Default to general context
    return { context: 'general' };
  }, []);

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    
    if (!selection || selection.toString().trim().length === 0) {
      setSelection(null);
      return;
    }

    const selectedText = selection.toString().trim();
    
    // Only show overlay for meaningful selections (more than 3 characters)
    if (selectedText.length < 3) {
      setSelection(null);
      return;
    }

    const position = getSelectionPosition(selectedText);
    const element = selection.anchorNode?.parentElement as HTMLElement;
    
    if (!element) {
      setSelection(null);
      return;
    }

    // Determine the context of the selection
    const contextInfo = determineSelectionContext(element);

    // If contextInfo is null, ignore this selection (e.g., form elements)
    if (!contextInfo) {
      setSelection(null);
      return;
    }

    setSelection({
      text: selectedText,
      position,
      element,
      ...contextInfo
    });
  }, [getSelectionPosition, determineSelectionContext]);

  const clearSelection = useCallback(() => {
    setSelection(null);
    // Clear the browser selection
    if (window.getSelection) {
      window.getSelection()?.removeAllRanges();
    }
  }, []);

  // Listen for text selection changes
  useEffect(() => {
    const handleMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(handleTextSelection, 10);
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      // Handle keyboard selection (Shift + Arrow keys, Ctrl+A, etc.)
      if (event.shiftKey || event.ctrlKey || event.metaKey) {
        setTimeout(handleTextSelection, 10);
      }
    };

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleTextSelection]);

  return {
    selection,
    clearSelection,
    handleTextSelection
  };
};
