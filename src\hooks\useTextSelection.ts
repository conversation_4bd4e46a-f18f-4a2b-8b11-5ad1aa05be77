import { useState, useEffect, useCallback } from 'react';

interface TextSelection {
  text: string;
  position: { x: number; y: number };
  element: HTMLElement | null;
}

export const useTextSelection = () => {
  const [selection, setSelection] = useState<TextSelection | null>(null);

  const getSelectionPosition = useCallback((selectedText: string): { x: number; y: number } => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return { x: 0, y: 0 };
    }

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    // Position the overlay above the selection
    return {
      x: rect.left + rect.width / 2,
      y: rect.top
    };
  }, []);

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    
    if (!selection || selection.toString().trim().length === 0) {
      setSelection(null);
      return;
    }

    const selectedText = selection.toString().trim();
    
    // Only show overlay for meaningful selections (more than 3 characters)
    if (selectedText.length < 3) {
      setSelection(null);
      return;
    }

    const position = getSelectionPosition(selectedText);
    const element = selection.anchorNode?.parentElement as HTMLElement;

    setSelection({
      text: selectedText,
      position,
      element
    });
  }, [getSelectionPosition]);

  const clearSelection = useCallback(() => {
    setSelection(null);
    // Clear the browser selection
    if (window.getSelection) {
      window.getSelection()?.removeAllRanges();
    }
  }, []);

  // Listen for text selection changes
  useEffect(() => {
    const handleMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(handleTextSelection, 10);
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      // Handle keyboard selection (Shift + Arrow keys, Ctrl+A, etc.)
      if (event.shiftKey || event.ctrlKey || event.metaKey) {
        setTimeout(handleTextSelection, 10);
      }
    };

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleTextSelection]);

  return {
    selection,
    clearSelection,
    handleTextSelection
  };
};
