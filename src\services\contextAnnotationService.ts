/**
 * Context Annotation Service
 * Handles general context annotations from chat conversations and general context
 * These are not tied to specific files but capture insights across conversations
 */

import { ContextNote, ContextNotesCollection, GlobalContextIndex, GlobalContextInsight } from '../types/fileIntelligenceTypes'
import { intelligence as intelligenceClient, vault as vaultClient } from '../api/UnifiedAPIClient'
import { extractContextPath } from '../utils/vaultPath'

export interface ContextAnnotationData {
  selectedText: string
  conversationId?: string
  chatMessageId?: string
  vaultContext?: string
  filePath?: string // Add filePath for Route 1 (file-specific annotations)
  userInput?: string
  tags?: string[]
  importance?: 'high' | 'medium' | 'low'
  category?: string
}

export class ContextAnnotationService {
  private static instance: ContextAnnotationService
  private listeners: Set<(data: ContextAnnotationData) => void> = new Set()

  static getInstance(): ContextAnnotationService {
    if (!ContextAnnotationService.instance) {
      ContextAnnotationService.instance = new ContextAnnotationService()
    }
    return ContextAnnotationService.instance
  }

  /**
   * Add a general context annotation from selected text
   */
  async addContextAnnotation(data: ContextAnnotationData): Promise<boolean> {
    try {
      console.log('[CONTEXT-ANNOTATION] 📝 Adding context annotation:', {
        selectedTextLength: data.selectedText.length,
        conversationId: data.conversationId,
        chatMessageId: data.chatMessageId,
        vaultContext: data.vaultContext,
        hasUserInput: !!data.userInput
      })

      // Determine the target path for the annotation
      const targetPath = this.getContextAnnotationPath(data)
      
      // Create the context note
      const contextNote: ContextNote = {
        id: `context_note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'general_context',
        title: this.generateTitle(data.selectedText, data.category),
        content: this.buildContent(data.selectedText, data.userInput, data.filePath),
        selected_text: data.selectedText,
        source_context: {
          chat_message_id: data.chatMessageId,
          conversation_id: data.conversationId,
          timestamp: new Date().toISOString(),
          user_input: data.userInput,
          vault_context: data.vaultContext,
          file_path: data.filePath // Add file path for Route 1
        },
        metadata: {
          tags: data.tags || this.autoGenerateTags(data.selectedText),
          importance: data.importance || 'medium',
          category: data.category || 'general_insight',
          related_concepts: this.extractRelatedConcepts(data.selectedText),
          confidence_score: 0.8
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        note_number: 1
      }

      // Save the context note
      const success = await this.saveContextNote(targetPath, contextNote, data.vaultContext)
      if (success) {
        console.log('[CONTEXT-ANNOTATION] ✅ Successfully added context annotation')
        this.notifyListeners(data)
        return true
      } else {
        console.error('[CONTEXT-ANNOTATION] ❌ Failed to save context annotation')
        return false
      }
    } catch (error) {
      console.error('[CONTEXT-ANNOTATION] ❌ Error adding context annotation:', error)
      return false
    }
  }

  /**
   * Get the real file path for storing context annotations in .intelligence/context-notes
   */
  private getContextAnnotationPath(data: ContextAnnotationData): string {
    // Route 1: File-specific annotations go to file-specific context notes
    if (data.filePath) {
      // Create a file-specific context notes path
      const fileHash = this.generateFileHash(data.filePath)
      return `context-notes/file-${fileHash}.json`
    }
    
    // Route 2: General context annotations go to conversation-specific context notes
    const fileName = data.conversationId ? `${data.conversationId}.json` : 'global_context.json'
    return `context-notes/${fileName}`
  }

  /**
   * Resolve absolute vault path for saving
   * - If a selected context (vault) is provided, resolve to its absolute path from registry
   * - Otherwise, resolve to shared-dropbox under the vault root
   */
  private async resolveVaultPath(vaultContext?: string): Promise<string> {
    try {
      const registry: any = await vaultClient.getRegistry()
      const vaultRoot: string | undefined = registry?.vaultRoot

      // If we have a selected context id, try to resolve to its absolute path
      if (vaultContext && registry?.contexts && Array.isArray(registry.contexts)) {
        const ctx = registry.contexts.find((c: any) => c.id === vaultContext || c.name === vaultContext)
        if (ctx?.path) {
          return ctx.path as string
        }
      }

      // Fallback to shared-dropbox under the vault root
      if (vaultRoot) {
        // Join using platform-safe join
        const isWin = /\\|\w:\\/i.test(vaultRoot)
        return isWin ? `${vaultRoot}\\shared-dropbox` : `${vaultRoot}/shared-dropbox`
      }

      // Last resort: return provided string (may fail downstream but keeps behavior unchanged)
      return vaultContext || 'shared-dropbox'
    } catch (e) {
      console.warn('[CONTEXT-ANNOTATION] ⚠️ Failed to resolve vault path from registry, using fallback:', e)
      return vaultContext || 'shared-dropbox'
    }
  }

  /**
   * Generate a title for the context note
   */
  private generateTitle(selectedText: string, category?: string): string {
    const maxLength = 60
    let title = selectedText.trim()
    
    if (title.length > maxLength) {
      title = title.substring(0, maxLength).trim() + '...'
    }
    
    if (category && category !== 'general_insight') {
      title = `${category}: ${title}`
    }
    
    return title
  }

  /**
   * Build the content combining selected text and user input
   */
  private buildContent(selectedText: string, userInput?: string, filePath?: string): string {
    let content = `**Selected Context:**\n${selectedText}`
    
    if (userInput && userInput.trim()) {
      content += `\n\n**User Notes:**\n${userInput.trim()}`
    }
    
    if (filePath) {
      content += `\n\n---\n*Source: File annotation from ${filePath}*`
    } else {
      content += `\n\n---\n*Source: General context annotation*`
    }
    
    return content
  }

  /**
   * Auto-generate tags based on selected text
   */
  private autoGenerateTags(selectedText: string): string[] {
    const tags: string[] = []
    const text = selectedText.toLowerCase()
    
    // Simple tag generation based on content
    if (text.includes('design') || text.includes('ui') || text.includes('ux')) {
      tags.push('design')
    }
    if (text.includes('code') || text.includes('programming') || text.includes('development')) {
      tags.push('development')
    }
    if (text.includes('business') || text.includes('strategy') || text.includes('planning')) {
      tags.push('business')
    }
    if (text.includes('research') || text.includes('analysis') || text.includes('study')) {
      tags.push('research')
    }
    
    // Always add a general tag
    if (tags.length === 0) {
      tags.push('general')
    }
    
    return tags
  }

  /**
   * Generate a simple hash for file paths
   */
  private generateFileHash(filePath: string): string {
    // Simple hash function for file paths
    let hash = 0
    for (let i = 0; i < filePath.length; i++) {
      const char = filePath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Extract related concepts from selected text
   */
  private extractRelatedConcepts(selectedText: string): string[] {
    // Simple concept extraction - could be enhanced with AI analysis
    const concepts: string[] = []
    const words = selectedText.toLowerCase().split(/\s+/)
    
    // Look for technical terms, proper nouns, etc.
    const technicalTerms = ['api', 'sdk', 'framework', 'library', 'component', 'system', 'architecture']
    const foundTerms = words.filter(word => technicalTerms.includes(word))
    
    if (foundTerms.length > 0) {
      concepts.push(...foundTerms)
    }
    
    return concepts
  }

  /**
   * Save a context note to storage using the unified intelligence API
   */
  private async saveContextNote(targetPath: string, contextNote: ContextNote, vaultContext?: string): Promise<boolean> {
    try {
      console.log('[CONTEXT-ANNOTATION] 💾 Saving context note to:', targetPath)
      
      // Determine the absolute vault path via registry
      const vaultPath = await this.resolveVaultPath(vaultContext)
      console.log('[CONTEXT-ANNOTATION] 💾 Resolved vault path:', vaultPath)
      
      console.log('[CONTEXT-ANNOTATION] 💾 🔍 About to save using unified IPC:', {
        filePath: targetPath,
        vaultPath,
        noteId: contextNote.id
      });

      // Use the unified intelligence API to save directly
      // The targetPath already includes the filename (e.g., "context-notes/conversation-123.json")
      const saveResult = await intelligenceClient.write(targetPath, vaultPath, {
        json: contextNote  // Save the individual note directly
      })

      console.log('[CONTEXT-ANNOTATION] 💾 💾 Save result:', {
        success: saveResult?.success,
        error: saveResult?.error,
        fullResult: saveResult
      });

      if (saveResult && saveResult.success !== false) {
        console.log('[CONTEXT-ANNOTATION] 💾 ✅ Context note saved successfully to:', `${vaultPath}/.intelligence/${targetPath}`);
        return true
      } else {
        console.error('[CONTEXT-ANNOTATION] 💾 ❌ Failed to save context note:', saveResult)
        return false
      }
    } catch (error) {
      console.error('[CONTEXT-ANNOTATION] 💾 ❌ Error saving context note:', error)
      return false
    }
  }

  /**
   * Load context notes for a conversation
   */
  async loadContextNotes(conversationId: string, vaultContext?: string): Promise<ContextNote[]> {
    try {
      const fileName = conversationId ? `${conversationId}.json` : 'global_context.json'
      const targetPath = `context-notes/${fileName}`
      
      // Determine the vault path: selected vault or shared-dropbox fallback
      let vaultPath: string;
      if (vaultContext && vaultContext !== 'default') {
        vaultPath = vaultContext;
      } else {
        vaultPath = 'shared-dropbox';
      }
      
      console.log('[CONTEXT-ANNOTATION] 📖 Loading context notes from:', {
        targetPath,
        vaultPath,
        fullPath: `${vaultPath}/.intelligence/${targetPath}`
      });
      
      const result = await intelligenceClient.read(targetPath, vaultPath)
      
      if (result && result.success !== false && result.data) {
        const contextNote = result.data as ContextNote
        console.log('[CONTEXT-ANNOTATION] 📖 ✅ Loaded context note:', contextNote.id);
        return [contextNote]; // Return as array for consistency
      }
      
      console.log('[CONTEXT-ANNOTATION] 📖 No context notes found')
      return []
    } catch (error) {
      console.error('[CONTEXT-ANNOTATION] 📖 ❌ Error loading context notes:', error)
      return []
    }
  }

  /**
   * Load global context insights
   */
  async loadGlobalContextInsights(): Promise<GlobalContextInsight[]> {
    try {
      console.log('[CONTEXT-ANNOTATION] 🌍 Loading global context insights')
      
      const targetPath = 'context-notes/global_context.json'
      const vaultPath = 'shared-dropbox' // Global insights always in shared-dropbox
      
      const result = await intelligenceClient.read(targetPath, vaultPath)
      
      if (result && result.success !== false && result.data) {
        const contextNote = result.data as ContextNote
        // Convert to GlobalContextInsight format
        const insight: GlobalContextInsight = {
          id: contextNote.id,
          type: 'cross_conversation_insight',
          title: contextNote.title,
          content: contextNote.content,
          source_conversations: [{
            conversation_id: contextNote.source_context.conversation_id || 'global',
            vault_context: contextNote.source_context.vault_context || 'shared-dropbox',
            timestamp: contextNote.created_at,
            relevance_score: 0.8
          }],
          metadata: {
            tags: contextNote.metadata.tags,
            importance: contextNote.metadata.importance,
            category: contextNote.metadata.category,
            confidence_score: contextNote.metadata.confidence_score
          },
          created_at: contextNote.created_at,
          updated_at: contextNote.updated_at
        }
        
        return [insight]
      }
      
      return []
    } catch (error) {
      console.error('[CONTEXT-ANNOTATION] 🌍 ❌ Error loading global context insights:', error)
      return []
    }
  }

  /**
   * Subscribe to context annotation events
   */
  subscribe(listener: (data: ContextAnnotationData) => void): () => void {
    this.listeners.add(listener)
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * Notify listeners of context annotation events
   */
  private notifyListeners(data: ContextAnnotationData): void {
    this.listeners.forEach(listener => {
      try {
        listener(data)
      } catch (error) {
        console.error('[CONTEXT-ANNOTATION] ❌ Error in listener:', error)
      }
    })
  }
}

export const contextAnnotationService = ContextAnnotationService.getInstance()
