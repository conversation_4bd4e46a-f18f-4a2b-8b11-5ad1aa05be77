/**
 * Conversation Intelligence Context Manager
 * 
 * Maintains Map of file intelligence data per conversation, ensuring context 
 * persists throughout chat session and prevents intelligence data loss.
 */

import { unifiedIntelligenceService, ProcessedIntelligence } from './unifiedIntelligenceService'
import { intelligenceCacheManager } from './intelligenceCacheManager'
import { fileAttachmentManager } from './fileAttachmentManager'

export interface ConversationFileContext {
  filePath: string
  vaultPath: string
  intelligence: ProcessedIntelligence
  attachedAt: string
  lastAccessed: string
  messageIds: string[] // Messages that reference this file
}

export interface ConversationIntelligenceContext {
  conversationId: string
  files: Map<string, ConversationFileContext>
  createdAt: string
  lastUpdated: string
  totalFiles: number
  totalIntelligenceSize: number
}

export interface AttachFileOptions {
  forceReprocess?: boolean
  includeInHistory?: boolean
  messageId?: string
}

class ConversationIntelligenceManager {
  private conversations = new Map<string, ConversationIntelligenceContext>()
  private activeConversationId: string | null = null

  /**
   * Set the active conversation
   */
  setActiveConversation(conversationId: string): void {
    this.activeConversationId = conversationId
    
    // Initialize conversation context if it doesn't exist
    if (!this.conversations.has(conversationId)) {
      this.initializeConversation(conversationId)
    }
    
    console.log('[CONVERSATION-INTELLIGENCE] 🎯 Active conversation set to:', conversationId)
  }

  /**
   * Initialize a new conversation context
   */
  private initializeConversation(conversationId: string): void {
    const context: ConversationIntelligenceContext = {
      conversationId,
      files: new Map(),
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      totalFiles: 0,
      totalIntelligenceSize: 0
    }
    
    this.conversations.set(conversationId, context)
    console.log('[CONVERSATION-INTELLIGENCE] 🆕 Initialized conversation context:', conversationId)
  }

  /**
   * Attach file intelligence to the active conversation
   */
  async attachFileToConversation(
    filePath: string, 
    vaultPath: string, 
    options: AttachFileOptions = {}
  ): Promise<ConversationFileContext | null> {
    if (!this.activeConversationId) {
      console.error('[CONVERSATION-INTELLIGENCE] ❌ No active conversation set')
      return null
    }

    try {
      console.log('[CONVERSATION-INTELLIGENCE] 📎 Attaching file to conversation:', filePath)

      // Get or create conversation context
      let context = this.conversations.get(this.activeConversationId)
      if (!context) {
        this.initializeConversation(this.activeConversationId)
        context = this.conversations.get(this.activeConversationId)!
      }

      // Check if file is already attached (duplicate prevention)
      const fileKey = `${filePath}:${vaultPath}`
      const existingFile = context.files.get(fileKey)

      if (existingFile && !options.forceReprocess) {
        // Update access time and message reference
        existingFile.lastAccessed = new Date().toISOString()
        if (options.messageId && !existingFile.messageIds.includes(options.messageId)) {
          existingFile.messageIds.push(options.messageId)
        }

        // Update the context in the map
        context.files.set(fileKey, existingFile)
        context.lastUpdated = new Date().toISOString()

        // Update cache
        intelligenceCacheManager.set(
          `conversation:${this.activeConversationId}`,
          context,
          60 * 60 * 1000
        )

        console.log('[CONVERSATION-INTELLIGENCE] ♻️ File already attached, updated access time and message reference')
        return existingFile
      }

      // Get intelligence data for the file
      const intelligence = await unifiedIntelligenceService.getIntelligence(
        { filePath, vaultPath },
        { forceReprocess: options.forceReprocess }
      )

      // Create file context
      const fileContext: ConversationFileContext = {
        filePath,
        vaultPath,
        intelligence,
        attachedAt: new Date().toISOString(),
        lastAccessed: new Date().toISOString(),
        messageIds: options.messageId ? [options.messageId] : []
      }

      // Add to conversation context
      context.files.set(fileKey, fileContext)
      context.totalFiles = context.files.size
      context.totalIntelligenceSize += this.estimateIntelligenceSize(intelligence)
      context.lastUpdated = new Date().toISOString()

      // Also register with file attachment manager for database consistency
      if (options.messageId) {
        await fileAttachmentManager.attachFileToMessage(
          options.messageId,
          this.activeConversationId,
          fileContext.intelligence.filePath, // Using filePath as fileId for now
          { attachmentType: 'context', includeInConversation: true }
        )
      }

      // Cache the conversation context
      intelligenceCacheManager.set(
        `conversation:${this.activeConversationId}`,
        context,
        60 * 60 * 1000 // 1 hour TTL
      )

      console.log('[CONVERSATION-INTELLIGENCE] ✅ File attached to conversation:', filePath)
      return fileContext

    } catch (error: any) {
      console.error('[CONVERSATION-INTELLIGENCE] 💥 Error attaching file:', error)
      return null
    }
  }

  /**
   * Get all file contexts for the active conversation
   */
  getConversationFiles(): ConversationFileContext[] {
    if (!this.activeConversationId) {
      return []
    }

    const context = this.conversations.get(this.activeConversationId)
    if (!context) {
      return []
    }

    return Array.from(context.files.values())
  }

  /**
   * Get specific file context from conversation
   */
  getFileContext(filePath: string, vaultPath: string): ConversationFileContext | null {
    if (!this.activeConversationId) {
      return null
    }

    const context = this.conversations.get(this.activeConversationId)
    if (!context) {
      return null
    }

    const fileKey = `${filePath}:${vaultPath}`
    return context.files.get(fileKey) || null
  }

  /**
   * Build context string for AI from all attached files
   */
  buildContextForAI(): string {
    const files = this.getConversationFiles()
    
    if (files.length === 0) {
      return ''
    }

    const contextParts: string[] = []
    
    contextParts.push('=== ATTACHED FILES CONTEXT ===')
    
    for (const file of files) {
      contextParts.push(`\n--- File: ${file.filePath} ---`)
      
      if (file.intelligence.extractedContent) {
        // Truncate very long content
        const content = file.intelligence.extractedContent
        const truncated = content.length > 2000 ? content.substring(0, 2000) + '...[truncated]' : content
        contextParts.push(truncated)
      }
      
      // Add key ideas if available
      if (file.intelligence.intelligence?.key_ideas?.length > 0) {
        contextParts.push('\nKey Ideas:')
        file.intelligence.intelligence.key_ideas.slice(0, 3).forEach((idea: any, index: number) => {
          contextParts.push(`${index + 1}. ${idea.text || idea}`)
        })
      }
    }
    
    contextParts.push('\n=== END ATTACHED FILES CONTEXT ===\n')
    
    return contextParts.join('\n')
  }

  /**
   * Get conversation statistics
   */
  getConversationStats(conversationId?: string): ConversationIntelligenceContext | null {
    const targetId = conversationId || this.activeConversationId
    if (!targetId) {
      return null
    }

    return this.conversations.get(targetId) || null
  }

  /**
   * Remove file from conversation
   */
  removeFileFromConversation(filePath: string, vaultPath: string): boolean {
    if (!this.activeConversationId) {
      return false
    }

    const context = this.conversations.get(this.activeConversationId)
    if (!context) {
      return false
    }

    const fileKey = `${filePath}:${vaultPath}`
    const removed = context.files.delete(fileKey)
    
    if (removed) {
      context.totalFiles = context.files.size
      context.lastUpdated = new Date().toISOString()
      
      // Update cache
      intelligenceCacheManager.set(
        `conversation:${this.activeConversationId}`, 
        context, 
        60 * 60 * 1000
      )
      
      console.log('[CONVERSATION-INTELLIGENCE] 🗑️ Removed file from conversation:', filePath)
    }
    
    return removed
  }

  /**
   * Clear conversation context
   */
  clearConversation(conversationId?: string): void {
    const targetId = conversationId || this.activeConversationId
    if (!targetId) {
      return
    }

    this.conversations.delete(targetId)
    intelligenceCacheManager.delete(`conversation:${targetId}`)
    
    console.log('[CONVERSATION-INTELLIGENCE] 🗑️ Cleared conversation context:', targetId)
  }

  /**
   * Get all conversation IDs
   */
  getConversationIds(): string[] {
    return Array.from(this.conversations.keys())
  }

  /**
   * Load conversation context from cache
   */
  loadConversationFromCache(conversationId: string): boolean {
    const cached = intelligenceCacheManager.get<ConversationIntelligenceContext>(`conversation:${conversationId}`)
    
    if (cached) {
      this.conversations.set(conversationId, cached)
      console.log('[CONVERSATION-INTELLIGENCE] 🔥 Loaded conversation from cache:', conversationId)
      return true
    }
    
    return false
  }

  /**
   * Estimate intelligence data size
   */
  private estimateIntelligenceSize(intelligence: ProcessedIntelligence): number {
    try {
      return JSON.stringify(intelligence).length * 2 // Rough UTF-16 estimate
    } catch {
      return 1000 // Fallback estimate
    }
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    totalConversations: number
    totalFiles: number
    estimatedMemoryUsage: number
    activeConversation: string | null
  } {
    let totalFiles = 0
    let estimatedMemoryUsage = 0
    
    for (const context of this.conversations.values()) {
      totalFiles += context.totalFiles
      estimatedMemoryUsage += context.totalIntelligenceSize
    }
    
    return {
      totalConversations: this.conversations.size,
      totalFiles,
      estimatedMemoryUsage,
      activeConversation: this.activeConversationId
    }
  }

  /**
   * Cleanup old conversations to free memory
   */
  cleanupOldConversations(maxAge: number = 24 * 60 * 60 * 1000): number {
    const now = Date.now()
    let cleaned = 0
    
    for (const [conversationId, context] of this.conversations.entries()) {
      const age = now - new Date(context.lastUpdated).getTime()
      
      if (age > maxAge && conversationId !== this.activeConversationId) {
        this.clearConversation(conversationId)
        cleaned++
      }
    }
    
    console.log('[CONVERSATION-INTELLIGENCE] 🧹 Cleaned up', cleaned, 'old conversations')
    return cleaned
  }
}

// Export singleton instance
export const conversationIntelligenceManager = new ConversationIntelligenceManager()
