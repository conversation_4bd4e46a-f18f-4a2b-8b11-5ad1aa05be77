/**
 * Deprecated Services Shim
 * 
 * 🚨 DEPRECATION NOTICE: This file provides backward compatibility for deprecated services
 * that have been migrated to canonical services. This shim will be removed in a future version.
 * 
 * Migration Path:
 * - Replace intelligenceStorageService calls with intelligenceClient.write()/read()
 * - Replace fileIntelligenceService calls with fileAnalysisService.analyzeDocument()
 * 
 * @deprecated Use canonical services directly instead
 */

import { intelligence } from '../api/UnifiedAPIClient'
import { fileAnalysisService } from './fileAnalysisService'

// ============================================================================
// Intelligence Storage Shim Functions
// ============================================================================

/**
 * Shim for clearVaultIntelligenceState
 * @deprecated Use intelligenceClient.write() instead
 */
export const clearVaultIntelligenceState = async (vaultPath: string): Promise<void> => {
  console.warn('[DEPRECATION] clearVaultIntelligenceState is deprecated. Use intelligenceClient.write() instead.')
  
  try {
    // Clear vault intelligence by writing empty state
    await intelligence.write(`${vaultPath}/.intelligence/vault_intelligence.json`, vaultPath, { json: {} })
    console.log(`[SHIM] Cleared vault intelligence state for: ${vaultPath}`)
  } catch (error) {
    console.error('[SHIM] Error clearing vault intelligence state:', error)
    throw error
  }
}

/**
 * Shim for storeFileIntelligence
 * @deprecated Use intelligenceClient.write() instead
 */
export const storeFileIntelligence = async (filePath: string, intelligenceData: any, options: any): Promise<void> => {
  console.warn('[DEPRECATION] storeFileIntelligence is deprecated. Use intelligenceClient.write() instead.')
  
  try {
    // Extract vault path from options or infer from file path
    const vaultPath = options?.vaultPath || filePath.split('/').slice(0, -1).join('/')
    
    // Store intelligence data
    await intelligence.write(filePath, vaultPath, { json: intelligenceData })
    console.log(`[SHIM] Stored file intelligence for: ${filePath}`)
  } catch (error) {
    console.error('[SHIM] Error storing file intelligence:', error)
    throw error
  }
}

/**
 * Shim for getFileIntelligence
 * @deprecated Use intelligenceClient.read() instead
 */
export const getFileIntelligence = async (filePath: string, vaultPath?: string): Promise<any> => {
  console.warn('[DEPRECATION] getFileIntelligence is deprecated. Use intelligenceClient.read() instead.')
  
  try {
    // Infer vault path if not provided
    const targetVaultPath = vaultPath || filePath.split('/').slice(0, -1).join('/')
    
    // Retrieve intelligence data
    const result = await intelligence.read(filePath, targetVaultPath)
    console.log(`[SHIM] Retrieved file intelligence for: ${filePath}`)
    return result?.data || null
  } catch (error) {
    console.error('[SHIM] Error retrieving file intelligence:', error)
    throw error
  }
}

// ============================================================================
// File Intelligence Shim Functions
// ============================================================================

/**
 * Shim for analyzeFile
 * @deprecated Use fileAnalysisService.analyzeDocument() instead
 */
export const analyzeFile = async (filePath: string, content: string, config: any = {}): Promise<any> => {
  console.warn('[DEPRECATION] analyzeFile is deprecated. Use fileAnalysisService.analyzeDocument() instead.')
  
  try {
    // Use canonical FileAnalysisService
    const result = await fileAnalysisService.analyzeDocument(content, config)
    console.log(`[SHIM] Analyzed file: ${filePath}`)
    return result
  } catch (error) {
    console.error('[SHIM] Error analyzing file:', error)
    throw error
  }
}

/**
 * Shim for extractKeyIdeas
 * @deprecated Use fileAnalysisService.analyzeDocument() instead
 */
export const extractKeyIdeas = async (content: string, config: any = {}): Promise<any[]> => {
  console.warn('[DEPRECATION] extractKeyIdeas is deprecated. Use fileAnalysisService.analyzeDocument() instead.')
  
  try {
    // Use canonical FileAnalysisService to get key ideas
    const result = await fileAnalysisService.analyzeDocument(content, config)
    console.log(`[SHIM] Extracted key ideas from content`)
    return result?.key_ideas || []
  } catch (error) {
    console.error('[SHIM] Error extracting key ideas:', error)
    throw error
  }
}

// ============================================================================
// Shim Service Objects (for backward compatibility)
// ============================================================================

/**
 * @deprecated Use intelligenceClient directly instead
 */
export const intelligenceStorageService = {
  clearVaultIntelligenceState,
  storeFileIntelligence,
  getFileIntelligence
}

/**
 * @deprecated Use fileAnalysisService directly instead
 */
export const fileIntelligenceService = {
  analyzeFile,
  extractKeyIdeas
}
