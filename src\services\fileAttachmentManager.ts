/**
 * File Attachment Manager
 * 
 * Manages file attachments at both message and conversation levels,
 * ensuring proper database relationships and context flow.
 */

export interface FileAttachmentRelationship {
  attachmentId: string
  messageId?: string
  conversationId: string
  fileId: string
  attachmentType: 'attachment' | 'reference' | 'context'
  createdAt: string
}

export interface ConversationFileContext {
  conversationId: string
  files: FileAttachmentRelationship[]
  totalFiles: number
  lastUpdated: string
}

export interface AttachmentOptions {
  attachmentType?: 'attachment' | 'reference' | 'context'
  includeInConversation?: boolean
  messageId?: string
}

class FileAttachmentManager {
  private conversationFileCache = new Map<string, ConversationFileContext>()

  /**
   * Attach file to message and optionally to conversation
   */
  async attachFileToMessage(
    messageId: string,
    conversationId: string,
    fileId: string,
    options: AttachmentOptions = {}
  ): Promise<{ messageAttachmentId?: string; conversationAttachmentId?: string }> {
    try {
      const { attachmentType = 'attachment', includeInConversation = true } = options

      console.log('[FILE-ATTACHMENT] 📎 Attaching file to message:', { messageId, fileId, attachmentType })

      const result: { messageAttachmentId?: string; conversationAttachmentId?: string } = {}

      // Attach to message
      if (window.electronAPI?.files?.addFileAttachment) {
        const messageAttachmentId = await window.electronAPI.files.addFileAttachment(
          messageId,
          fileId,
          attachmentType
        )
        result.messageAttachmentId = messageAttachmentId
        console.log('[FILE-ATTACHMENT] ✅ File attached to message:', messageAttachmentId)
      }

      // Attach to conversation if requested
      if (includeInConversation) {
        const conversationAttachmentId = await this.attachFileToConversation(
          conversationId,
          fileId,
          { attachmentType, messageId }
        )
        result.conversationAttachmentId = conversationAttachmentId
      }

      // Update cache
      this.updateConversationFileCache(conversationId)

      return result
    } catch (error: any) {
      console.error('[FILE-ATTACHMENT] 💥 Error attaching file to message:', error)
      throw error
    }
  }

  /**
   * Attach file to conversation
   */
  async attachFileToConversation(
    conversationId: string,
    fileId: string,
    options: AttachmentOptions = {}
  ): Promise<string | null> {
    try {
      const { attachmentType = 'context', messageId } = options

      console.log('[FILE-ATTACHMENT] 📎 Attaching file to conversation:', { conversationId, fileId, attachmentType })

      // Check if file is already attached to conversation
      const existingAttachment = await this.getConversationFileAttachment(conversationId, fileId)
      
      if (existingAttachment) {
        console.log('[FILE-ATTACHMENT] ♻️ File already attached to conversation:', existingAttachment)
        return existingAttachment.attachmentId
      }

      // Attach file to conversation
      if (window.electronAPI?.db?.files?.addFileAttachment) {
        const attachmentId = await window.electronAPI.db.files.addFileAttachment(
          conversationId,
          fileId
        )
        
        console.log('[FILE-ATTACHMENT] ✅ File attached to conversation:', attachmentId)
        
        // Update cache
        this.updateConversationFileCache(conversationId)
        
        return attachmentId
      }

      return null
    } catch (error: any) {
      console.error('[FILE-ATTACHMENT] 💥 Error attaching file to conversation:', error)
      return null
    }
  }

  /**
   * Get all files attached to a conversation
   */
  async getConversationFiles(conversationId: string): Promise<FileAttachmentRelationship[]> {
    try {
      // Check cache first
      const cached = this.conversationFileCache.get(conversationId)
      if (cached && Date.now() - new Date(cached.lastUpdated).getTime() < 5 * 60 * 1000) {
        return cached.files
      }

      console.log('[FILE-ATTACHMENT] 🔍 Loading conversation files:', conversationId)

      // Load from database
      const files: FileAttachmentRelationship[] = []

      // Get message-level attachments
      if (window.electronAPI?.db?.getMessages) {
        const messages = await window.electronAPI.db.getMessages(conversationId)
        
        for (const message of messages) {
          if (window.electronAPI?.files?.getMessageFiles) {
            const messageFiles = await window.electronAPI.files.getMessageFiles(message.id)
            
            for (const fileWithAttachment of messageFiles) {
              files.push({
                attachmentId: fileWithAttachment.attachment_id || `msg_${message.id}_${fileWithAttachment.id}`,
                messageId: message.id.toString(),
                conversationId,
                fileId: fileWithAttachment.id,
                attachmentType: fileWithAttachment.attachment_type || 'attachment',
                createdAt: fileWithAttachment.attachment_created_at || fileWithAttachment.created_at
              })
            }
          }
        }
      }

      // Update cache
      const context: ConversationFileContext = {
        conversationId,
        files,
        totalFiles: files.length,
        lastUpdated: new Date().toISOString()
      }
      this.conversationFileCache.set(conversationId, context)

      console.log('[FILE-ATTACHMENT] ✅ Loaded', files.length, 'files for conversation:', conversationId)
      return files
    } catch (error: any) {
      console.error('[FILE-ATTACHMENT] 💥 Error loading conversation files:', error)
      return []
    }
  }

  /**
   * Get specific file attachment in conversation
   */
  async getConversationFileAttachment(
    conversationId: string,
    fileId: string
  ): Promise<FileAttachmentRelationship | null> {
    const files = await this.getConversationFiles(conversationId)
    return files.find(f => f.fileId === fileId) || null
  }

  /**
   * Remove file attachment from message
   */
  async removeFileFromMessage(messageId: string, fileId: string): Promise<boolean> {
    try {
      console.log('[FILE-ATTACHMENT] 🗑️ Removing file from message:', { messageId, fileId })

      if (window.electronAPI?.files?.removeFileAttachment) {
        const result = await window.electronAPI.files.removeFileAttachment(messageId, fileId)
        
        if (result) {
          console.log('[FILE-ATTACHMENT] ✅ File removed from message')
          return true
        }
      }

      return false
    } catch (error: any) {
      console.error('[FILE-ATTACHMENT] 💥 Error removing file from message:', error)
      return false
    }
  }

  /**
   * Remove file attachment from conversation
   */
  async removeFileFromConversation(conversationId: string, fileId: string): Promise<boolean> {
    try {
      console.log('[FILE-ATTACHMENT] 🗑️ Removing file from conversation:', { conversationId, fileId })

      // Clear from cache
      this.conversationFileCache.delete(conversationId)

      // Note: Actual removal would depend on database implementation
      // For now, we'll just clear the cache and let it reload
      
      console.log('[FILE-ATTACHMENT] ✅ File removed from conversation cache')
      return true
    } catch (error: any) {
      console.error('[FILE-ATTACHMENT] 💥 Error removing file from conversation:', error)
      return false
    }
  }

  /**
   * Get file attachment statistics for conversation
   */
  async getConversationFileStats(conversationId: string): Promise<{
    totalFiles: number
    fileTypes: Record<string, number>
    attachmentTypes: Record<string, number>
    oldestAttachment?: string
    newestAttachment?: string
  }> {
    const files = await this.getConversationFiles(conversationId)
    
    const fileTypes: Record<string, number> = {}
    const attachmentTypes: Record<string, number> = {}
    const dates = files.map(f => new Date(f.createdAt).getTime()).filter(d => !isNaN(d))

    for (const file of files) {
      // Count attachment types
      attachmentTypes[file.attachmentType] = (attachmentTypes[file.attachmentType] || 0) + 1
      
      // Note: File type would need to be loaded from file record
      // For now, we'll skip file type counting
    }

    return {
      totalFiles: files.length,
      fileTypes,
      attachmentTypes,
      oldestAttachment: dates.length > 0 ? new Date(Math.min(...dates)).toISOString() : undefined,
      newestAttachment: dates.length > 0 ? new Date(Math.max(...dates)).toISOString() : undefined
    }
  }

  /**
   * Update conversation file cache
   */
  private async updateConversationFileCache(conversationId: string): Promise<void> {
    try {
      // Clear cache to force reload
      this.conversationFileCache.delete(conversationId)
      
      // Reload files
      await this.getConversationFiles(conversationId)
    } catch (error) {
      console.warn('[FILE-ATTACHMENT] Could not update conversation file cache:', error)
    }
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.conversationFileCache.clear()
    console.log('[FILE-ATTACHMENT] 🗑️ All caches cleared')
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    conversationsCached: number
    totalFiles: number
    cacheKeys: string[]
  } {
    let totalFiles = 0
    
    for (const context of this.conversationFileCache.values()) {
      totalFiles += context.totalFiles
    }

    return {
      conversationsCached: this.conversationFileCache.size,
      totalFiles,
      cacheKeys: Array.from(this.conversationFileCache.keys())
    }
  }

  /**
   * Validate file attachment relationships
   */
  async validateConversationFileRelationships(conversationId: string): Promise<{
    valid: boolean
    issues: string[]
    recommendations: string[]
  }> {
    const issues: string[] = []
    const recommendations: string[] = []

    try {
      const files = await this.getConversationFiles(conversationId)
      
      // Check for duplicate attachments
      const fileIds = files.map(f => f.fileId)
      const duplicates = fileIds.filter((id, index) => fileIds.indexOf(id) !== index)
      
      if (duplicates.length > 0) {
        issues.push(`Duplicate file attachments found: ${duplicates.join(', ')}`)
        recommendations.push('Remove duplicate file attachments to improve performance')
      }

      // Check for orphaned attachments (files without valid message references)
      const orphanedFiles = files.filter(f => f.messageId && !f.messageId.trim())
      
      if (orphanedFiles.length > 0) {
        issues.push(`${orphanedFiles.length} orphaned file attachments found`)
        recommendations.push('Clean up orphaned file attachments')
      }

      return {
        valid: issues.length === 0,
        issues,
        recommendations
      }
    } catch (error: any) {
      return {
        valid: false,
        issues: [`Validation failed: ${error.message}`],
        recommendations: ['Check database connectivity and file attachment APIs']
      }
    }
  }
}

// Export singleton instance
export const fileAttachmentManager = new FileAttachmentManager()
