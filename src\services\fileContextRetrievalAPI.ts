/**
 * File Context Retrieval API
 * 
 * Unified API to retrieve all file intelligence context for conversations,
 * enabling proper context rebuilding and seamless file intelligence access.
 */

import { conversationIntelligenceManager, ConversationFileContext } from './conversationIntelligenceManager'
import { fileAttachmentManager, FileAttachmentRelationship } from './fileAttachmentManager'
import { unifiedIntelligenceService, ProcessedIntelligence } from './unifiedIntelligenceService'
import { intelligenceCacheManager } from './intelligenceCacheManager'

export interface FileContextSummary {
  filePath: string
  vaultPath: string
  fileName: string
  fileType: string
  attachmentType: string
  messageIds: string[]
  hasIntelligence: boolean
  intelligenceSize: number
  lastAccessed: string
  attachedAt: string
}

export interface ConversationContextSummary {
  conversationId: string
  totalFiles: number
  totalIntelligenceSize: number
  files: FileContextSummary[]
  lastUpdated: string
  cacheStatus: 'cached' | 'loaded' | 'partial' | 'error'
}

export interface ContextRetrievalOptions {
  includeIntelligenceData?: boolean
  includeFileContent?: boolean
  maxContentLength?: number
  forceReload?: boolean
}

class FileContextRetrievalAPI {
  /**
   * Get complete file context for a conversation
   */
  async getConversationFileContext(
    conversationId: string,
    options: ContextRetrievalOptions = {}
  ): Promise<ConversationContextSummary> {
    try {
      console.log('[FILE-CONTEXT-API] 🔍 Retrieving conversation file context:', conversationId)

      // Set active conversation
      conversationIntelligenceManager.setActiveConversation(conversationId)

      // Get conversation files from intelligence manager
      const intelligenceFiles = conversationIntelligenceManager.getConversationFiles()
      
      // Get file attachments from attachment manager
      const attachmentFiles = await fileAttachmentManager.getConversationFiles(conversationId)

      // Merge and deduplicate files
      const allFiles = this.mergeFileContexts(intelligenceFiles, attachmentFiles)

      // Build file summaries
      const fileSummaries: FileContextSummary[] = []
      let totalIntelligenceSize = 0

      for (const file of allFiles) {
        const summary = await this.buildFileContextSummary(file, options)
        fileSummaries.push(summary)
        totalIntelligenceSize += summary.intelligenceSize
      }

      const contextSummary: ConversationContextSummary = {
        conversationId,
        totalFiles: fileSummaries.length,
        totalIntelligenceSize,
        files: fileSummaries,
        lastUpdated: new Date().toISOString(),
        cacheStatus: 'loaded'
      }

      // Cache the summary
      intelligenceCacheManager.set(
        `context-summary:${conversationId}`,
        contextSummary,
        15 * 60 * 1000 // 15 minutes TTL
      )

      console.log('[FILE-CONTEXT-API] ✅ Retrieved context for', fileSummaries.length, 'files')
      return contextSummary

    } catch (error: any) {
      console.error('[FILE-CONTEXT-API] 💥 Error retrieving conversation file context:', error)
      
      return {
        conversationId,
        totalFiles: 0,
        totalIntelligenceSize: 0,
        files: [],
        lastUpdated: new Date().toISOString(),
        cacheStatus: 'error'
      }
    }
  }

  /**
   * Get intelligence data for specific file in conversation
   */
  async getFileIntelligence(
    conversationId: string,
    filePath: string,
    vaultPath: string,
    options: ContextRetrievalOptions = {}
  ): Promise<ProcessedIntelligence | null> {
    try {
      console.log('[FILE-CONTEXT-API] 🧠 Retrieving file intelligence:', filePath)

      // Check conversation intelligence manager first
      const fileContext = conversationIntelligenceManager.getFileContext(filePath, vaultPath)
      
      if (fileContext) {
        console.log('[FILE-CONTEXT-API] ✅ Found intelligence in conversation context')
        return fileContext.intelligence
      }

      // Fallback to unified intelligence service
      const intelligence = await unifiedIntelligenceService.getIntelligence(
        { filePath, vaultPath },
        { forceReprocess: options.forceReload }
      )

      return intelligence
    } catch (error: any) {
      console.error('[FILE-CONTEXT-API] 💥 Error retrieving file intelligence:', error)
      return null
    }
  }

  /**
   * Build context string for AI from conversation files
   */
  async buildAIContextString(
    conversationId: string,
    options: ContextRetrievalOptions = {}
  ): Promise<string> {
    try {
      const { maxContentLength = 4000 } = options

      // Get conversation context
      const context = await this.getConversationFileContext(conversationId, options)
      
      if (context.files.length === 0) {
        return ''
      }

      const contextParts: string[] = []
      contextParts.push('=== CONVERSATION FILE CONTEXT ===')
      contextParts.push(`Files attached: ${context.totalFiles}`)
      contextParts.push('')

      let currentLength = contextParts.join('\n').length

      for (const file of context.files) {
        if (currentLength >= maxContentLength) {
          contextParts.push('...[additional files truncated]')
          break
        }

        contextParts.push(`--- ${file.fileName} ---`)
        contextParts.push(`Type: ${file.fileType} | Attachment: ${file.attachmentType}`)
        
        if (file.hasIntelligence && options.includeIntelligenceData) {
          const intelligence = await this.getFileIntelligence(
            conversationId,
            file.filePath,
            file.vaultPath,
            options
          )

          if (intelligence?.extractedContent) {
            const remainingLength = maxContentLength - currentLength
            const content = intelligence.extractedContent
            const truncatedContent = content.length > remainingLength 
              ? content.substring(0, remainingLength) + '...[truncated]'
              : content

            contextParts.push('Content:')
            contextParts.push(truncatedContent)
            currentLength += truncatedContent.length
          }

          // Add key ideas if available
          if (intelligence?.intelligence?.key_ideas?.length > 0) {
            contextParts.push('Key Ideas:')
            intelligence.intelligence.key_ideas.slice(0, 3).forEach((idea: any, index: number) => {
              contextParts.push(`${index + 1}. ${idea.text || idea}`)
            })
          }
        }

        contextParts.push('')
        currentLength = contextParts.join('\n').length
      }

      contextParts.push('=== END FILE CONTEXT ===')
      
      return contextParts.join('\n')
    } catch (error: any) {
      console.error('[FILE-CONTEXT-API] 💥 Error building AI context string:', error)
      return ''
    }
  }

  /**
   * Refresh conversation file context
   */
  async refreshConversationContext(conversationId: string): Promise<ConversationContextSummary> {
    // Clear caches
    intelligenceCacheManager.delete(`context-summary:${conversationId}`)
    conversationIntelligenceManager.clearConversation(conversationId)
    fileAttachmentManager.clearCache()

    // Reload context
    return await this.getConversationFileContext(conversationId, { forceReload: true })
  }

  /**
   * Merge file contexts from different sources
   */
  private mergeFileContexts(
    intelligenceFiles: ConversationFileContext[],
    attachmentFiles: FileAttachmentRelationship[]
  ): Array<ConversationFileContext | FileAttachmentRelationship> {
    const merged = new Map<string, ConversationFileContext | FileAttachmentRelationship>()

    // Add intelligence files
    for (const file of intelligenceFiles) {
      const key = `${file.filePath}:${file.vaultPath}`
      merged.set(key, file)
    }

    // Add attachment files (if not already present)
    for (const file of attachmentFiles) {
      // Note: FileAttachmentRelationship doesn't have filePath/vaultPath
      // We'd need to enhance it or get file details from database
      const key = `attachment:${file.fileId}`
      if (!merged.has(key)) {
        merged.set(key, file)
      }
    }

    return Array.from(merged.values())
  }

  /**
   * Build file context summary
   */
  private async buildFileContextSummary(
    file: ConversationFileContext | FileAttachmentRelationship,
    options: ContextRetrievalOptions
  ): Promise<FileContextSummary> {
    // Check if it's a ConversationFileContext or FileAttachmentRelationship
    if ('intelligence' in file) {
      // ConversationFileContext
      return {
        filePath: file.filePath,
        vaultPath: file.vaultPath,
        fileName: file.filePath.split(/[/\\]/).pop() || 'Unknown',
        fileType: 'document', // Would need to determine from file extension
        attachmentType: 'intelligence',
        messageIds: file.messageIds,
        hasIntelligence: true,
        intelligenceSize: JSON.stringify(file.intelligence).length,
        lastAccessed: file.lastAccessed,
        attachedAt: file.attachedAt
      }
    } else {
      // FileAttachmentRelationship
      return {
        filePath: `file:${file.fileId}`, // Placeholder
        vaultPath: '', // Would need to get from database
        fileName: `File ${file.fileId}`,
        fileType: 'unknown',
        attachmentType: file.attachmentType,
        messageIds: file.messageId ? [file.messageId] : [],
        hasIntelligence: false,
        intelligenceSize: 0,
        lastAccessed: file.createdAt,
        attachedAt: file.createdAt
      }
    }
  }

  /**
   * Get API statistics
   */
  getAPIStats(): {
    cacheHits: number
    totalRequests: number
    averageResponseTime: number
  } {
    // This would be implemented with proper metrics tracking
    return {
      cacheHits: 0,
      totalRequests: 0,
      averageResponseTime: 0
    }
  }
}

// Export singleton instance
export const fileContextRetrievalAPI = new FileContextRetrievalAPI()
