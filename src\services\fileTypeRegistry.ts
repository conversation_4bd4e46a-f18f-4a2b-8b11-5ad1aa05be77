/**
 * Unified File Type Registry Service
 * Single source of truth for file type detection and plugin management
 */

import {
  FileType,
  FileTypeInfo,
  FileTypePlugin,
  FileTypeRegistry,
  EXTENSION_TO_TYPE_MAP,
  MIME_TYPE_MAP
} from '../types/fileTypes';

// Re-export types for convenience
export type { FileType, FileTypeInfo, FileTypePlugin, FileTypeRegistry };

/**
 * Detect file type from filename and optional content
 */
export const detectFileType = (fileName: string, content?: string): FileTypeInfo => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  
  // Get base type from extension
  let baseType: FileType = EXTENSION_TO_TYPE_MAP[extension] || 'unknown';
  
  // Special case: Check for Mermaid content in markdown files
  if (baseType === 'markdown' && content && isMermaidContent(content)) {
    baseType = 'mermaid';
  }
  
  // Handle unknown extensions
  if (baseType === 'unknown') {
    baseType = 'unsupported';
  }
  
  return createFileTypeInfo(baseType, extension);
};

/**
 * Detect if content is a Mermaid diagram
 */
export const isMermaidContent = (content: string): boolean => {
  const trimmedContent = content.trim();

  const mermaidPatterns = [
    /^graph\s+(TD|TB|BT|RL|LR)/i,
    /^flowchart\s+(TD|TB|BT|RL|LR)/i,
    /^sequenceDiagram/i,
    /^classDiagram/i,
    /^stateDiagram/i,
    /^erDiagram/i,
    /^gantt/i,
    /^pie\s+title/i,
    /^journey/i,
    /^gitgraph/i,
    /^mindmap/i,
    /^timeline/i,
    /^quadrantChart/i,
    /^requirement/i,
    /^C4Context/i,
  ];

  return mermaidPatterns.some(pattern => pattern.test(trimmedContent));
};

/**
 * Create FileTypeInfo object for a given type and extension
 */
const createFileTypeInfo = (type: FileType, extension: string): FileTypeInfo => {
  const baseInfo = {
    type,
    extension,
    mimeType: MIME_TYPE_MAP[type] || 'application/octet-stream',
  };

  switch (type) {
    case 'pdf':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true,
        extractionMethod: 'pdf-parse',
        displayName: 'PDF Document'
      };

    case 'markdown':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Markdown Document'
      };

    case 'mermaid':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Mermaid Diagram'
      };

    case 'image':
      return {
        ...baseInfo,
        canExtractText: false,
        canAnnotate: false,
        requiresProcessing: false,
        extractionMethod: 'none',
        displayName: 'Image File'
      };

    case 'text':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Text File'
      };

    case 'code':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Code File'
      };

    case 'html':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'HTML File'
      };

    case 'json':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'JSON File'
      };

    case 'xml':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'XML File'
      };

    case 'yaml':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'YAML File'
      };

    case 'csv':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'CSV File'
      };

    case 'excel':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: false,
        requiresProcessing: true,
        extractionMethod: 'plugin-based',
        displayName: 'Excel Spreadsheet'
      };

    case 'word':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true,
        extractionMethod: 'plugin-based',
        displayName: 'Word Document'
      };

    case 'powerpoint':
      return {
        ...baseInfo,
        canExtractText: true,
        canAnnotate: false,
        requiresProcessing: true,
        extractionMethod: 'plugin-based',
        displayName: 'PowerPoint Presentation'
      };

    case 'unknown':
    case 'unsupported':
    default:
      return {
        ...baseInfo,
        type: 'unsupported',
        canExtractText: false,
        canAnnotate: false,
        requiresProcessing: false,
        extractionMethod: 'none',
        displayName: 'Unsupported File'
      };
  }
};

/**
 * Simple file type registry implementation
 */
class SimpleFileTypeRegistry implements FileTypeRegistry {
  private plugins: Map<FileType, FileTypePlugin> = new Map();

  registerPlugin(fileType: FileType, plugin: FileTypePlugin): void {
    this.plugins.set(fileType, plugin);
  }

  getPlugin(fileType: FileType): FileTypePlugin | null {
    return this.plugins.get(fileType) || null;
  }

  listPlugins(): FileTypePlugin[] {
    return Array.from(this.plugins.values());
  }
}

// Export singleton instance
export const fileTypeRegistry = new SimpleFileTypeRegistry();

/**
 * Check if a file type requires Electron-side processing
 */
export const requiresElectronProcessing = (fileType: FileType): boolean => {
  return ['pdf', 'image', 'excel', 'word', 'powerpoint'].includes(fileType);
};

/**
 * Get extraction method for a file type
 */
export const getExtractionMethod = (fileType: FileType): string => {
  const typeInfo = createFileTypeInfo(fileType, '');
  return typeInfo.extractionMethod;
};
