/**
 * Intelligence Cache Manager
 * 
 * Centralized caching layer for intelligence data to avoid repeated file system reads
 * and improve performance during chat sessions.
 */

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  accessCount: number
  lastAccessed: number
  size: number
  ttl?: number
}

export interface CacheStats {
  totalEntries: number
  totalSize: number
  hitRate: number
  missRate: number
  averageAccessCount: number
  oldestEntry?: number
  newestEntry?: number
}

export interface CacheConfig {
  maxSize: number // Maximum cache size in bytes
  maxEntries: number // Maximum number of entries
  defaultTTL: number // Default time-to-live in milliseconds
  cleanupInterval: number // Cleanup interval in milliseconds
}

class IntelligenceCacheManager {
  private cache = new Map<string, CacheEntry>()
  private hits = 0
  private misses = 0
  private cleanupTimer?: NodeJS.Timeout
  
  private config: CacheConfig = {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 1000,
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    cleanupInterval: 5 * 60 * 1000 // 5 minutes
  }

  constructor(config?: Partial<CacheConfig>) {
    if (config) {
      this.config = { ...this.config, ...config }
    }
    
    this.startCleanupTimer()
    console.log('[INTELLIGENCE-CACHE] 🚀 Cache manager initialized with config:', this.config)
  }

  /**
   * Get data from cache
   */
  get<T = any>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.misses++
      return null
    }

    // Check TTL
    const now = Date.now()
    const ttl = entry.ttl || this.config.defaultTTL
    if (now - entry.timestamp > ttl) {
      this.cache.delete(key)
      this.misses++
      console.log('[INTELLIGENCE-CACHE] ⏰ Cache entry expired:', key)
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = now
    this.hits++

    console.log('[INTELLIGENCE-CACHE] 🎯 Cache hit for:', key)
    return entry.data as T
  }

  /**
   * Set data in cache
   */
  set<T = any>(key: string, data: T, ttl?: number): boolean {
    try {
      const now = Date.now()
      const size = this.estimateSize(data)
      
      // Check if we need to make space
      if (!this.canFit(size)) {
        this.evictLRU(size)
      }

      const entry: CacheEntry<T> = {
        data,
        timestamp: now,
        accessCount: 1,
        lastAccessed: now,
        size,
        ttl
      }

      this.cache.set(key, entry)
      console.log('[INTELLIGENCE-CACHE] 💾 Cached data for:', key, `(${size} bytes)`)
      
      return true
    } catch (error) {
      console.error('[INTELLIGENCE-CACHE] 💥 Error setting cache:', error)
      return false
    }
  }

  /**
   * Check if key exists in cache (without updating access stats)
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check TTL
    const now = Date.now()
    const ttl = entry.ttl || this.config.defaultTTL
    if (now - entry.timestamp > ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      console.log('[INTELLIGENCE-CACHE] 🗑️ Deleted cache entry:', key)
    }
    return deleted
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
    this.hits = 0
    this.misses = 0
    console.log('[INTELLIGENCE-CACHE] 🗑️ Cache cleared')
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values())
    const totalRequests = this.hits + this.misses
    
    return {
      totalEntries: this.cache.size,
      totalSize: entries.reduce((sum, entry) => sum + entry.size, 0),
      hitRate: totalRequests > 0 ? this.hits / totalRequests : 0,
      missRate: totalRequests > 0 ? this.misses / totalRequests : 0,
      averageAccessCount: entries.length > 0 ? entries.reduce((sum, entry) => sum + entry.accessCount, 0) / entries.length : 0,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : undefined,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : undefined
    }
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache entries for debugging
   */
  entries(): Array<{ key: string; entry: CacheEntry }> {
    return Array.from(this.cache.entries()).map(([key, entry]) => ({ key, entry }))
  }

  /**
   * Check if new data can fit in cache
   */
  private canFit(size: number): boolean {
    const currentSize = this.getCurrentSize()
    const currentEntries = this.cache.size
    
    return (currentSize + size <= this.config.maxSize) && 
           (currentEntries < this.config.maxEntries)
  }

  /**
   * Get current cache size
   */
  private getCurrentSize(): number {
    return Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.size, 0)
  }

  /**
   * Evict least recently used entries to make space
   */
  private evictLRU(neededSize: number): void {
    const entries = Array.from(this.cache.entries())
    
    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
    
    let freedSize = 0
    let evicted = 0
    
    for (const [key, entry] of entries) {
      this.cache.delete(key)
      freedSize += entry.size
      evicted++
      
      console.log('[INTELLIGENCE-CACHE] 🧹 Evicted LRU entry:', key)
      
      // Stop when we have enough space or evicted enough entries
      if (freedSize >= neededSize || this.cache.size < this.config.maxEntries * 0.8) {
        break
      }
    }
    
    console.log('[INTELLIGENCE-CACHE] 🧹 Evicted', evicted, 'entries, freed', freedSize, 'bytes')
  }

  /**
   * Estimate size of data in bytes
   */
  private estimateSize(data: any): number {
    try {
      if (typeof data === 'string') {
        return data.length * 2 // Rough estimate for UTF-16
      }
      
      if (typeof data === 'object' && data !== null) {
        return JSON.stringify(data).length * 2
      }
      
      return 100 // Default estimate for other types
    } catch (error) {
      return 100 // Fallback estimate
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * Stop cleanup timer
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    let cleaned = 0
    
    for (const [key, entry] of this.cache.entries()) {
      const ttl = entry.ttl || this.config.defaultTTL
      if (now - entry.timestamp > ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }
    
    if (cleaned > 0) {
      console.log('[INTELLIGENCE-CACHE] 🧹 Cleaned up', cleaned, 'expired entries')
    }
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('[INTELLIGENCE-CACHE] ⚙️ Config updated:', this.config)
    
    // Restart cleanup timer if interval changed
    if (newConfig.cleanupInterval) {
      this.stopCleanupTimer()
      this.startCleanupTimer()
    }
  }

  /**
   * Destroy cache manager
   */
  destroy(): void {
    this.stopCleanupTimer()
    this.clear()
    console.log('[INTELLIGENCE-CACHE] 💀 Cache manager destroyed')
  }
}

// Export singleton instance
export const intelligenceCacheManager = new IntelligenceCacheManager()

// Export class for custom instances
export { IntelligenceCacheManager }
