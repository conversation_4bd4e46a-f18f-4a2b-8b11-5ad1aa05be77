/**
 * Intelligence Data Flow Service
 * 
 * Ensures consistent intelligence data flow following PathResolver patterns
 * and kernel pipeline architecture from pre-draft-plugin-design.md
 */

import { extractContextPath, joinLike } from '../utils/vaultPath'

export interface IntelligenceDataFlowConfig {
  vaultPath: string
  filePath: string
  forceReprocess?: boolean
  useKernelPipeline?: boolean
}

export interface IntelligenceFlowResult {
  success: boolean
  data?: any
  artifactsPath?: string
  sessionPath?: string
  source: 'cache' | 'existing' | 'processed' | 'error'
  processingTime: number
  error?: string
}

class IntelligenceDataFlowService {
  private flowCache = new Map<string, { result: IntelligenceFlowResult; timestamp: number }>()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  /**
   * Process intelligence data flow following kernel pipeline patterns
   */
  async processIntelligenceFlow(config: IntelligenceDataFlowConfig): Promise<IntelligenceFlowResult> {
    const startTime = Date.now()
    const { vaultPath, filePath, forceReprocess = false, useKernelPipeline = true } = config
    
    try {
      console.log('[INTELLIGENCE-FLOW] 🚀 Starting intelligence flow for:', filePath)

      // Step 1: Check cache first (unless forced reprocessing)
      if (!forceReprocess) {
        const cached = this.getCachedResult(filePath, vaultPath)
        if (cached) {
          console.log('[INTELLIGENCE-FLOW] 🔥 Cache hit for:', filePath)
          return {
            ...cached,
            processingTime: Date.now() - startTime
          }
        }
      }

      // Step 2: Use kernel pipeline for processing
      if (useKernelPipeline) {
        const kernelResult = await this.processWithKernelPipeline(filePath, vaultPath)
        
        if (kernelResult.success) {
          // Cache successful result
          this.cacheResult(filePath, vaultPath, kernelResult)
          
          return {
            ...kernelResult,
            processingTime: Date.now() - startTime
          }
        }
      }

      // Step 3: Fallback to direct processing
      console.log('[INTELLIGENCE-FLOW] ⚠️ Kernel pipeline failed, using fallback processing')
      const fallbackResult = await this.processFallback(filePath, vaultPath)
      
      return {
        ...fallbackResult,
        processingTime: Date.now() - startTime
      }

    } catch (error: any) {
      console.error('[INTELLIGENCE-FLOW] 💥 Error in intelligence flow:', error)
      
      return {
        success: false,
        source: 'error',
        processingTime: Date.now() - startTime,
        error: error.message || 'Unknown error in intelligence flow'
      }
    }
  }

  /**
   * Process using kernel pipeline (FileCoreService)
   */
  private async processWithKernelPipeline(filePath: string, vaultPath: string): Promise<IntelligenceFlowResult> {
    try {
      console.log('[INTELLIGENCE-FLOW] 🔧 Using kernel pipeline for:', filePath)

      // Use the electron API to process file with kernel pipeline
      const processResult = await window.electronAPI?.files?.processFile(filePath)
      
      if (processResult && processResult.success) {
        const artifactsPath = this.getExpectedArtifactsPath(filePath, vaultPath)
        const sessionPath = this.getExpectedSessionPath(filePath, vaultPath)
        
        return {
          success: true,
          data: {
            text: processResult.content?.text || '',
            metadata: processResult.content?.metadata || {},
            filePath,
            vaultPath,
            processed_at: new Date().toISOString()
          },
          artifactsPath,
          sessionPath,
          source: 'processed',
          processingTime: 0 // Will be set by caller
        }
      } else {
        return {
          success: false,
          source: 'error',
          processingTime: 0,
          error: processResult?.error || 'Kernel pipeline processing failed'
        }
      }
    } catch (error: any) {
      console.error('[INTELLIGENCE-FLOW] 💥 Kernel pipeline error:', error)
      return {
        success: false,
        source: 'error',
        processingTime: 0,
        error: error.message || 'Kernel pipeline error'
      }
    }
  }

  /**
   * Fallback processing when kernel pipeline fails
   */
  private async processFallback(filePath: string, vaultPath: string): Promise<IntelligenceFlowResult> {
    try {
      console.log('[INTELLIGENCE-FLOW] 🔄 Using fallback processing for:', filePath)

      // Try to read file content directly
      const fileContent = await this.readFileContent(filePath)
      
      if (fileContent) {
        const artifactsPath = this.getExpectedArtifactsPath(filePath, vaultPath)
        const sessionPath = this.getExpectedSessionPath(filePath, vaultPath)
        
        return {
          success: true,
          data: {
            text: fileContent,
            metadata: {
              processing_method: 'fallback',
              file_size: fileContent.length,
              processed_at: new Date().toISOString()
            },
            filePath,
            vaultPath
          },
          artifactsPath,
          sessionPath,
          source: 'processed',
          processingTime: 0
        }
      } else {
        return {
          success: false,
          source: 'error',
          processingTime: 0,
          error: 'Could not read file content'
        }
      }
    } catch (error: any) {
      console.error('[INTELLIGENCE-FLOW] 💥 Fallback processing error:', error)
      return {
        success: false,
        source: 'error',
        processingTime: 0,
        error: error.message || 'Fallback processing error'
      }
    }
  }

  /**
   * Read file content using available APIs
   */
  private async readFileContent(filePath: string): Promise<string | null> {
    try {
      // Try electron API first
      if (window.electronAPI?.files?.readFile) {
        const result = await window.electronAPI.files.readFile(filePath)
        if (result && result.success) {
          return result.content || ''
        }
      }

      // Fallback to fetch for web context (if applicable)
      // This would only work for accessible files
      return null
    } catch (error) {
      console.warn('[INTELLIGENCE-FLOW] Could not read file content:', error)
      return null
    }
  }

  /**
   * Get expected artifacts path following PathResolver pattern
   */
  private getExpectedArtifactsPath(filePath: string, vaultPath: string): string {
    const fileHash = this.computeStableHash(filePath)
    return joinLike(vaultPath, '.intelligence', 'documents', fileHash, 'artifacts', 'artifacts.json')
  }

  /**
   * Get expected session path following PathResolver pattern
   */
  private getExpectedSessionPath(filePath: string, vaultPath: string): string {
    const fileHash = this.computeStableHash(filePath)
    return joinLike(vaultPath, '.intelligence', 'documents', fileHash, 'sessions')
  }

  /**
   * Compute stable hash matching PathResolver.computeStableHash()
   */
  private computeStableHash(filePath: string): string {
    // Normalize path separators for consistent hashing
    const normalizedPath = filePath.replace(/\\/g, '/')
    
    let hash = 0
    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16)
  }

  /**
   * Cache management
   */
  private getCachedResult(filePath: string, vaultPath: string): IntelligenceFlowResult | null {
    const cacheKey = `${filePath}:${vaultPath}`
    const cached = this.flowCache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result
    }
    
    return null
  }

  private cacheResult(filePath: string, vaultPath: string, result: IntelligenceFlowResult): void {
    const cacheKey = `${filePath}:${vaultPath}`
    this.flowCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    })
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.flowCache.clear()
    console.log('[INTELLIGENCE-FLOW] 🗑️ Cache cleared')
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.flowCache.size,
      keys: Array.from(this.flowCache.keys())
    }
  }

  /**
   * Validate intelligence data flow configuration
   */
  validateConfig(config: IntelligenceDataFlowConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.filePath) {
      errors.push('filePath is required')
    }
    
    if (!config.vaultPath) {
      errors.push('vaultPath is required')
    }
    
    // Additional validation can be added here
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// Export singleton instance
export const intelligenceDataFlowService = new IntelligenceDataFlowService()
