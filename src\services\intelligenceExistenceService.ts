/**
 * Intelligence Existence Service
 * 
 * Checks for existing intelligence data before processing files.
 * Follows kernel pipeline architecture from pre-draft-plugin-design.md
 */

import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
import { extractContextPath } from '../utils/vaultPath'
import { intelligenceCacheManager } from './intelligenceCacheManager'

export interface IntelligenceCheckResult {
  exists: boolean
  data?: any
  artifactsPath?: string
  sessionPath?: string
  error?: string
}

export interface FileIntelligenceInfo {
  filePath: string
  vaultPath: string
  contentHash?: string
  fileSize?: number
  lastModified?: string
}

class IntelligenceExistenceService {
  // Use centralized cache manager instead of local cache
  // private cache = new Map<string, IntelligenceCheckResult>()
  // private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  /**
   * Check if intelligence data exists for a file
   * Following kernel pipeline: /intelligence/documents/<hash>/artifacts.json
   */
  async checkIntelligenceExists(fileInfo: FileIntelligenceInfo): Promise<IntelligenceCheckResult> {
    try {
      const { filePath, vaultPath } = fileInfo
      
      // Create cache key
      const cacheKey = `${filePath}:${vaultPath}`
      
      // Check centralized cache first
      const cached = intelligenceCacheManager.get<IntelligenceCheckResult>(cacheKey)
      if (cached) {
        console.log('[INTELLIGENCE-CHECK] 🔥 Cache hit for:', filePath)
        return cached
      }

      console.log('[INTELLIGENCE-CHECK] 🔍 Checking intelligence for:', filePath)

      // Use intelligence client to check for existing data
      const result = await intelligenceClient.read(filePath, vaultPath)
      
      if (result && result.success !== false && result.data) {
        const checkResult: IntelligenceCheckResult = {
          exists: true,
          data: result.data,
          artifactsPath: this.getArtifactsPath(filePath, vaultPath),
          sessionPath: this.getSessionPath(filePath, vaultPath)
        }
        
        // Cache the result using centralized cache manager
        intelligenceCacheManager.set(cacheKey, checkResult, 10 * 60 * 1000) // 10 minutes TTL
        
        console.log('[INTELLIGENCE-CHECK] ✅ Intelligence exists for:', filePath)
        return checkResult
      } else {
        const checkResult: IntelligenceCheckResult = {
          exists: false,
          error: result?.error || 'No intelligence data found'
        }
        
        // Cache negative result for shorter time
        intelligenceCacheManager.set(cacheKey, checkResult, 2 * 60 * 1000) // 2 minutes TTL for negative results
        
        console.log('[INTELLIGENCE-CHECK] ❌ No intelligence found for:', filePath)
        return checkResult
      }
    } catch (error: any) {
      console.error('[INTELLIGENCE-CHECK] 💥 Error checking intelligence:', error)
      return {
        exists: false,
        error: error.message || 'Failed to check intelligence'
      }
    }
  }

  /**
   * Check multiple files for intelligence data
   */
  async checkMultipleFiles(fileInfos: FileIntelligenceInfo[]): Promise<Map<string, IntelligenceCheckResult>> {
    const results = new Map<string, IntelligenceCheckResult>()
    
    // Process files in parallel but limit concurrency
    const batchSize = 5
    for (let i = 0; i < fileInfos.length; i += batchSize) {
      const batch = fileInfos.slice(i, i + batchSize)
      const batchPromises = batch.map(async (fileInfo) => {
        const result = await this.checkIntelligenceExists(fileInfo)
        return { fileInfo, result }
      })
      
      const batchResults = await Promise.all(batchPromises)
      batchResults.forEach(({ fileInfo, result }) => {
        results.set(fileInfo.filePath, result)
      })
    }
    
    return results
  }

  /**
   * Get the expected artifacts path for a file using PathResolver
   * This ensures consistency with the kernel pipeline
   */
  private getArtifactsPath(filePath: string, vaultPath: string): string {
    // Use PathResolver to get the correct artifacts path
    // This follows the kernel pattern: <vault>/.intelligence/documents/<hash>/artifacts/artifacts.json
    try {
      // We need to call the electron API to get the correct path
      // For now, construct it manually following the same pattern as PathResolver
      const fileHash = this.generateFileHash(filePath)
      return `${vaultPath}/.intelligence/documents/${fileHash}/artifacts/artifacts.json`
    } catch (error) {
      console.warn('[INTELLIGENCE-CHECK] Error getting artifacts path:', error)
      // Fallback to manual construction
      const fileHash = this.generateFileHash(filePath)
      return `${vaultPath}/.intelligence/documents/${fileHash}/artifacts/artifacts.json`
    }
  }

  /**
   * Get the expected session path for a file using PathResolver
   */
  private getSessionPath(filePath: string, vaultPath: string): string {
    // Use PathResolver pattern: <vault>/.intelligence/documents/<hash>/sessions/
    const fileHash = this.generateFileHash(filePath)
    return `${vaultPath}/.intelligence/documents/${fileHash}/sessions/`
  }

  /**
   * Generate a stable hash for the file path
   * This should match PathResolver.computeStableHash() behavior
   */
  private generateFileHash(filePath: string): string {
    // Use the same hashing approach as PathResolver.computeStableHash()
    // For now, use a simple hash that should be consistent
    let hash = 0
    const normalizedPath = filePath.replace(/\\/g, '/') // Normalize path separators

    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16)
  }

  /**
   * Clear cache for a specific file
   */
  clearCache(filePath: string, vaultPath: string): void {
    const cacheKey = `${filePath}:${vaultPath}`
    intelligenceCacheManager.delete(cacheKey)
    console.log('[INTELLIGENCE-CHECK] 🗑️ Cleared cache for:', filePath)
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    intelligenceCacheManager.clear()
    console.log('[INTELLIGENCE-CHECK] 🗑️ Cleared all intelligence cache')
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    const stats = intelligenceCacheManager.getStats()
    return {
      size: stats.totalEntries,
      keys: intelligenceCacheManager.keys().filter(key => key.includes('intelligence-check'))
    }
  }

  /**
   * Check if a file needs processing based on intelligence existence and file modification
   */
  async needsProcessing(fileInfo: FileIntelligenceInfo): Promise<boolean> {
    const checkResult = await this.checkIntelligenceExists(fileInfo)
    
    if (!checkResult.exists) {
      return true // No intelligence data, needs processing
    }

    // If we have intelligence data, check if file was modified after intelligence creation
    if (fileInfo.lastModified && checkResult.data?.updated_at) {
      const fileModified = new Date(fileInfo.lastModified).getTime()
      const intelligenceUpdated = new Date(checkResult.data.updated_at).getTime()
      
      if (fileModified > intelligenceUpdated) {
        console.log('[INTELLIGENCE-CHECK] 📅 File modified after intelligence, needs reprocessing')
        return true
      }
    }

    console.log('[INTELLIGENCE-CHECK] ✅ Intelligence is up-to-date, no processing needed')
    return false
  }
}

// Export singleton instance
export const intelligenceExistenceService = new IntelligenceExistenceService()
