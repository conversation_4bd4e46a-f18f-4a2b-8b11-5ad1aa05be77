/**
 * Master Document Service - V03 Compliant
 * Generates and manages master.md as JSON composition with tabbed interface
 * Consolidates all sources for local reasoning as per V03 design
 */

import { MasterDocument, MasterTab, MasterGenerationOptions } from '../types/masterTypes'
import { generateUniqueId, generateContentId } from '../utils/idGenerator'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'

export class MasterDocumentService {
  
  /**
   * Generate master.md document for a vault context
   * V03 COMPLIANT: Consolidates files, chats, artifacts, labels/notes, key ideas, open questions
   */
  async generateMasterDocument(
    vaultPath: string, 
    contextPath: string, 
    options: MasterGenerationOptions = {
      includeRecentActivity: true,
      includeTimeline: true,
      includeCustomTabs: false,
      maxItems: 50
    }
  ): Promise<MasterDocument> {
    
    const masterDoc: MasterDocument = {
      id: generateContentId(`${vaultPath}/${contextPath}`, 'master'),
      vaultPath,
      contextPath,
      tabs: [],
      metadata: {
        created: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        version: '3.0',
        generatedBy: 'system'
      },
      consolidatedData: {
        files: [],
        chats: [],
        artifacts: [],
        labels: [],
        keyIdeas: [],
        openQuestions: []
      }
    }

    // Consolidate data from all sources
    await this.consolidateIntelligenceData(masterDoc, options)

    // Generate tabs based on options
    if (options.includeRecentActivity) {
      masterDoc.tabs.push(await this.generateRecentActivityTab(masterDoc))
    }

    // Always include blank canvas tab for sidecar results
    masterDoc.tabs.push(this.generateCanvasTab())

    if (options.includeTimeline) {
      masterDoc.tabs.push(await this.generateTimelineTab(masterDoc, options))
    }

    // Set first tab as active
    if (masterDoc.tabs.length > 0) {
      masterDoc.tabs[0].isActive = true
    }

    return masterDoc
  }

  /**
   * Consolidate intelligence data from .intelligence and DB records
   */
  private async consolidateIntelligenceData(
    masterDoc: MasterDocument, 
    options: MasterGenerationOptions
  ): Promise<void> {
    try {
      // Get vault registry to scan for intelligence files
      const vaultRegistryResult = await window.electronAPI.vault.getVaultRegistry()
      if (!vaultRegistryResult || !vaultRegistryResult.success) return

      const vaultRegistry = vaultRegistryResult.data || vaultRegistryResult

      // Scan intelligence directory for files
      const intelligenceDir = `${masterDoc.vaultPath}/${masterDoc.contextPath}/.intelligence`
      const intelligenceFiles = await window.electronAPI.vault.readDirectory(intelligenceDir)
      
      if (intelligenceFiles.success && intelligenceFiles.items) {
        for (const item of intelligenceFiles.items.slice(0, options.maxItems)) {
          if (item.name.endsWith('.json')) {
            try {
              const intelligence = await intelligenceClient.read(item.path, masterDoc.vaultPath)
              if (intelligence.success && intelligence.data) {
                masterDoc.consolidatedData.files.push({
                  path: item.path,
                  intelligence: intelligence.data
                })

                // Extract key ideas
                if (intelligence.data.key_ideas) {
                  masterDoc.consolidatedData.keyIdeas.push(
                    ...intelligence.data.key_ideas.map((idea: any) => ({
                      id: idea.id,
                      text: idea.text,
                      relevance: idea.relevance_score,
                      sources: [item.path]
                    }))
                  )
                }
              }
            } catch (error) {
              console.warn(`Failed to load intelligence for ${item.path}:`, error)
            }
          }
        }
      }

      // TODO: Consolidate chat data from DB
      // TODO: Consolidate artifacts data
      // TODO: Generate open questions from consolidated data

    } catch (error) {
      console.error('Failed to consolidate intelligence data:', error)
    }
  }

  /**
   * Generate Recent Activity Summary tab
   */
  private async generateRecentActivityTab(masterDoc: MasterDocument): Promise<MasterTab> {
    const recentFiles = masterDoc.consolidatedData.files
      .slice(0, 10)
      .map(file => ({
        id: generateUniqueId('file'),
        name: file.path.split('/').pop() || 'Unknown',
        path: file.path,
        lastModified: new Date().toISOString(), // TODO: Get actual modification time
        intelligence: {
          keyIdeas: file.intelligence?.key_ideas?.length || 0,
          confidence: file.intelligence?.processing_confidence || 0
        }
      }))

    return {
      id: generateUniqueId('tab'),
      title: 'Recent Activity',
      type: 'activity',
      isActive: true,
      content: {
        recentActivity: {
          summary: `Context contains ${masterDoc.consolidatedData.files.length} processed files with ${masterDoc.consolidatedData.keyIdeas.length} key ideas extracted.`,
          lastUpdated: new Date().toISOString(),
          keyMetrics: {
            filesProcessed: masterDoc.consolidatedData.files.length,
            chatsCreated: masterDoc.consolidatedData.chats.length,
            ideasGenerated: masterDoc.consolidatedData.keyIdeas.length
          },
          recentFiles,
          recentChats: masterDoc.consolidatedData.chats.slice(0, 5)
        }
      }
    }
  }

  /**
   * Generate blank canvas tab for sidecar prompting results
   */
  private generateCanvasTab(): MasterTab {
    return {
      id: generateUniqueId('tab'),
      title: 'AI Canvas',
      type: 'canvas',
      content: {
        canvas: {
          promptResults: [],
          customContent: 'This is your AI canvas. Results from sidecar prompting will appear here.'
        }
      }
    }
  }

  /**
   * Generate linear timeline tab
   */
  private async generateTimelineTab(
    masterDoc: MasterDocument, 
    options: MasterGenerationOptions
  ): Promise<MasterTab> {
    const timelineItems = [
      ...masterDoc.consolidatedData.files.map(file => ({
        id: generateUniqueId('timeline'),
        type: 'document' as const,
        title: file.path.split('/').pop() || 'Unknown Document',
        timestamp: new Date().toISOString(), // TODO: Get actual timestamp
        path: file.path,
        preview: file.intelligence?.summary || 'No preview available',
        tags: file.intelligence?.key_ideas?.slice(0, 3).map((idea: any) => idea.text) || []
      })),
      ...masterDoc.consolidatedData.chats.map(chat => ({
        id: generateUniqueId('timeline'),
        type: 'chat' as const,
        title: chat.title,
        timestamp: new Date().toISOString(), // TODO: Get actual timestamp
        preview: chat.summary,
        tags: []
      }))
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    return {
      id: generateUniqueId('tab'),
      title: 'Timeline',
      type: 'timeline',
      content: {
        timeline: {
          items: timelineItems.slice(0, options.maxItems),
          filters: {
            type: ['document', 'note', 'chat']
          }
        }
      }
    }
  }

  /**
   * Update master document incrementally
   */
  async updateMasterDocument(
    masterDoc: MasterDocument, 
    updates: Partial<MasterDocument>
  ): Promise<MasterDocument> {
    const updatedDoc = {
      ...masterDoc,
      ...updates,
      metadata: {
        ...masterDoc.metadata,
        lastUpdated: new Date().toISOString()
      }
    }

    // Persist to vault
    await this.persistMasterDocument(updatedDoc)
    
    return updatedDoc
  }

  /**
   * Persist master document to vault
   */
  private async persistMasterDocument(masterDoc: MasterDocument): Promise<void> {
    try {
      const masterPath = `${masterDoc.vaultPath}/${masterDoc.contextPath}/master.json`
      await window.electronAPI.vault.writeFile(masterPath, JSON.stringify(masterDoc, null, 2))
    } catch (error) {
      console.error('Failed to persist master document:', error)
    }
  }

  /**
   * Load existing master document
   */
  async loadMasterDocument(vaultPath: string, contextPath: string): Promise<MasterDocument | null> {
    try {
      const masterPath = `${vaultPath}/${contextPath}/master.json`
      const result = await window.electronAPI.vault.readFile(masterPath)
      
      if (result.success && result.content) {
        return JSON.parse(result.content)
      }
    } catch (error) {
      console.warn('Failed to load master document:', error)
    }
    
    return null
  }
}

export const masterDocumentService = new MasterDocumentService()
