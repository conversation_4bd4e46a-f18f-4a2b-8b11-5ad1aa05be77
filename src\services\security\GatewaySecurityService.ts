/**
 * Gateway Security Service for ChatLo Security Framework V2
 * Analyzes content before external LLM transmission
 */

import { SecurityLevel, SecurityAnalysis, SecurityDecision, DetectedPattern } from '../../types/securityTypes'
import { PatternDetector } from './PatternDetector'
import { PrivateModeController } from './PrivateModeController'

export class GatewaySecurityService {
  private static instance: GatewaySecurityService
  private patternDetector: PatternDetector
  private privateModeController: PrivateModeController
  private securityLevel: SecurityLevel = 'balanced'
  private customVaultPatterns: string[] = []

  private constructor() {
    this.patternDetector = new PatternDetector()
    this.privateModeController = PrivateModeController.getInstance()
    this.loadSettings()
  }

  static getInstance(): GatewaySecurityService {
    if (!GatewaySecurityService.instance) {
      GatewaySecurityService.instance = new GatewaySecurityService()
    }
    return GatewaySecurityService.instance
  }

  /**
   * Analyze content before transmission to external LLM
   */
  async analyzeBeforeTransmission(
    content: string,
    files: File[],
    targetModel: string
  ): Promise<SecurityDecision> {

    // Skip analysis for local models
    if (this.isLocalModel(targetModel)) {
      return {
        allowed: true,
        blocked: false,
        requiresPermission: false,
        warnings: [],
        detectedPatterns: []
      }
    }

    // Check private mode first
    if (this.privateModeController.isPrivateMode()) {
      return {
        allowed: false,
        blocked: true,
        requiresPermission: false,
        warnings: ['External models are blocked in Private Mode'],
        detectedPatterns: []
      }
    }

    // Analyze content for sensitive patterns
    const analysis = await this.analyzeContent(content, files)

    // Apply security level logic
    return this.makeSecurityDecision(analysis, targetModel)
  }

  /**
   * Analyze content for sensitive patterns
   */
  private async analyzeContent(content: string, files: File[]): Promise<SecurityAnalysis> {
    // Detect sensitive patterns in text content
    const detectedPatterns = this.patternDetector.detectSensitivePatterns(content)

    // Filter patterns based on custom vault exceptions
    const filteredPatterns = this.applyCustomVaultExceptions(detectedPatterns)

    // Calculate risk level
    const riskLevel = this.calculateRiskLevel(filteredPatterns, files)

    return {
      hasFiles: files.length > 0,
      fileTypes: files.map(f => f.type),
      sensitivePatterns: filteredPatterns,
      riskLevel,
      customExceptions: this.customVaultPatterns
    }
  }

  /**
   * Apply custom vault pattern exceptions
   */
  private applyCustomVaultExceptions(patterns: DetectedPattern[]): DetectedPattern[] {
    if (this.customVaultPatterns.length === 0) {
      return patterns
    }

    return patterns.filter(pattern => {
      // Check if any custom pattern allows this content
      const isAllowed = this.customVaultPatterns.some(customPattern => {
        try {
          const regex = new RegExp(customPattern, 'gi')
          return pattern.matches.some(match => regex.test(match))
        } catch (error) {
          console.warn('[Security] Invalid custom pattern:', customPattern)
          return false
        }
      })

      return !isAllowed
    })
  }

  /**
   * Calculate overall risk level
   */
  private calculateRiskLevel(patterns: DetectedPattern[], files: File[]): 'low' | 'medium' | 'high' {
    const hasHighSeverityPatterns = patterns.some(p => p.severity === 'high')
    const hasMediumSeverityPatterns = patterns.some(p => p.severity === 'medium')
    const hasFiles = files.length > 0

    if (hasHighSeverityPatterns || (hasMediumSeverityPatterns && hasFiles)) {
      return 'high'
    }

    if (hasMediumSeverityPatterns || hasFiles) {
      return 'medium'
    }

    return 'low'
  }

  /**
   * Make security decision based on analysis and security level
   */
  private makeSecurityDecision(analysis: SecurityAnalysis, _targetModel: string): SecurityDecision {
    const hasPatterns = analysis.sensitivePatterns.length > 0

    switch (this.securityLevel) {
      case 'strict':
        if (hasPatterns || analysis.hasFiles) {
          return {
            allowed: false,
            blocked: true,
            requiresPermission: false,
            warnings: ['Sensitive content blocked in Strict mode'],
            detectedPatterns: analysis.sensitivePatterns
          }
        }
        break

      case 'balanced':
        if (hasPatterns || analysis.hasFiles) {
          return {
            allowed: false,
            blocked: false,
            requiresPermission: true,
            warnings: ['Sensitive content detected - permission required'],
            detectedPatterns: analysis.sensitivePatterns
          }
        }
        break

      case 'disabled':
        // Just log and allow
        if (hasPatterns || analysis.hasFiles) {
          return {
            allowed: true,
            blocked: false,
            requiresPermission: false,
            warnings: ['Content sharing reminder - security disabled'],
            detectedPatterns: analysis.sensitivePatterns
          }
        }
        break
    }

    // Default: allow transmission
    return {
      allowed: true,
      blocked: false,
      requiresPermission: false,
      warnings: [],
      detectedPatterns: []
    }
  }

  /**
   * Check if model is local
   */
  private isLocalModel(modelId: string): boolean {
    return modelId.startsWith('ollama:') || modelId.startsWith('lmstudio:')
  }

  /**
   * Update security settings
   */
  updateSettings(securityLevel: SecurityLevel, customVaultPatterns: string): void {
    this.securityLevel = securityLevel
    this.customVaultPatterns = customVaultPatterns
      .split('\n')
      .map(p => p.trim())
      .filter(p => p.length > 0)

    console.log(`[Security] Settings updated: ${securityLevel}, ${this.customVaultPatterns.length} custom patterns`)
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      if (window.electronAPI?.settings) {
        const securityLevel = await window.electronAPI.settings.get('securityLevel') as SecurityLevel
        const customPatterns = await window.electronAPI.settings.get('allowedVaultPatterns') as string

        if (securityLevel) {
          this.securityLevel = securityLevel
        }

        if (customPatterns) {
          this.customVaultPatterns = customPatterns
            .split('\n')
            .map(p => p.trim())
            .filter(p => p.length > 0)
        }
      }
    } catch (error) {
      console.warn('[Security] Failed to load settings:', error)
    }
  }

  /**
   * Get current security status
   */
  getStatus(): { level: SecurityLevel; patternsCount: number; privateMode: boolean } {
    return {
      level: this.securityLevel,
      patternsCount: this.customVaultPatterns.length,
      privateMode: this.privateModeController.isPrivateMode()
    }
  }
}
