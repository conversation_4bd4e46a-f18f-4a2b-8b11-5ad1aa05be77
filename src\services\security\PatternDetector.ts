/**
 * Smart Pattern Detector for ChatLo Security Framework V2
 * Context-aware detection that excludes common footers and disclaimers
 */

import { DetectedPattern, SecurityPatterns } from '../../types/securityTypes'

interface ExcludedArea {
  start: number
  end: number
  type: string
}

export class PatternDetector {
  private patterns: SecurityPatterns

  constructor() {
    this.patterns = this.getDefaultPatterns()
  }

  private getDefaultPatterns(): SecurityPatterns {
    return {
      sensitivePatterns: {
        // Personal Identifiable Information (PII)
        email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
        phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
        ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
        creditCard: /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,

        // Legal & Contract Headers (Highly Sensitive)
        contractTitle: /\b(?:CONFIDENTIAL|PROPRIETARY|NON-DISCLOSURE|AGREEMENT|CONTRACT)\s+(?:AGREEMENT|CONTRACT|DOCUMENT)/gi,
        legalHeader: /\b(?:ATTORNEY-CLIENT|PRIVILEGED|WORK PRODUCT|CONFIDENTIAL COMMUNICATION)/gi,

        // Business Sensitive (Smart - excludes footers)
        proprietary: /\b(?:proprietary|confidential|internal|restricted)\b(?!\s*(?:footer|disclaimer|notice))/gi,
        patent: /\b(?:patent|Patent)\s*(?:no\.?|number|#)\s*[\d,\-\/]+/gi,
        nda: /\b(?:NDA|non-disclosure|confidentiality agreement)\b/gi,

        // Financial Information
        bankAccount: /\b\d{8,17}\b/g,
        routingNumber: /\b\d{9}\b/g,

        // Personal Information
        address: /\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)\b/gi,
        dob: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g
      },

      excludedPatterns: {
        // Common footers and disclaimers to exclude
        commonFooter: /\b(?:this\s+(?:email|message|communication)\s+is\s+confidential|confidential\s+and\s+proprietary|for\s+internal\s+use\s+only)/gi,
        legalDisclaimer: /\b(?:disclaimer|notice|warning):\s*(?:confidential|proprietary)/gi,
        copyrightFooter: /\b(?:copyright|©)\s*\d{4}.*(?:all\s+rights\s+reserved|company\s+name)/gi,
        emailSignature: /\b(?:confidential|proprietary).*(?:signature|footer|disclaimer)/gi
      }
    }
  }

  /**
   * Detect sensitive patterns in content with smart exclusion of footers
   */
  detectSensitivePatterns(content: string): DetectedPattern[] {
    const patterns: DetectedPattern[] = []

    // First, find excluded areas (footers, disclaimers)
    const excludedAreas = this.findExcludedAreas(content)

    // Then detect sensitive patterns, excluding footer areas
    for (const [type, regex] of Object.entries(this.patterns.sensitivePatterns)) {
      const matches = Array.from(content.matchAll(regex))

      if (matches.length > 0) {
        // Filter out matches that are in excluded areas
        const validMatches = matches.filter(match =>
          !this.isInExcludedArea(match.index || 0, excludedAreas)
        )

        if (validMatches.length > 0) {
          patterns.push({
            type,
            matches: validMatches.map(m => m[0]),
            maskedValues: validMatches.map(m => this.maskSensitiveValue(m[0], type)),
            severity: this.getPatternSeverity(type)
          })
        }
      }
    }

    return patterns
  }

  /**
   * Find areas in content that should be excluded from sensitive detection
   */
  private findExcludedAreas(content: string): ExcludedArea[] {
    const excludedAreas: ExcludedArea[] = []

    for (const [type, regex] of Object.entries(this.patterns.excludedPatterns)) {
      const matches = Array.from(content.matchAll(regex))

      for (const match of matches) {
        if (match.index !== undefined) {
          excludedAreas.push({
            start: match.index,
            end: match.index + match[0].length,
            type
          })
        }
      }
    }

    return excludedAreas
  }

  /**
   * Check if a match position is within an excluded area
   */
  private isInExcludedArea(matchIndex: number, excludedAreas: ExcludedArea[]): boolean {
    return excludedAreas.some(area =>
      matchIndex >= area.start && matchIndex <= area.end
    )
  }

  /**
   * Mask sensitive values for display
   */
  private maskSensitiveValue(value: string, type: string): string {
    switch (type) {
      case 'email':
        return value.replace(/(.{1,3}).*@(.*)/, '$1***@$2')
      case 'phone':
        return value.replace(/(\d{3}).*(\d{4})/, '$1-***-$2')
      case 'ssn':
        return value.replace(/(\d{3})-.*-(\d{4})/, '$1-**-$2')
      case 'creditCard':
        return value.replace(/(\d{4}).*(\d{4})/, '$1-****-****-$2')
      case 'contractTitle':
      case 'legalHeader':
      case 'proprietary':
      case 'patent':
      case 'nda':
        return value.substring(0, Math.min(10, value.length)) + '***'
      default:
        return value.substring(0, 3) + '***'
    }
  }

  /**
   * Get severity level for pattern type
   */
  private getPatternSeverity(type: string): 'low' | 'medium' | 'high' {
    const highSeverity = ['ssn', 'creditCard', 'contractTitle', 'legalHeader', 'bankAccount']
    const mediumSeverity = ['email', 'phone', 'proprietary', 'patent', 'nda']

    if (highSeverity.includes(type)) return 'high'
    if (mediumSeverity.includes(type)) return 'medium'
    return 'low'
  }

  /**
   * Update patterns (for plugin overrides)
   */
  updatePatterns(newPatterns: SecurityPatterns): void {
    this.patterns = {
      sensitivePatterns: { ...this.patterns.sensitivePatterns, ...newPatterns.sensitivePatterns },
      excludedPatterns: { ...this.patterns.excludedPatterns, ...newPatterns.excludedPatterns }
    }
  }

  /**
   * Get current patterns
   */
  getPatterns(): SecurityPatterns {
    return { ...this.patterns }
  }
}
