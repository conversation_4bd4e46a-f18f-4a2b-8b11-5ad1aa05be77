/**
 * Private Mode Controller for ChatLo Security Framework V2
 * Manages local vs external model access control
 */

import { OpenRouterModel } from '../../types'
import { LocalModel } from '../localModelService'

export class PrivateModeController {
  private static instance: PrivateModeController
  private isPrivate: boolean = false
  private listeners: Set<(isPrivate: boolean) => void> = new Set()

  private constructor() {
    this.loadState()
  }

  static getInstance(): PrivateModeController {
    if (!PrivateModeController.instance) {
      PrivateModeController.instance = new PrivateModeController()
    }
    return PrivateModeController.instance
  }

  /**
   * Set private mode state
   */
  setPrivateMode(enabled: boolean): void {
    if (this.isPrivate !== enabled) {
      this.isPrivate = enabled
      this.persistState()
      this.notifyListeners()

      console.log(`[PrivateMode] ${enabled ? 'Enabled' : 'Disabled'} - ${enabled ? 'Local models only' : 'All models available'}`)
    }
  }

  /**
   * Get current private mode state
   */
  isPrivateMode(): boolean {
    return this.isPrivate
  }

  /**
   * Check if external models are allowed
   */
  isExternalModelAllowed(): boolean {
    return !this.isPrivate
  }

  /**
   * Filter available models based on private mode
   */
  filterModelsByPrivacy(allModels: OpenRouterModel[], localModels: LocalModel[]): OpenRouterModel[] {
    if (this.isPrivate) {
      // In private mode, only return local models formatted as OpenRouterModel
      return localModels.map(localModel => ({
        id: `${localModel.provider}:${localModel.name}`,
        name: localModel.name,
        description: `Local model via ${localModel.provider}`,
        context_length: 4096, // Default context length for local models
        pricing: { prompt: '0', completion: '0' },
        top_provider: {
          context_length: 4096, // Default context length
          max_completion_tokens: 2048 // Default max completion tokens
        }
      }))
    }

    // In non-private mode, return all models (external + local)
    const localAsOpenRouter = localModels.map(localModel => ({
      id: `${localModel.provider}:${localModel.name}`,
      name: localModel.name,
      description: `Local model via ${localModel.provider}`,
      context_length: 4096, // Default context length for local models
      pricing: { prompt: '0', completion: '0' },
      top_provider: {
        context_length: 4096, // Default context length
        max_completion_tokens: 2048 // Default max completion tokens
      }
    }))

    return [...allModels, ...localAsOpenRouter]
  }

  /**
   * Check if a specific model is allowed in current mode
   */
  isModelAllowed(modelId: string): boolean {
    const isLocalModel = modelId.startsWith('ollama:') || modelId.startsWith('lmstudio:')

    if (this.isPrivate) {
      // In private mode, only local models are allowed
      return isLocalModel
    }

    // In non-private mode, all models are allowed
    return true
  }

  /**
   * Get model type for security logging
   */
  getModelType(modelId: string): 'local' | 'external' {
    return (modelId.startsWith('ollama:') || modelId.startsWith('lmstudio:')) ? 'local' : 'external'
  }

  /**
   * Add listener for private mode changes
   */
  addListener(listener: (isPrivate: boolean) => void): void {
    this.listeners.add(listener)
  }

  /**
   * Remove listener for private mode changes
   */
  removeListener(listener: (isPrivate: boolean) => void): void {
    this.listeners.delete(listener)
  }

  /**
   * Notify all listeners of private mode change
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.isPrivate)
      } catch (error) {
        console.error('[PrivateMode] Error notifying listener:', error)
      }
    })

    // Also emit custom event for components that use event listeners
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('privateModeChanged', {
        detail: { isPrivate: this.isPrivate }
      }))
    }
  }

  /**
   * Load private mode state from storage
   */
  private loadState(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('chatlo_private_mode')
        if (stored !== null) {
          this.isPrivate = JSON.parse(stored)
        }
      }
    } catch (error) {
      console.warn('[PrivateMode] Failed to load state:', error)
      this.isPrivate = false
    }
  }

  /**
   * Persist private mode state to storage
   */
  private persistState(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('chatlo_private_mode', JSON.stringify(this.isPrivate))
      }
    } catch (error) {
      console.error('[PrivateMode] Failed to persist state:', error)
    }
  }

  /**
   * Get private mode status for UI display
   */
  getStatus(): { isPrivate: boolean; description: string; icon: string } {
    return {
      isPrivate: this.isPrivate,
      description: this.isPrivate ? 'Private: Local models only' : 'Connected: Local + External models',
      icon: this.isPrivate ? '🔒' : '🌐'
    }
  }
}
