/**
 * Security Services Index
 * Exports all security-related services for ChatLo Security Framework V2
 */

import { PatternDetector } from './PatternDetector'
import { PrivateModeController } from './PrivateModeController'
import { GatewaySecurityService } from './GatewaySecurityService'

export { PatternDetector } from './PatternDetector'
export { PrivateModeController } from './PrivateModeController'
export { GatewaySecurityService } from './GatewaySecurityService'

// Create singleton instances for easy access (lazy initialization to avoid circular dependencies)
let _patternDetector: PatternDetector | null = null
let _privateModeController: PrivateModeController | null = null
let _gatewaySecurityService: GatewaySecurityService | null = null

export const getPatternDetector = (): PatternDetector => {
  if (!_patternDetector) {
    _patternDetector = new PatternDetector()
  }
  return _patternDetector
}

export const getPrivateModeController = (): PrivateModeController => {
  if (!_privateModeController) {
    _privateModeController = PrivateModeController.getInstance()
  }
  return _privateModeController
}

export const getGatewaySecurityService = (): GatewaySecurityService => {
  if (!_gatewaySecurityService) {
    _gatewaySecurityService = GatewaySecurityService.getInstance()
  }
  return _gatewaySecurityService
}
