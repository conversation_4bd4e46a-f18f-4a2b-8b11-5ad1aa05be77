/**
 * Unified Intelligence Retrieval Service
 * 
 * Implements intelligence-first processing pipeline:
 * 1. Check if intelligence exists
 * 2. Load existing intelligence OR process file and store intelligence
 * 3. Return unified intelligence data for chat context
 */

import { intelligenceExistenceService, FileIntelligenceInfo, IntelligenceCheckResult } from './intelligenceExistenceService'
import { intelligence as intelligenceClient, files as filesClient } from '../api/UnifiedAPIClient'
import { extractContextPath } from '../utils/vaultPath'
import { intelligenceCacheManager } from './intelligenceCacheManager'
import { generateIdeaId } from '../utils/idGenerator'

export interface ProcessedIntelligence {
  filePath: string
  vaultPath: string
  intelligence: any
  extractedContent: string
  metadata: any
  source: 'existing' | 'processed'
  processingTime?: number
}

export interface ProcessingOptions {
  forceReprocess?: boolean
  includeRawContent?: boolean
  maxContentLength?: number
  processingTimeout?: number
}

class UnifiedIntelligenceService {
  private processingQueue = new Map<string, Promise<ProcessedIntelligence>>()

  /**
   * Get intelligence data for a file using intelligence-first approach
   * Prevents duplicate processing by checking queue and cache
   */
  async getIntelligence(
    fileInfo: FileIntelligenceInfo,
    options: ProcessingOptions = {}
  ): Promise<ProcessedIntelligence> {
    const { filePath, vaultPath } = fileInfo
    const cacheKey = `${filePath}:${vaultPath}`

    // Check if already processing this file
    if (this.processingQueue.has(cacheKey)) {
      console.log('[UNIFIED-INTELLIGENCE] ⏳ Already processing, waiting for result:', filePath)
      return await this.processingQueue.get(cacheKey)!
    }

    // Check if we have a recent result in cache (unless forced reprocessing)
    if (!options.forceReprocess) {
      const cached = intelligenceCacheManager.get<ProcessedIntelligence>(`intelligence:${cacheKey}`)
      if (cached) {
        console.log('[UNIFIED-INTELLIGENCE] 🔥 Cache hit, returning cached intelligence:', filePath)
        return cached
      }
    }

    // Start processing
    const processingPromise = this.processIntelligence(fileInfo, options)
    this.processingQueue.set(cacheKey, processingPromise)

    try {
      const result = await processingPromise

      // Cache the result for future use
      intelligenceCacheManager.set(
        `intelligence:${cacheKey}`,
        result,
        30 * 60 * 1000 // 30 minutes TTL
      )

      return result
    } finally {
      // Clean up processing queue
      this.processingQueue.delete(cacheKey)
    }
  }

  /**
   * Internal processing logic
   */
  private async processIntelligence(
    fileInfo: FileIntelligenceInfo,
    options: ProcessingOptions
  ): Promise<ProcessedIntelligence> {
    const startTime = Date.now()
    const { filePath, vaultPath } = fileInfo

    try {
      console.log('[UNIFIED-INTELLIGENCE] 🚀 Starting intelligence processing for:', filePath)

      // Step 1: Check if intelligence exists (unless forced reprocessing)
      if (!options.forceReprocess) {
        const existenceCheck = await intelligenceExistenceService.checkIntelligenceExists(fileInfo)
        
        if (existenceCheck.exists && existenceCheck.data) {
          console.log('[UNIFIED-INTELLIGENCE] ✅ Using existing intelligence for:', filePath)
          
          // Load extracted content from artifacts if available
          const extractedContent = await this.loadExtractedContent(filePath, vaultPath)
          
          return {
            filePath,
            vaultPath,
            intelligence: existenceCheck.data,
            extractedContent: extractedContent || '',
            metadata: existenceCheck.data.analysis_metadata || {},
            source: 'existing',
            processingTime: Date.now() - startTime
          }
        }
      }

      // Step 2: Process file and store intelligence
      console.log('[UNIFIED-INTELLIGENCE] 🔄 Processing file and creating intelligence:', filePath)
      
      const processedResult = await this.processAndStoreIntelligence(fileInfo, options)
      
      return {
        filePath,
        vaultPath,
        intelligence: processedResult.intelligence,
        extractedContent: processedResult.extractedContent,
        metadata: processedResult.metadata,
        source: 'processed',
        processingTime: Date.now() - startTime
      }

    } catch (error: any) {
      console.error('[UNIFIED-INTELLIGENCE] 💥 Error processing intelligence:', error)
      
      // Return minimal intelligence data on error
      return {
        filePath,
        vaultPath,
        intelligence: {
          file_path: filePath,
          key_ideas: [],
          weighted_entities: [],
          human_connections: [],
          processing_confidence: 0,
          analysis_metadata: {
            error: error.message,
            timestamp: new Date().toISOString()
          }
        },
        extractedContent: '',
        metadata: { error: error.message },
        source: 'processed',
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * Process file and store intelligence using FileCoreService pipeline
   */
  private async processAndStoreIntelligence(
    fileInfo: FileIntelligenceInfo,
    options: ProcessingOptions
  ): Promise<{ intelligence: any; extractedContent: string; metadata: any }> {
    const { filePath, vaultPath } = fileInfo

    try {
      // Step 1: Process file using kernel pipeline
      console.log('[UNIFIED-INTELLIGENCE] 📄 Processing file content:', filePath)
      
      const processResult = await window.electronAPI?.files?.processFile(filePath)
      
      if (!processResult || !processResult.success) {
        throw new Error(`File processing failed: ${processResult?.error || 'Unknown error'}`)
      }

      const extractedContent = processResult.content?.text || ''
      const metadata = processResult.content?.metadata || {}

      console.log('[UNIFIED-INTELLIGENCE] ✅ File processed, extracted content length:', extractedContent.length)

      // Step 2: Generate intelligence data if we have content
      let intelligence: any = {
        file_path: filePath,
        key_ideas: [],
        weighted_entities: [],
        human_connections: [],
        processing_confidence: 0.5,
        analysis_metadata: {
          processing_time_ms: 0,
          model_used: 'kernel-pipeline',
          timestamp: new Date().toISOString(),
          content_length: extractedContent.length
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // If we have extracted content, try to generate basic intelligence
      if (extractedContent && extractedContent.length > 0) {
        intelligence = await this.generateBasicIntelligence(extractedContent, filePath, metadata)
      }

      // Step 3: Store intelligence data
      console.log('[UNIFIED-INTELLIGENCE] 💾 Storing intelligence data:', filePath)
      
      const storeResult = await intelligenceClient.write(filePath, vaultPath, {
        json: intelligence
      })

      if (!storeResult || storeResult.success === false) {
        console.warn('[UNIFIED-INTELLIGENCE] ⚠️ Failed to store intelligence, continuing with in-memory data')
      }

      return {
        intelligence,
        extractedContent,
        metadata
      }

    } catch (error: any) {
      console.error('[UNIFIED-INTELLIGENCE] 💥 Error in processAndStoreIntelligence:', error)
      throw error
    }
  }

  /**
   * Load extracted content from artifacts.json
   */
  private async loadExtractedContent(filePath: string, vaultPath: string): Promise<string | null> {
    try {
      // Try to get processed content from file processing API
      const processResult = await window.electronAPI?.files?.processFile(filePath)
      
      if (processResult && processResult.success && processResult.content?.text) {
        return processResult.content.text
      }

      return null
    } catch (error) {
      console.warn('[UNIFIED-INTELLIGENCE] ⚠️ Could not load extracted content:', error)
      return null
    }
  }

  /**
   * Generate basic intelligence from extracted content
   */
  private async generateBasicIntelligence(content: string, filePath: string, metadata: any): Promise<any> {
    // Basic keyword extraction and analysis
    const words = content.toLowerCase().split(/\s+/)
    const wordCount = words.length
    
    // Extract potential key ideas (simple approach)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
    const keyIdeas = sentences.slice(0, 5).map((sentence, index) => ({
      id: generateIdeaId(sentence.trim(), index),
      text: sentence.trim(),
      relevance_score: Math.max(0.3, Math.random() * 0.7),
      category: 'extracted_content'
    }))

    // Extract potential entities (basic approach)
    const capitalizedWords = content.match(/\b[A-Z][a-z]+\b/g) || []
    const uniqueCapitalized = [...new Set(capitalizedWords)]
    const entities = uniqueCapitalized.slice(0, 10).map((word, index) => ({
      text: word,
      type: 'UNKNOWN',
      confidence: Math.max(0.3, Math.random() * 0.7),
      frequency: capitalizedWords.filter(w => w === word).length
    }))

    return {
      file_path: filePath,
      key_ideas: keyIdeas,
      weighted_entities: entities,
      human_connections: [],
      processing_confidence: Math.min(0.8, Math.max(0.3, keyIdeas.length * 0.1)),
      analysis_metadata: {
        processing_time_ms: 100,
        model_used: 'basic-extraction',
        timestamp: new Date().toISOString(),
        content_length: content.length,
        word_count: wordCount,
        sentence_count: sentences.length,
        extraction_method: 'keyword-based'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }

  /**
   * Process multiple files in batch
   */
  async processBatch(fileInfos: FileIntelligenceInfo[], options: ProcessingOptions = {}): Promise<ProcessedIntelligence[]> {
    console.log('[UNIFIED-INTELLIGENCE] 📦 Processing batch of', fileInfos.length, 'files')
    
    const results = await Promise.all(
      fileInfos.map(fileInfo => this.getIntelligence(fileInfo, options))
    )
    
    console.log('[UNIFIED-INTELLIGENCE] ✅ Batch processing complete')
    return results
  }

  /**
   * Clear processing queue (useful for cleanup)
   */
  clearQueue(): void {
    this.processingQueue.clear()
    console.log('[UNIFIED-INTELLIGENCE] 🗑️ Processing queue cleared')
  }
}

// Export singleton instance
export const unifiedIntelligenceService = new UnifiedIntelligenceService()
