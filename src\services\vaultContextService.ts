import { BaseService, ServiceError, ServiceErrorCode } from './base'

export interface VaultContext {
  vaultPath: string
  contextId: string
  contextName: string
  contextPath: string
}

export interface PathResolution {
  type: 'file' | 'annotation' | 'vault-relative'
  vault: string
  context: string
  path: string
  originalPath: string
}

/**
 * VaultContextService - Single source of truth for vault context state
 * Replaces the fragmented isVirtualPath system with unified context management
 */
class VaultContextService extends BaseService {
  private currentContext: VaultContext | null = null
  private contextListeners: ((context: VaultContext | null) => void)[] = []

  constructor() {
    super({
      name: 'VaultContextService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Load current context from URL params or storage
    await this.loadCurrentContext()
  }

  /**
   * Set the current vault context
   */
  async setCurrentContext(context: VaultContext | null): Promise<void> {
    this.currentContext = context
    this.notifyContextListeners()
    
    this.logger.info('Context changed', 'setCurrentContext', { 
      contextId: context?.contextId,
      vaultPath: context?.vaultPath 
    })
  }

  /**
   * Get the current vault context
   */
  getCurrentContext(): VaultContext | null {
    return this.currentContext
  }

  /**
   * Subscribe to context changes
   */
  onContextChange(listener: (context: VaultContext | null) => void): () => void {
    this.contextListeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.contextListeners.indexOf(listener)
      if (index > -1) {
        this.contextListeners.splice(index, 1)
      }
    }
  }

  /**
   * Resolve any path type to a standardized PathResolution
   * This replaces all isVirtualPath logic
   */
  async resolvePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    try {
      // Determine path type
      const isAnnotationPath = inputPath.startsWith('chat-notes/')
      const isAbsolutePath = /^[A-Z]:\\/.test(inputPath) || inputPath.startsWith('/')
      
      if (isAnnotationPath) {
        return await this.resolveAnnotationPath(inputPath, options)
      } else if (isAbsolutePath) {
        return await this.resolveFilePath(inputPath, options)
      } else {
        return await this.resolveVaultRelativePath(inputPath, options)
      }
    } catch (error) {
      throw new ServiceError(
        ServiceErrorCode.VALIDATION_ERROR,
        `Failed to resolve path: ${inputPath}`,
        { serviceName: this.serviceName, operation: 'resolvePath', details: { inputPath, error } }
      )
    }
  }

  /**
   * Resolve annotation paths (chat-notes/...)
   */
  private async resolveAnnotationPath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    const context = await this.getContextForResolution(options)
    
    // Convert chat-notes/file.json → .intelligence/context-notes/file.json
    const fileName = inputPath.replace('chat-notes/', '')
    const resolvedPath = `${context.contextPath}/.intelligence/context-notes/${fileName}`
    
    return {
      type: 'annotation',
      vault: context.vaultPath,
      context: context.contextId,
      path: resolvedPath,
      originalPath: inputPath
    }
  }

  /**
   * Resolve absolute file paths using vault registry lookup
   * FIXED: Avoid PathResolver.inferVaultPath security rejection
   */
  private async resolveFilePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    try {
      // Get vault registry for direct lookup instead of PathResolver.inferVaultPath
      const registry = await window.electronAPI?.vault?.getVaultRegistry()

      if (!registry?.vaults) {
        throw new Error('No vault registry available')
      }

      // Find vault by direct path matching (avoid security rejection)
      let bestMatch: any = null
      let bestMatchLength = 0

      const normalizedInputPath = inputPath.replace(/\\/g, '/').toLowerCase()

      for (const vault of registry.vaults) {
        const normalizedVaultPath = vault.path.replace(/\\/g, '/').toLowerCase()

        if (normalizedInputPath.startsWith(normalizedVaultPath)) {
          if (normalizedVaultPath.length > bestMatchLength) {
            bestMatch = vault
            bestMatchLength = normalizedVaultPath.length
          }
        }

        // Also check contexts within vaults
        if (vault.contexts) {
          for (const context of vault.contexts) {
            const normalizedContextPath = context.path.replace(/\\/g, '/').toLowerCase()

            if (normalizedInputPath.startsWith(normalizedContextPath)) {
              if (normalizedContextPath.length > bestMatchLength) {
                bestMatch = { ...vault, selectedContext: context }
                bestMatchLength = normalizedContextPath.length
              }
            }
          }
        }
      }

      if (!bestMatch) {
        throw new Error(`Cannot find vault for file path: ${inputPath}`)
      }

      // Extract context from path or use selected context
      const contextId = bestMatch.selectedContext
        ? bestMatch.selectedContext.id
        : this.extractContextFromPath(inputPath, bestMatch.path)

      const vaultPath = bestMatch.selectedContext
        ? bestMatch.selectedContext.path
        : bestMatch.path

      console.log('[VAULT-CONTEXT] ✅ File path resolved successfully:', {
        inputPath,
        vaultPath,
        contextId,
        matchType: bestMatch.selectedContext ? 'context' : 'vault'
      })

      return {
        type: 'file',
        vault: vaultPath,
        context: contextId,
        path: inputPath, // Keep original path for file operations
        originalPath: inputPath
      }
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Failed to resolve file path:', error)
      throw new ServiceError(
        ServiceErrorCode.VALIDATION_ERROR,
        `Failed to resolve file path: ${inputPath}`,
        { serviceName: this.serviceName, operation: 'resolveFilePath', details: { inputPath, error } }
      )
    }
  }

  /**
   * Resolve vault-relative paths (documents/file.pdf)
   */
  private async resolveVaultRelativePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    const context = await this.getContextForResolution(options)
    const resolvedPath = `${context.contextPath}/${inputPath}`
    
    return {
      type: 'vault-relative',
      vault: context.vaultPath,
      context: context.contextId,
      path: resolvedPath,
      originalPath: inputPath
    }
  }

  /**
   * Get context for path resolution
   */
  private async getContextForResolution(options?: {
    vault?: string
    context?: string
  }): Promise<VaultContext> {
    // Use provided context if available
    if (options?.vault && options?.context) {
      return {
        vaultPath: options.vault,
        contextId: options.context,
        contextName: options.context,
        contextPath: `${options.vault}/${options.context}`
      }
    }

    // Use current context
    if (this.currentContext) {
      return this.currentContext
    }

    throw new ServiceError(
      ServiceErrorCode.CONFIGURATION_ERROR,
      'No vault context available for path resolution',
      { serviceName: this.serviceName, operation: 'getContextForResolution' }
    )
  }

  /**
   * Extract context ID from file path
   */
  private extractContextFromPath(filePath: string, vaultPath: string): string {
    const relativePath = filePath.replace(vaultPath, '').replace(/^[\\\/]/, '')
    const pathParts = relativePath.split(/[\\\/]/)
    return pathParts[0] || 'default'
  }

  /**
   * Load current context from URL params or storage
   */
  private async loadCurrentContext(): Promise<void> {
    try {
      // Try to get context from URL params first
      const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '')
      const contextParam = urlParams.get('context')
      const vaultParam = urlParams.get('vault')

      if (contextParam && vaultParam) {
        const context: VaultContext = {
          vaultPath: vaultParam,
          contextId: contextParam,
          contextName: contextParam,
          contextPath: `${vaultParam}/${contextParam}`
        }
        
        await this.setCurrentContext(context)
        return
      }

      // Fallback to default context from vault registry
      const registry = await window.electronAPI?.vault?.getVaultRegistry()
      if (registry?.vaults?.[0]?.contexts?.[0]) {
        const vault = registry.vaults[0]
        const context = vault.contexts[0]
        
        const vaultContext: VaultContext = {
          vaultPath: vault.path,
          contextId: context.id,
          contextName: context.name,
          contextPath: context.path
        }
        
        await this.setCurrentContext(vaultContext)
      }
    } catch (error) {
      this.logger.warn('Failed to load current context', 'loadCurrentContext', error)
    }
  }

  /**
   * Notify all context listeners
   */
  private notifyContextListeners(): void {
    this.contextListeners.forEach(listener => {
      try {
        listener(this.currentContext)
      } catch (error) {
        this.logger.error('Error in context listener', 'notifyContextListeners', error)
      }
    })
  }
}

export const vaultContextService = new VaultContextService()
