import React, { useState } from 'react';
import OfficeViewer from './OfficeViewer';

interface FileViewerTestPageProps {
  isOpen: boolean;
  onClose: () => void;
}

const FileViewerTestPage: React.FC<FileViewerTestPageProps> = ({ isOpen, onClose }) => {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [testFiles] = useState([
    'test-documents/sample.docx',
    'test-documents/sample.xlsx', 
    'test-documents/sample.pptx',
    'test-documents/legacy.doc',
    'test-documents/spreadsheet.xls',
    'test-documents/presentation.ppt',
    'test-documents/corrupted.docx',
    'test-documents/unsupported.pdf'
  ]);
  const [viewerKey, setViewerKey] = useState(0);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handleFileSelect = (filePath: string) => {
    setSelectedFile(filePath);
    setViewerKey(prev => prev + 1); // Force re-render
    addLog(`Selected file: ${filePath}`);
  };

  const handleViewerError = (error: string) => {
    addLog(`ERROR: ${error}`);
  };

  const handleViewerLoad = () => {
    addLog(`Successfully loaded: ${selectedFile}`);
  };

  const clearLogs = () => {
    setLogs([]);
    addLog('Logs cleared');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[95%] h-[95%] max-w-7xl max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">File Viewer Test Suite</h2>
            <p className="text-sm text-gray-600 mt-1">
              Testing Office document binary presentation and parsing capabilities
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - File Selection and Logs */}
          <div className="w-80 border-r border-gray-200 flex flex-col">
            {/* File Selection */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Test Files</h3>
              <div className="space-y-2">
                {testFiles.map((file, index) => (
                  <button
                    key={index}
                    onClick={() => handleFileSelect(file)}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      selectedFile === file
                        ? 'bg-blue-100 text-blue-900 border border-blue-300'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200'
                    }`}
                  >
                    <div className="font-medium">{file.split('/').pop()}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {file.split('.').pop()?.toUpperCase()} • {file}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Test Controls */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Test Controls</h3>
              <div className="space-y-2">
                <button
                  onClick={() => handleFileSelect(selectedFile)}
                  disabled={!selectedFile}
                  className="w-full px-3 py-2 bg-green-100 text-green-800 rounded-md text-sm hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Reload Current File
                </button>
                <button
                  onClick={clearLogs}
                  className="w-full px-3 py-2 bg-gray-100 text-gray-800 rounded-md text-sm hover:bg-gray-200 transition-colors"
                >
                  Clear Logs
                </button>
              </div>
            </div>

            {/* Logs Panel */}
            <div className="flex-1 p-4 overflow-hidden">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Test Logs</h3>
              <div className="bg-gray-900 rounded-lg p-3 h-full overflow-auto">
                {logs.length === 0 ? (
                  <p className="text-gray-400 text-sm">No logs yet. Select a file to start testing.</p>
                ) : (
                  <div className="space-y-1">
                    {logs.map((log, index) => (
                      <div
                        key={index}
                        className={`text-sm font-mono ${
                          log.includes('ERROR') 
                            ? 'text-red-400' 
                            : log.includes('Successfully') 
                            ? 'text-green-400' 
                            : 'text-gray-300'
                        }`}
                      >
                        {log}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Viewer */}
          <div className="flex-1 p-4">
            {selectedFile ? (
              <OfficeViewer
                key={viewerKey}
                filePath={selectedFile}
                onError={handleViewerError}
                onLoad={handleViewerLoad}
              />
            ) : (
              <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <div className="text-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No file selected</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Choose a test file from the left panel to begin testing
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status Bar */}
        <div className="border-t border-gray-200 px-4 py-2 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              Status: {selectedFile ? `Testing ${selectedFile.split('/').pop()}` : 'Ready to test'}
            </div>
            <div>
              Tech Stack: React + TypeScript + Local File Processing
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileViewerTestPage;
