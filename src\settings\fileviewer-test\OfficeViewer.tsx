import React, { useState, useEffect } from 'react';

interface OfficeViewerProps {
  filePath: string;
  fileType?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

interface ViewerState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
  content: string | null;
  binaryData: ArrayBuffer | null;
  fileInfo: {
    name: string;
    size: number;
    type: string;
    lastModified?: Date;
  } | null;
}

const OfficeViewer: React.FC<OfficeViewerProps> = ({ 
  filePath, 
  fileType, 
  onError, 
  onLoad 
}) => {
  const [state, setState] = useState<ViewerState>({
    isLoading: true,
    hasError: false,
    errorMessage: '',
    content: null,
    binaryData: null,
    fileInfo: null
  });

  const supportedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
    'application/msword', // .doc
    'application/vnd.ms-excel', // .xls
    'application/vnd.ms-powerpoint' // .ppt
  ];

  const getFileExtension = (path: string): string => {
    return path.split('.').pop()?.toLowerCase() || '';
  };

  const detectFileType = (path: string): string => {
    const extension = getFileExtension(path);
    const typeMap: { [key: string]: string } = {
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'doc': 'application/msword',
      'xls': 'application/vnd.ms-excel',
      'ppt': 'application/vnd.ms-powerpoint'
    };
    return typeMap[extension] || 'application/octet-stream';
  };

  const isSupported = (type: string): boolean => {
    return supportedTypes.includes(type);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const simulateFileLoad = async (path: string): Promise<void> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, hasError: false }));
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const detectedType = fileType || detectFileType(path);
      const fileName = path.split('/').pop() || path.split('\\').pop() || 'unknown';
      
      if (!isSupported(detectedType)) {
        throw new Error(`Unsupported file type: ${detectedType}`);
      }

      // Simulate binary data reading
      const mockSize = Math.floor(Math.random() * 1000000) + 10000; // 10KB to 1MB
      const binaryData = new ArrayBuffer(mockSize);
      const view = new Uint8Array(binaryData);
      
      // Add mock Office document signature
      view[0] = 0x50; // 'P'
      view[1] = 0x4B; // 'K' (ZIP signature for modern Office docs)
      view[2] = 0x03;
      view[3] = 0x04;

      // Simulate extracted text content
      const mockContent = `Document Preview:\n\nFile: ${fileName}\nType: ${detectedType}\nSize: ${formatFileSize(mockSize)}\n\nThis is a simulated preview of the Office document content.\n\nIn a real implementation, this would show:\n- Document text content\n- Formatting information\n- Embedded images\n- Tables and charts\n- Metadata\n\nBinary data has been successfully loaded and processed.`;

      setState({
        isLoading: false,
        hasError: false,
        errorMessage: '',
        content: mockContent,
        binaryData,
        fileInfo: {
          name: fileName,
          size: mockSize,
          type: detectedType,
          lastModified: new Date()
        }
      });

      onLoad?.();
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setState(prev => ({
        ...prev,
        isLoading: false,
        hasError: true,
        errorMessage: errorMsg
      }));
      onError?.(errorMsg);
    }
  };

  useEffect(() => {
    if (filePath) {
      simulateFileLoad(filePath);
    }
  }, [filePath, fileType]);

  const renderLoadingState = () => (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <span className="ml-3 text-gray-600">Loading document...</span>
    </div>
  );

  const renderErrorState = () => (
    <div className="bg-red-50 border border-red-200 rounded-lg p-6 m-4">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">Error Loading Document</h3>
          <p className="text-sm text-red-700 mt-1">{state.errorMessage}</p>
        </div>
      </div>
      <div className="mt-4">
        <button 
          onClick={() => simulateFileLoad(filePath)}
          className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>
  );

  const renderFileInfo = () => {
    if (!state.fileInfo) return null;
    
    return (
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Name:</span>
            <span className="ml-2 text-gray-900">{state.fileInfo.name}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Size:</span>
            <span className="ml-2 text-gray-900">{formatFileSize(state.fileInfo.size)}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Type:</span>
            <span className="ml-2 text-gray-900">{getFileExtension(state.fileInfo.name).toUpperCase()}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Binary Data:</span>
            <span className="ml-2 text-green-600">✓ Loaded</span>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => (
    <div className="flex-1 overflow-auto p-6">
      <div className="max-w-4xl mx-auto">
        <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono bg-white border border-gray-200 rounded-lg p-4">
          {state.content}
        </pre>
        
        {state.binaryData && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Binary Data Analysis</h4>
            <div className="text-sm text-blue-800">
              <p>Buffer Size: {state.binaryData.byteLength} bytes</p>
              <p>First 4 bytes: {Array.from(new Uint8Array(state.binaryData.slice(0, 4))).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}</p>
              <p>Status: Binary data successfully parsed and ready for processing</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col bg-white border border-gray-300 rounded-lg shadow-sm">
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-3">
        <h3 className="text-lg font-medium text-gray-900">Office Document Viewer (Test)</h3>
        <p className="text-sm text-gray-600 mt-1">
          Testing binary presentation and document parsing
        </p>
      </div>
      
      {state.isLoading && renderLoadingState()}
      {state.hasError && renderErrorState()}
      {!state.isLoading && !state.hasError && (
        <>
          {renderFileInfo()}
          {renderContent()}
        </>
      )}
    </div>
  );
};

export default OfficeViewer;
