/**
 * File Attachment Continuity Test Suite
 * 
 * Tests to verify that file intelligence context persists across multiple 
 * messages in the same conversation and that the new architecture works correctly.
 */

import { conversationIntelligenceManager } from '../services/conversationIntelligenceManager'
import { unifiedIntelligenceService } from '../services/unifiedIntelligenceService'
import { fileAttachmentManager } from '../services/fileAttachmentManager'
import { intelligenceCacheManager } from '../services/intelligenceCacheManager'

// Mock data for testing
const mockFileInfo = {
  filePath: '/test/documents/sample.pdf',
  vaultPath: '/test/vault',
  fileSize: 1024000,
  lastModified: '2024-01-15T10:00:00Z'
}

const mockIntelligence = {
  filePath: mockFileInfo.filePath,
  vaultPath: mockFileInfo.vaultPath,
  extractedContent: 'This is a sample document with important information about testing.',
  intelligence: {
    key_ideas: [
      { text: 'Testing is important for software quality', relevance: 95 },
      { text: 'File attachment continuity ensures context persistence', relevance: 90 }
    ],
    summary: 'A document about testing methodologies and file handling.',
    entities: ['testing', 'software', 'quality']
  },
  source: 'test-intelligence',
  processingTime: 150,
  lastUpdated: new Date().toISOString()
}

const mockConversationId = 'test-conversation-123'

describe('File Attachment Continuity Tests', () => {
  beforeEach(() => {
    // Clear all caches and reset state
    conversationIntelligenceManager.clearConversation(mockConversationId)
    fileAttachmentManager.clearCache()
    intelligenceCacheManager.clear()
  })

  describe('Conversation Intelligence Context Management', () => {
    test('should set active conversation correctly', () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      const stats = conversationIntelligenceManager.getConversationStats()
      expect(stats?.conversationId).toBe(mockConversationId)
    })

    test('should attach file to conversation and maintain context', async () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      // Mock the unified intelligence service
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockResolvedValue(mockIntelligence)
      
      const fileContext = await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-1' }
      )
      
      expect(fileContext).toBeTruthy()
      expect(fileContext?.filePath).toBe(mockFileInfo.filePath)
      expect(fileContext?.intelligence.extractedContent).toBe(mockIntelligence.extractedContent)
    })

    test('should prevent duplicate file attachments', async () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockResolvedValue(mockIntelligence)
      
      // Attach file first time
      const firstAttachment = await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-1' }
      )
      
      // Attach same file second time
      const secondAttachment = await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-2' }
      )
      
      expect(firstAttachment).toBeTruthy()
      expect(secondAttachment).toBeTruthy()
      expect(secondAttachment?.messageIds).toContain('msg-1')
      expect(secondAttachment?.messageIds).toContain('msg-2')
    })

    test('should build AI context string from attached files', async () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockResolvedValue(mockIntelligence)
      
      await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-1' }
      )
      
      const contextString = conversationIntelligenceManager.buildContextForAI()
      
      expect(contextString).toContain('ATTACHED FILES CONTEXT')
      expect(contextString).toContain(mockFileInfo.filePath)
      expect(contextString).toContain(mockIntelligence.extractedContent)
    })
  })

  describe('Intelligence Cache Management', () => {
    test('should cache intelligence data correctly', () => {
      const cacheKey = 'test-intelligence-key'
      const testData = { test: 'data', timestamp: Date.now() }
      
      intelligenceCacheManager.set(cacheKey, testData, 60000) // 1 minute TTL
      
      const cachedData = intelligenceCacheManager.get(cacheKey)
      expect(cachedData).toEqual(testData)
    })

    test('should respect TTL and expire cached data', (done) => {
      const cacheKey = 'test-expiry-key'
      const testData = { test: 'expiry-data' }
      
      intelligenceCacheManager.set(cacheKey, testData, 100) // 100ms TTL
      
      setTimeout(() => {
        const cachedData = intelligenceCacheManager.get(cacheKey)
        expect(cachedData).toBeNull()
        done()
      }, 150)
    })

    test('should provide accurate cache statistics', () => {
      intelligenceCacheManager.clear()
      
      intelligenceCacheManager.set('key1', { data: 1 }, 60000)
      intelligenceCacheManager.set('key2', { data: 2 }, 60000)
      
      const stats = intelligenceCacheManager.getStats()
      expect(stats.totalEntries).toBe(2)
      expect(stats.totalMemoryUsage).toBeGreaterThan(0)
    })
  })

  describe('File Attachment Manager', () => {
    test('should attach file to message and conversation', async () => {
      const mockMessageId = 'msg-123'
      const mockFileId = 'file-456'
      
      // Mock the electron API
      const mockAddFileAttachment = jest.fn().mockResolvedValue('attachment-789')
      global.window = {
        electronAPI: {
          files: {
            addFileAttachment: mockAddFileAttachment
          },
          db: {
            files: {
              addFileAttachment: jest.fn().mockResolvedValue('conv-attachment-101')
            }
          }
        }
      } as any
      
      const result = await fileAttachmentManager.attachFileToMessage(
        mockMessageId,
        mockConversationId,
        mockFileId,
        { attachmentType: 'attachment', includeInConversation: true }
      )
      
      expect(result.messageAttachmentId).toBe('attachment-789')
      expect(result.conversationAttachmentId).toBe('conv-attachment-101')
    })

    test('should get conversation files correctly', async () => {
      // Mock the electron API for getting messages and files
      global.window = {
        electronAPI: {
          db: {
            getMessages: jest.fn().mockResolvedValue([
              { id: 'msg-1', content: 'Test message' }
            ])
          },
          files: {
            getMessageFiles: jest.fn().mockResolvedValue([
              {
                id: 'file-1',
                filename: 'test.pdf',
                attachment_id: 'att-1',
                attachment_type: 'attachment',
                attachment_created_at: '2024-01-15T10:00:00Z'
              }
            ])
          }
        }
      } as any
      
      const files = await fileAttachmentManager.getConversationFiles(mockConversationId)
      
      expect(files).toHaveLength(1)
      expect(files[0].fileId).toBe('file-1')
      expect(files[0].attachmentType).toBe('attachment')
    })
  })

  describe('Integration Tests', () => {
    test('should maintain context across multiple messages', async () => {
      // Set up conversation
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      // Mock intelligence service
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockResolvedValue(mockIntelligence)
      
      // Attach file to first message
      await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-1' }
      )
      
      // Verify context is available for second message
      const contextForSecondMessage = conversationIntelligenceManager.buildContextForAI()
      expect(contextForSecondMessage).toContain(mockIntelligence.extractedContent)
      
      // Attach same file to second message (should reuse context)
      const secondAttachment = await conversationIntelligenceManager.attachFileToConversation(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath,
        { messageId: 'msg-2' }
      )
      
      expect(secondAttachment?.messageIds).toContain('msg-1')
      expect(secondAttachment?.messageIds).toContain('msg-2')
    })

    test('should handle conversation cleanup correctly', () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      // Add some data
      intelligenceCacheManager.set(`conversation:${mockConversationId}`, { test: 'data' }, 60000)
      
      // Clear conversation
      conversationIntelligenceManager.clearConversation(mockConversationId)
      
      // Verify cleanup
      const cachedData = intelligenceCacheManager.get(`conversation:${mockConversationId}`)
      expect(cachedData).toBeNull()
    })

    test('should provide memory usage statistics', () => {
      conversationIntelligenceManager.setActiveConversation(mockConversationId)
      
      const memoryStats = conversationIntelligenceManager.getMemoryStats()
      
      expect(memoryStats).toHaveProperty('totalConversations')
      expect(memoryStats).toHaveProperty('totalFiles')
      expect(memoryStats).toHaveProperty('estimatedMemoryUsage')
      expect(memoryStats).toHaveProperty('activeConversation')
      expect(memoryStats.activeConversation).toBe(mockConversationId)
    })
  })
})

// Test runner helper
export const runFileAttachmentContinuityTests = async () => {
  console.log('🧪 Running File Attachment Continuity Tests...')
  
  try {
    // This would be run by Jest in a real test environment
    // For now, we'll just log that the test suite is ready
    console.log('✅ Test suite created and ready for execution')
    console.log('📋 Test coverage includes:')
    console.log('  - Conversation intelligence context management')
    console.log('  - Intelligence cache management')
    console.log('  - File attachment manager functionality')
    console.log('  - Integration tests for context persistence')
    console.log('  - Memory management and cleanup')
    
    return {
      success: true,
      message: 'File attachment continuity test suite ready',
      testCount: 12
    }
  } catch (error) {
    console.error('💥 Error setting up test suite:', error)
    return {
      success: false,
      message: 'Failed to set up test suite',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
