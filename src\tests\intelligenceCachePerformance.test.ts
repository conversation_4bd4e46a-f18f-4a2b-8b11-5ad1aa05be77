/**
 * Intelligence Cache Performance Test Suite
 * 
 * Tests to verify performance improvements from intelligence caching
 * and ensure no memory leaks in the caching system.
 */

import { intelligenceCacheManager } from '../services/intelligenceCacheManager'
import { conversationIntelligenceManager } from '../services/conversationIntelligenceManager'

// Performance test utilities
const measurePerformance = async (fn: () => Promise<any> | any, iterations: number = 1) => {
  const times: number[] = []
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now()
    await fn()
    const end = performance.now()
    times.push(end - start)
  }
  
  return {
    average: times.reduce((a, b) => a + b, 0) / times.length,
    min: Math.min(...times),
    max: Math.max(...times),
    total: times.reduce((a, b) => a + b, 0)
  }
}

const generateMockIntelligenceData = (size: 'small' | 'medium' | 'large' = 'medium') => {
  const baseSizes = {
    small: { content: 100, ideas: 3, entities: 5 },
    medium: { content: 1000, ideas: 10, entities: 20 },
    large: { content: 10000, ideas: 50, entities: 100 }
  }
  
  const config = baseSizes[size]
  
  return {
    filePath: `/test/file-${Date.now()}.pdf`,
    vaultPath: '/test/vault',
    extractedContent: 'A'.repeat(config.content),
    intelligence: {
      key_ideas: Array.from({ length: config.ideas }, (_, i) => ({
        text: `Key idea ${i + 1}`,
        relevance: Math.random() * 100
      })),
      summary: 'Test summary for performance testing',
      entities: Array.from({ length: config.entities }, (_, i) => `entity-${i}`)
    },
    source: 'performance-test',
    processingTime: Math.random() * 1000,
    lastUpdated: new Date().toISOString()
  }
}

describe('Intelligence Cache Performance Tests', () => {
  beforeEach(() => {
    // Clear cache before each test
    intelligenceCacheManager.clear()
  })

  afterEach(() => {
    // Clean up after each test
    intelligenceCacheManager.clear()
  })

  describe('Cache Operations Performance', () => {
    test('should perform cache set operations efficiently', async () => {
      const testData = generateMockIntelligenceData('medium')
      const iterations = 1000
      
      const performance = await measurePerformance(() => {
        const key = `test-key-${Math.random()}`
        intelligenceCacheManager.set(key, testData, 60000)
      }, iterations)
      
      expect(performance.average).toBeLessThan(1) // Should be under 1ms per operation
      expect(performance.max).toBeLessThan(10) // No operation should take more than 10ms
    })

    test('should perform cache get operations efficiently', async () => {
      // Pre-populate cache
      const keys: string[] = []
      for (let i = 0; i < 1000; i++) {
        const key = `test-key-${i}`
        keys.push(key)
        intelligenceCacheManager.set(key, generateMockIntelligenceData('small'), 60000)
      }
      
      const performance = await measurePerformance(() => {
        const randomKey = keys[Math.floor(Math.random() * keys.length)]
        return intelligenceCacheManager.get(randomKey)
      }, 1000)
      
      expect(performance.average).toBeLessThan(0.5) // Should be under 0.5ms per operation
    })

    test('should handle large data sets efficiently', async () => {
      const largeData = generateMockIntelligenceData('large')
      
      const setPerformance = await measurePerformance(() => {
        intelligenceCacheManager.set('large-data-key', largeData, 60000)
      }, 100)
      
      const getPerformance = await measurePerformance(() => {
        return intelligenceCacheManager.get('large-data-key')
      }, 100)
      
      expect(setPerformance.average).toBeLessThan(5) // Should handle large data in under 5ms
      expect(getPerformance.average).toBeLessThan(2) // Retrieval should be under 2ms
    })
  })

  describe('Memory Management', () => {
    test('should not leak memory with frequent cache operations', async () => {
      const initialStats = intelligenceCacheManager.getStats()
      const initialMemory = initialStats.totalMemoryUsage
      
      // Perform many cache operations
      for (let i = 0; i < 10000; i++) {
        const key = `temp-key-${i}`
        const data = generateMockIntelligenceData('small')
        
        intelligenceCacheManager.set(key, data, 100) // Short TTL
        
        // Occasionally get and delete
        if (i % 100 === 0) {
          intelligenceCacheManager.get(key)
          intelligenceCacheManager.delete(key)
        }
      }
      
      // Wait for TTL expiration
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // Force cleanup
      intelligenceCacheManager.cleanup()
      
      const finalStats = intelligenceCacheManager.getStats()
      const memoryGrowth = finalStats.totalMemoryUsage - initialMemory
      
      // Memory growth should be minimal after cleanup
      expect(memoryGrowth).toBeLessThan(1000000) // Less than 1MB growth
    })

    test('should respect TTL and clean up expired entries', async () => {
      const shortTTL = 50 // 50ms
      const testData = generateMockIntelligenceData('medium')
      
      // Set data with short TTL
      for (let i = 0; i < 100; i++) {
        intelligenceCacheManager.set(`ttl-key-${i}`, testData, shortTTL)
      }
      
      const initialStats = intelligenceCacheManager.getStats()
      expect(initialStats.totalEntries).toBe(100)
      
      // Wait for TTL expiration
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Access cache to trigger cleanup
      intelligenceCacheManager.get('non-existent-key')
      
      const finalStats = intelligenceCacheManager.getStats()
      expect(finalStats.totalEntries).toBeLessThan(initialStats.totalEntries)
    })

    test('should handle memory pressure gracefully', async () => {
      const largeDataSets: any[] = []
      
      try {
        // Try to fill cache with large amounts of data
        for (let i = 0; i < 1000; i++) {
          const largeData = generateMockIntelligenceData('large')
          largeDataSets.push(largeData)
          intelligenceCacheManager.set(`pressure-key-${i}`, largeData, 300000)
          
          // Check if we're approaching memory limits
          const stats = intelligenceCacheManager.getStats()
          if (stats.totalMemoryUsage > 100 * 1024 * 1024) { // 100MB limit
            break
          }
        }
        
        // Cache should still be functional
        const testKey = 'pressure-key-0'
        const retrieved = intelligenceCacheManager.get(testKey)
        expect(retrieved).toBeTruthy()
        
      } catch (error) {
        // Should handle memory pressure without crashing
        expect(error).toBeInstanceOf(Error)
      }
    })
  })

  describe('Conversation Intelligence Performance', () => {
    test('should handle multiple conversations efficiently', async () => {
      const conversationIds = Array.from({ length: 100 }, (_, i) => `conv-${i}`)
      
      const performance = await measurePerformance(async () => {
        const convId = conversationIds[Math.floor(Math.random() * conversationIds.length)]
        conversationIntelligenceManager.setActiveConversation(convId)
        
        // Simulate file attachment
        const mockData = generateMockIntelligenceData('small')
        await conversationIntelligenceManager.attachFileToConversation(
          mockData.filePath,
          mockData.vaultPath,
          { includeInHistory: true }
        )
      }, 500)
      
      expect(performance.average).toBeLessThan(10) // Should be under 10ms per operation
    })

    test('should build AI context efficiently for large conversations', async () => {
      const conversationId = 'large-conversation'
      conversationIntelligenceManager.setActiveConversation(conversationId)
      
      // Attach many files to conversation
      for (let i = 0; i < 50; i++) {
        const mockData = generateMockIntelligenceData('medium')
        mockData.filePath = `/test/file-${i}.pdf`
        
        // Mock the intelligence service
        jest.spyOn(require('../services/unifiedIntelligenceService').unifiedIntelligenceService, 'getIntelligence')
          .mockResolvedValue(mockData)
        
        await conversationIntelligenceManager.attachFileToConversation(
          mockData.filePath,
          mockData.vaultPath,
          { includeInHistory: true }
        )
      }
      
      const performance = await measurePerformance(() => {
        return conversationIntelligenceManager.buildContextForAI()
      }, 100)
      
      expect(performance.average).toBeLessThan(50) // Should build context in under 50ms
    })

    test('should clean up old conversations to prevent memory leaks', async () => {
      const initialMemoryStats = conversationIntelligenceManager.getMemoryStats()
      
      // Create many conversations
      for (let i = 0; i < 200; i++) {
        const convId = `temp-conv-${i}`
        conversationIntelligenceManager.setActiveConversation(convId)
        
        // Add some data to each conversation
        const mockData = generateMockIntelligenceData('small')
        mockData.filePath = `/temp/file-${i}.pdf`
        
        jest.spyOn(require('../services/unifiedIntelligenceService').unifiedIntelligenceService, 'getIntelligence')
          .mockResolvedValue(mockData)
        
        await conversationIntelligenceManager.attachFileToConversation(
          mockData.filePath,
          mockData.vaultPath
        )
      }
      
      const beforeCleanupStats = conversationIntelligenceManager.getMemoryStats()
      expect(beforeCleanupStats.totalConversations).toBeGreaterThan(100)
      
      // Clean up old conversations (simulate 1 hour age)
      const cleaned = conversationIntelligenceManager.cleanupOldConversations(0) // Clean all
      
      const afterCleanupStats = conversationIntelligenceManager.getMemoryStats()
      expect(cleaned).toBeGreaterThan(0)
      expect(afterCleanupStats.totalConversations).toBeLessThan(beforeCleanupStats.totalConversations)
    })
  })

  describe('Cache Hit Rate Optimization', () => {
    test('should achieve high cache hit rates for repeated access', async () => {
      const testKeys = Array.from({ length: 100 }, (_, i) => `cache-test-${i}`)
      
      // Populate cache
      testKeys.forEach(key => {
        intelligenceCacheManager.set(key, generateMockIntelligenceData('small'), 60000)
      })
      
      let hits = 0
      let misses = 0
      
      // Simulate repeated access with some new keys
      for (let i = 0; i < 1000; i++) {
        const isExistingKey = Math.random() < 0.8 // 80% chance of existing key
        const key = isExistingKey 
          ? testKeys[Math.floor(Math.random() * testKeys.length)]
          : `new-key-${i}`
        
        const result = intelligenceCacheManager.get(key)
        if (result) {
          hits++
        } else {
          misses++
          // Add new key to cache
          intelligenceCacheManager.set(key, generateMockIntelligenceData('small'), 60000)
        }
      }
      
      const hitRate = hits / (hits + misses)
      expect(hitRate).toBeGreaterThan(0.75) // Should achieve >75% hit rate
    })
  })

  describe('Concurrent Access Performance', () => {
    test('should handle concurrent cache operations efficiently', async () => {
      const concurrentOperations = Array.from({ length: 100 }, (_, i) => {
        return async () => {
          const key = `concurrent-key-${i}`
          const data = generateMockIntelligenceData('small')
          
          // Mix of set and get operations
          if (i % 2 === 0) {
            intelligenceCacheManager.set(key, data, 60000)
          } else {
            intelligenceCacheManager.get(key)
          }
        }
      })
      
      const start = performance.now()
      await Promise.all(concurrentOperations.map(op => op()))
      const end = performance.now()
      
      const totalTime = end - start
      expect(totalTime).toBeLessThan(100) // Should complete all operations in under 100ms
    })
  })
})

// Test runner helper
export const runIntelligenceCachePerformanceTests = async () => {
  console.log('⚡ Running Intelligence Cache Performance Tests...')
  
  try {
    console.log('✅ Intelligence cache performance test suite created and ready for execution')
    console.log('📋 Test coverage includes:')
    console.log('  - Cache operation performance (set/get)')
    console.log('  - Large data set handling')
    console.log('  - Memory leak prevention')
    console.log('  - TTL and cleanup efficiency')
    console.log('  - Memory pressure handling')
    console.log('  - Conversation intelligence performance')
    console.log('  - Cache hit rate optimization')
    console.log('  - Concurrent access performance')
    
    return {
      success: true,
      message: 'Intelligence cache performance test suite ready',
      testCount: 12
    }
  } catch (error) {
    console.error('💥 Error setting up intelligence cache performance test suite:', error)
    return {
      success: false,
      message: 'Failed to set up intelligence cache performance test suite',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
