/**
 * Intelligence-First Processing Test Suite
 * 
 * Tests to verify that existing intelligence is properly loaded and new files 
 * are processed correctly using the intelligence-first approach.
 */

import { unifiedIntelligenceService } from '../services/unifiedIntelligenceService'
import { intelligenceExistenceService } from '../services/intelligenceExistenceService'
import { intelligenceCacheManager } from '../services/intelligenceCacheManager'

// Mock data for testing
const mockFileInfo = {
  filePath: '/test/documents/sample.pdf',
  vaultPath: '/test/vault',
  fileSize: 1024000,
  lastModified: '2024-01-15T10:00:00Z'
}

const mockExistingIntelligence = {
  filePath: mockFileInfo.filePath,
  vaultPath: mockFileInfo.vaultPath,
  extractedContent: 'Existing intelligence content from artifacts.json',
  intelligence: {
    key_ideas: [
      { text: 'Pre-existing key idea 1', relevance: 95 },
      { text: 'Pre-existing key idea 2', relevance: 88 }
    ],
    summary: 'Pre-existing summary from intelligence folder',
    entities: ['existing', 'intelligence', 'artifacts']
  },
  source: 'existing-artifacts',
  processingTime: 0,
  lastUpdated: '2024-01-14T12:00:00Z'
}

const mockNewIntelligence = {
  filePath: mockFileInfo.filePath,
  vaultPath: mockFileInfo.vaultPath,
  extractedContent: 'Newly processed content from file',
  intelligence: {
    key_ideas: [
      { text: 'Newly extracted key idea 1', relevance: 92 },
      { text: 'Newly extracted key idea 2', relevance: 85 }
    ],
    summary: 'Newly generated summary from processing',
    entities: ['new', 'processing', 'extraction']
  },
  source: 'fresh-processing',
  processingTime: 2500,
  lastUpdated: new Date().toISOString()
}

describe('Intelligence-First Processing Tests', () => {
  beforeEach(() => {
    // Clear all caches and reset mocks
    intelligenceCacheManager.clear()
    jest.clearAllMocks()
  })

  describe('Intelligence Existence Check', () => {
    test('should detect existing intelligence correctly', async () => {
      // Mock intelligence existence check
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: true,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: '2024-01-14T12:00:00Z',
        fileSize: 2048
      })

      const result = await intelligenceExistenceService.checkIntelligenceExists(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath
      )

      expect(result.exists).toBe(true)
      expect(result.intelligencePath).toContain('artifacts.json')
    })

    test('should detect missing intelligence correctly', async () => {
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: false,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: null,
        fileSize: 0
      })

      const result = await intelligenceExistenceService.checkIntelligenceExists(
        mockFileInfo.filePath,
        mockFileInfo.vaultPath
      )

      expect(result.exists).toBe(false)
      expect(result.lastModified).toBeNull()
    })

    test('should handle intelligence check errors gracefully', async () => {
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockRejectedValue(
        new Error('File system access denied')
      )

      try {
        await intelligenceExistenceService.checkIntelligenceExists(
          mockFileInfo.filePath,
          mockFileInfo.vaultPath
        )
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('File system access denied')
      }
    })
  })

  describe('Unified Intelligence Service', () => {
    test('should load existing intelligence when available', async () => {
      // Mock existing intelligence
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: true,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: '2024-01-14T12:00:00Z',
        fileSize: 2048
      })

      jest.spyOn(intelligenceExistenceService, 'loadExistingIntelligence').mockResolvedValue(
        mockExistingIntelligence
      )

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)

      expect(result.source).toBe('existing-artifacts')
      expect(result.extractedContent).toBe('Existing intelligence content from artifacts.json')
      expect(result.processingTime).toBe(0)
    })

    test('should process new file when no intelligence exists', async () => {
      // Mock no existing intelligence
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: false,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: null,
        fileSize: 0
      })

      // Mock file processing
      global.window = {
        electronAPI: {
          files: {
            processFile: jest.fn().mockResolvedValue({
              success: true,
              content: {
                text: 'Newly processed content from file',
                metadata: { processingTime: 2500 }
              }
            })
          }
        }
      } as any

      jest.spyOn(unifiedIntelligenceService, 'processIntelligence').mockResolvedValue(
        mockNewIntelligence
      )

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)

      expect(result.source).toBe('fresh-processing')
      expect(result.extractedContent).toBe('Newly processed content from file')
      expect(result.processingTime).toBeGreaterThan(0)
    })

    test('should force reprocessing when requested', async () => {
      // Mock existing intelligence
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: true,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: '2024-01-14T12:00:00Z',
        fileSize: 2048
      })

      // Mock forced reprocessing
      jest.spyOn(unifiedIntelligenceService, 'processIntelligence').mockResolvedValue(
        mockNewIntelligence
      )

      const result = await unifiedIntelligenceService.getIntelligence(
        mockFileInfo,
        { forceReprocess: true }
      )

      expect(result.source).toBe('fresh-processing')
      expect(result.processingTime).toBeGreaterThan(0)
    })

    test('should prevent duplicate processing', async () => {
      const processingPromise = unifiedIntelligenceService.getIntelligence(mockFileInfo)
      
      // Start second request while first is processing
      const secondPromise = unifiedIntelligenceService.getIntelligence(mockFileInfo)
      
      // Both should resolve to the same result
      const [result1, result2] = await Promise.all([processingPromise, secondPromise])
      
      expect(result1).toEqual(result2)
    })
  })

  describe('Cache Integration', () => {
    test('should cache intelligence results', async () => {
      const cacheKey = `intelligence:${mockFileInfo.filePath}:${mockFileInfo.vaultPath}`
      
      // Set cache
      intelligenceCacheManager.set(cacheKey, mockExistingIntelligence, 30 * 60 * 1000)
      
      // Mock service to use cache
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockImplementation(async (fileInfo, options) => {
        if (!options?.forceReprocess) {
          const cached = intelligenceCacheManager.get(cacheKey)
          if (cached) return cached
        }
        return mockNewIntelligence
      })

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)
      
      expect(result).toEqual(mockExistingIntelligence)
    })

    test('should bypass cache when force reprocessing', async () => {
      const cacheKey = `intelligence:${mockFileInfo.filePath}:${mockFileInfo.vaultPath}`
      
      // Set cache
      intelligenceCacheManager.set(cacheKey, mockExistingIntelligence, 30 * 60 * 1000)
      
      // Mock service to bypass cache on force reprocess
      jest.spyOn(unifiedIntelligenceService, 'getIntelligence').mockImplementation(async (fileInfo, options) => {
        if (options?.forceReprocess) {
          return mockNewIntelligence
        }
        const cached = intelligenceCacheManager.get(cacheKey)
        return cached || mockNewIntelligence
      })

      const result = await unifiedIntelligenceService.getIntelligence(
        mockFileInfo,
        { forceReprocess: true }
      )
      
      expect(result).toEqual(mockNewIntelligence)
    })
  })

  describe('Error Handling and Fallbacks', () => {
    test('should handle corrupted intelligence files', async () => {
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: true,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: '2024-01-14T12:00:00Z',
        fileSize: 2048
      })

      jest.spyOn(intelligenceExistenceService, 'loadExistingIntelligence').mockRejectedValue(
        new Error('Corrupted intelligence file')
      )

      // Should fallback to fresh processing
      jest.spyOn(unifiedIntelligenceService, 'processIntelligence').mockResolvedValue(
        mockNewIntelligence
      )

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)
      
      expect(result.source).toBe('fresh-processing')
    })

    test('should handle file processing failures', async () => {
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: false,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: null,
        fileSize: 0
      })

      global.window = {
        electronAPI: {
          files: {
            processFile: jest.fn().mockRejectedValue(new Error('File processing failed'))
          }
        }
      } as any

      try {
        await unifiedIntelligenceService.getIntelligence(mockFileInfo)
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toContain('processing failed')
      }
    })
  })

  describe('Performance Optimization', () => {
    test('should prioritize existing intelligence over processing', async () => {
      const startTime = Date.now()
      
      // Mock fast existing intelligence load
      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: true,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: '2024-01-14T12:00:00Z',
        fileSize: 2048
      })

      jest.spyOn(intelligenceExistenceService, 'loadExistingIntelligence').mockImplementation(
        async () => {
          // Simulate fast load
          await new Promise(resolve => setTimeout(resolve, 10))
          return mockExistingIntelligence
        }
      )

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)
      const endTime = Date.now()
      
      expect(result.source).toBe('existing-artifacts')
      expect(endTime - startTime).toBeLessThan(100) // Should be very fast
    })

    test('should handle concurrent requests efficiently', async () => {
      const requests = Array.from({ length: 5 }, () =>
        unifiedIntelligenceService.getIntelligence(mockFileInfo)
      )
      
      const results = await Promise.all(requests)
      
      // All results should be identical (no duplicate processing)
      results.forEach(result => {
        expect(result).toEqual(results[0])
      })
    })
  })

  describe('Integration with File Processing Pipeline', () => {
    test('should integrate with kernel pipeline correctly', async () => {
      global.window = {
        electronAPI: {
          files: {
            processFile: jest.fn().mockResolvedValue({
              success: true,
              content: {
                text: 'Kernel processed content',
                metadata: {
                  processingTime: 1500,
                  source: 'kernel-pipeline',
                  artifacts: {
                    key_ideas: ['Kernel idea 1', 'Kernel idea 2'],
                    summary: 'Kernel generated summary'
                  }
                }
              }
            })
          }
        }
      } as any

      jest.spyOn(intelligenceExistenceService, 'checkIntelligenceExists').mockResolvedValue({
        exists: false,
        intelligencePath: '/test/vault/.intelligence/documents/sample_pdf_hash/artifacts.json',
        lastModified: null,
        fileSize: 0
      })

      const result = await unifiedIntelligenceService.getIntelligence(mockFileInfo)
      
      expect(global.window.electronAPI.files.processFile).toHaveBeenCalledWith(mockFileInfo.filePath)
      expect(result.extractedContent).toContain('Kernel processed content')
    })
  })
})

// Test runner helper
export const runIntelligenceFirstProcessingTests = async () => {
  console.log('🧠 Running Intelligence-First Processing Tests...')
  
  try {
    console.log('✅ Intelligence-first processing test suite created and ready for execution')
    console.log('📋 Test coverage includes:')
    console.log('  - Intelligence existence detection')
    console.log('  - Loading existing intelligence from artifacts.json')
    console.log('  - Fresh file processing when needed')
    console.log('  - Force reprocessing functionality')
    console.log('  - Duplicate processing prevention')
    console.log('  - Cache integration and bypass')
    console.log('  - Error handling and fallbacks')
    console.log('  - Performance optimization')
    console.log('  - Kernel pipeline integration')
    
    return {
      success: true,
      message: 'Intelligence-first processing test suite ready',
      testCount: 15
    }
  } catch (error) {
    console.error('💥 Error setting up intelligence-first processing test suite:', error)
    return {
      success: false,
      message: 'Failed to set up intelligence-first processing test suite',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
