/**
 * Comprehensive Test Runner
 * 
 * Executes all test suites for the ChatArea file attachment flow overhaul
 * and provides detailed reporting on the testing results.
 */

import { runFileAttachmentContinuityTests } from './fileAttachmentContinuity.test'
import { runEnhancedFilePickerUXTests } from './enhancedFilePickerUX.test'
import { runIntelligenceFirstProcessingTests } from './intelligenceFirstProcessing.test'
import { runIntelligenceCachePerformanceTests } from './intelligenceCachePerformance.test'

interface TestSuiteResult {
  name: string
  success: boolean
  message: string
  testCount: number
  duration?: number
  error?: string
}

interface TestRunSummary {
  totalSuites: number
  passedSuites: number
  failedSuites: number
  totalTests: number
  totalDuration: number
  results: TestSuiteResult[]
  overallSuccess: boolean
}

export class ChatAreaTestRunner {
  private results: TestSuiteResult[] = []

  /**
   * Run all test suites for the ChatArea file attachment flow
   */
  async runAllTests(): Promise<TestRunSummary> {
    console.log('🚀 Starting ChatArea File Attachment Flow Test Suite')
    console.log('=' .repeat(60))
    
    const startTime = performance.now()
    
    // Test Suite 1: File Attachment Continuity
    await this.runTestSuite(
      'File Attachment Continuity',
      runFileAttachmentContinuityTests,
      'Tests file intelligence context persistence across messages'
    )
    
    // Test Suite 2: Enhanced FilePicker UX
    await this.runTestSuite(
      'Enhanced FilePicker UX',
      runEnhancedFilePickerUXTests,
      'Tests new popup modal file selection experience'
    )
    
    // Test Suite 3: Intelligence-First Processing
    await this.runTestSuite(
      'Intelligence-First Processing',
      runIntelligenceFirstProcessingTests,
      'Tests existing intelligence loading and new file processing'
    )
    
    // Test Suite 4: Intelligence Cache Performance
    await this.runTestSuite(
      'Intelligence Cache Performance',
      runIntelligenceCachePerformanceTests,
      'Tests performance improvements and memory management'
    )
    
    const endTime = performance.now()
    const totalDuration = endTime - startTime
    
    // Generate summary
    const summary = this.generateSummary(totalDuration)
    
    // Print results
    this.printResults(summary)
    
    return summary
  }

  /**
   * Run individual test suite
   */
  private async runTestSuite(
    name: string,
    testFunction: () => Promise<any>,
    description: string
  ): Promise<void> {
    console.log(`\n📋 Running ${name} Tests...`)
    console.log(`   ${description}`)
    
    const startTime = performance.now()
    
    try {
      const result = await testFunction()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.results.push({
        name,
        success: result.success,
        message: result.message,
        testCount: result.testCount || 0,
        duration,
        error: result.error
      })
      
      if (result.success) {
        console.log(`   ✅ ${name}: ${result.message}`)
        console.log(`   📊 Tests: ${result.testCount}, Duration: ${duration.toFixed(2)}ms`)
      } else {
        console.log(`   ❌ ${name}: ${result.message}`)
        if (result.error) {
          console.log(`   💥 Error: ${result.error}`)
        }
      }
      
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.results.push({
        name,
        success: false,
        message: 'Test suite execution failed',
        testCount: 0,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      console.log(`   ❌ ${name}: Test suite execution failed`)
      console.log(`   💥 Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate test run summary
   */
  private generateSummary(totalDuration: number): TestRunSummary {
    const totalSuites = this.results.length
    const passedSuites = this.results.filter(r => r.success).length
    const failedSuites = totalSuites - passedSuites
    const totalTests = this.results.reduce((sum, r) => sum + r.testCount, 0)
    const overallSuccess = failedSuites === 0
    
    return {
      totalSuites,
      passedSuites,
      failedSuites,
      totalTests,
      totalDuration,
      results: this.results,
      overallSuccess
    }
  }

  /**
   * Print detailed test results
   */
  private printResults(summary: TestRunSummary): void {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 TEST RESULTS SUMMARY')
    console.log('=' .repeat(60))
    
    console.log(`Total Test Suites: ${summary.totalSuites}`)
    console.log(`Passed: ${summary.passedSuites} ✅`)
    console.log(`Failed: ${summary.failedSuites} ${summary.failedSuites > 0 ? '❌' : ''}`)
    console.log(`Total Tests: ${summary.totalTests}`)
    console.log(`Total Duration: ${summary.totalDuration.toFixed(2)}ms`)
    console.log(`Overall Status: ${summary.overallSuccess ? '✅ PASSED' : '❌ FAILED'}`)
    
    console.log('\n📋 DETAILED RESULTS:')
    console.log('-' .repeat(60))
    
    summary.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌'
      const duration = result.duration ? `${result.duration.toFixed(2)}ms` : 'N/A'
      
      console.log(`${index + 1}. ${status} ${result.name}`)
      console.log(`   Message: ${result.message}`)
      console.log(`   Tests: ${result.testCount}, Duration: ${duration}`)
      
      if (result.error) {
        console.log(`   Error: ${result.error}`)
      }
      console.log('')
    })
    
    if (summary.overallSuccess) {
      console.log('🎉 All test suites passed! ChatArea file attachment flow is ready.')
    } else {
      console.log('⚠️  Some test suites failed. Please review the errors above.')
    }
    
    console.log('\n📈 PERFORMANCE INSIGHTS:')
    console.log('-' .repeat(60))
    
    const avgDuration = summary.totalDuration / summary.totalSuites
    console.log(`Average test suite duration: ${avgDuration.toFixed(2)}ms`)
    
    const slowestSuite = summary.results.reduce((prev, current) => 
      (current.duration || 0) > (prev.duration || 0) ? current : prev
    )
    console.log(`Slowest test suite: ${slowestSuite.name} (${(slowestSuite.duration || 0).toFixed(2)}ms)`)
    
    const fastestSuite = summary.results.reduce((prev, current) => 
      (current.duration || Infinity) < (prev.duration || Infinity) ? current : prev
    )
    console.log(`Fastest test suite: ${fastestSuite.name} (${(fastestSuite.duration || 0).toFixed(2)}ms)`)
  }

  /**
   * Run specific test suite by name
   */
  async runSpecificTest(suiteName: string): Promise<TestSuiteResult | null> {
    const testSuites = {
      'continuity': {
        name: 'File Attachment Continuity',
        fn: runFileAttachmentContinuityTests,
        description: 'Tests file intelligence context persistence across messages'
      },
      'filepicker': {
        name: 'Enhanced FilePicker UX',
        fn: runEnhancedFilePickerUXTests,
        description: 'Tests new popup modal file selection experience'
      },
      'intelligence': {
        name: 'Intelligence-First Processing',
        fn: runIntelligenceFirstProcessingTests,
        description: 'Tests existing intelligence loading and new file processing'
      },
      'performance': {
        name: 'Intelligence Cache Performance',
        fn: runIntelligenceCachePerformanceTests,
        description: 'Tests performance improvements and memory management'
      }
    }
    
    const suite = testSuites[suiteName.toLowerCase() as keyof typeof testSuites]
    if (!suite) {
      console.log(`❌ Test suite '${suiteName}' not found.`)
      console.log(`Available suites: ${Object.keys(testSuites).join(', ')}`)
      return null
    }
    
    console.log(`🎯 Running specific test suite: ${suite.name}`)
    
    await this.runTestSuite(suite.name, suite.fn, suite.description)
    
    return this.results[this.results.length - 1]
  }

  /**
   * Generate test report for CI/CD
   */
  generateCIReport(summary: TestRunSummary): string {
    const report = {
      timestamp: new Date().toISOString(),
      overall_status: summary.overallSuccess ? 'PASSED' : 'FAILED',
      total_suites: summary.totalSuites,
      passed_suites: summary.passedSuites,
      failed_suites: summary.failedSuites,
      total_tests: summary.totalTests,
      total_duration_ms: summary.totalDuration,
      results: summary.results.map(r => ({
        name: r.name,
        status: r.success ? 'PASSED' : 'FAILED',
        test_count: r.testCount,
        duration_ms: r.duration,
        error: r.error || null
      }))
    }
    
    return JSON.stringify(report, null, 2)
  }
}

// Export singleton instance
export const chatAreaTestRunner = new ChatAreaTestRunner()

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2)
  const command = args[0]
  
  if (command === 'run') {
    const suite = args[1]
    if (suite) {
      chatAreaTestRunner.runSpecificTest(suite)
    } else {
      chatAreaTestRunner.runAllTests()
    }
  } else {
    console.log('Usage:')
    console.log('  npm run test:chatarea           # Run all test suites')
    console.log('  npm run test:chatarea continuity # Run continuity tests')
    console.log('  npm run test:chatarea filepicker # Run FilePicker tests')
    console.log('  npm run test:chatarea intelligence # Run intelligence tests')
    console.log('  npm run test:chatarea performance # Run performance tests')
  }
}
