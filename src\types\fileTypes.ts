/**
 * Unified File Type System
 * Single source of truth for all file type definitions and interfaces
 */

// Core file type definitions
export type FileType = 
  | 'pdf' 
  | 'image' 
  | 'markdown' 
  | 'mermaid'
  | 'text' 
  | 'code' 
  | 'html'
  | 'json'
  | 'xml'
  | 'yaml'
  | 'csv'
  | 'word'
  | 'excel'
  | 'powerpoint'
  | 'unknown'
  | 'unsupported';

// Unified file type information interface
export interface FileTypeInfo {
  type: FileType;
  extension: string;
  mimeType?: string;
  canExtractText: boolean;
  canAnnotate: boolean;
  requiresProcessing: boolean;
  extractionMethod: 'direct-read' | 'pdf-parse' | 'ocr' | 'plugin-based' | 'none';
  displayName: string;
}

// Plugin extraction result interface
export interface PluginExtractionResult {
  text: string;
  metadata: {
    frontmatter?: any;
    stats?: any;
    processingConfidence?: number;
    wordCount?: number;
    lineCount?: number;
    language?: string;
    [key: string]: any;
  };
}

// File type plugin interface (for renderer plugins)
export interface FileTypePlugin {
  canHandle(fileTypeInfo: FileTypeInfo): boolean;
  render(props: any): React.ReactNode;
  extractText?(content: string): Promise<string>;
  getMetadata?(content: string): Promise<any>;
  extract?(filePath: string, content: string): Promise<PluginExtractionResult>;
}

// File type registry interface
export interface FileTypeRegistry {
  listPlugins(): FileTypePlugin[];
  getPlugin(fileType: FileType): FileTypePlugin | null;
  registerPlugin(fileType: FileType, plugin: FileTypePlugin): void;
}

// Extension to file type mapping
export const EXTENSION_TO_TYPE_MAP: Record<string, FileType> = {
  // PDF
  'pdf': 'pdf',
  
  // Images
  'jpg': 'image',
  'jpeg': 'image',
  'png': 'image',
  'gif': 'image',
  'svg': 'image',
  'webp': 'image',
  'bmp': 'image',
  'ico': 'image',
  
  // Markdown
  'md': 'markdown',
  'markdown': 'markdown',
  
  // Text
  'txt': 'text',
  'log': 'text',
  
  // Code
  'js': 'code',
  'ts': 'code',
  'tsx': 'code',
  'jsx': 'code',
  'html': 'html',
  'css': 'code',
  'scss': 'code',
  'py': 'code',
  'java': 'code',
  'cpp': 'code',
  'c': 'code',
  'cs': 'code',
  'php': 'code',
  'rb': 'code',
  'go': 'code',
  'rs': 'code',
  'swift': 'code',
  'kt': 'code',
  'scala': 'code',
  
  // Data formats
  'json': 'json',
  'xml': 'xml',
  'yaml': 'yaml',
  'yml': 'yaml',
  'csv': 'csv',
  
  // Office Documents
  'doc': 'word',
  'docx': 'word',
  'xls': 'excel',
  'xlsx': 'excel',
  'ppt': 'powerpoint',
  'pptx': 'powerpoint',
  'odt': 'word',
  'ods': 'excel',
  'odp': 'powerpoint',
  'rtf': 'word'
};

// MIME type mappings
export const MIME_TYPE_MAP: Record<FileType, string> = {
  'pdf': 'application/pdf',
  'image': 'image/*',
  'markdown': 'text/markdown',
  'mermaid': 'text/markdown',
  'text': 'text/plain',
  'code': 'text/plain',
  'html': 'text/html',
  'json': 'application/json',
  'xml': 'application/xml',
  'yaml': 'application/x-yaml',
  'csv': 'text/csv',
  'word': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'powerpoint': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'unknown': 'application/octet-stream',
  'unsupported': 'application/octet-stream',
};
