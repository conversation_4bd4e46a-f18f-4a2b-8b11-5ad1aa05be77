/**
 * Master.md Types - V03 Compliant
 * Defines the JSON composition structure for tabbed master.md interface
 */

export interface MasterTab {
  id: string
  title: string
  type: 'activity' | 'canvas' | 'timeline' | 'custom'
  content: MasterTabContent
  isActive?: boolean
}

export interface MasterTabContent {
  // Recent Activity Summary Tab
  recentActivity?: {
    summary: string
    lastUpdated: string
    keyMetrics: {
      filesProcessed: number
      chatsCreated: number
      ideasGenerated: number
    }
    recentFiles: Array<{
      id: string
      name: string
      path: string
      lastModified: string
      intelligence?: {
        keyIdeas: number
        confidence: number
      }
    }>
    recentChats: Array<{
      id: string
      title: string
      lastMessage: string
      timestamp: string
    }>
  }

  // Blank Canvas Tab (for sidecar prompting results)
  canvas?: {
    promptResults: Array<{
      id: string
      prompt: string
      response: string
      timestamp: string
      source: 'sidecar' | 'user' | 'ai'
    }>
    customContent?: string
  }

  // Linear Timeline Tab
  timeline?: {
    items: Array<{
      id: string
      type: 'document' | 'note' | 'chat'
      title: string
      timestamp: string
      path?: string
      preview: string
      tags?: string[]
    }>
    filters: {
      type: Array<'document' | 'note' | 'chat'>
      dateRange?: {
        start: string
        end: string
      }
    }
  }

  // Custom Tab Content
  custom?: {
    title: string
    content: string
    metadata?: Record<string, any>
  }
}

export interface MasterDocument {
  id: string
  vaultPath: string
  contextPath: string
  tabs: MasterTab[]
  metadata: {
    created: string
    lastUpdated: string
    version: string
    generatedBy: 'system' | 'user'
  }
  // Consolidated sections from intelligence
  consolidatedData: {
    files: Array<{
      path: string
      intelligence: any
    }>
    chats: Array<{
      id: string
      title: string
      summary: string
    }>
    artifacts: Array<{
      id: string
      type: string
      title: string
      content: string
    }>
    labels: Array<{
      id: string
      text: string
      frequency: number
      sources: string[]
    }>
    keyIdeas: Array<{
      id: string
      text: string
      relevance: number
      sources: string[]
    }>
    openQuestions: Array<{
      id: string
      question: string
      context: string
      priority: 'high' | 'medium' | 'low'
    }>
  }
}

export interface MasterGenerationOptions {
  includeRecentActivity: boolean
  includeTimeline: boolean
  includeCustomTabs: boolean
  maxItems: number
  dateRange?: {
    start: string
    end: string
  }
}
