/**
 * Security Types for ChatLo Security Framework V2
 * Focused on data privacy and external LLM sharing transparency
 */

export type SecurityLevel = 'strict' | 'balanced' | 'disabled'

export interface DetectedPattern {
  type: string
  matches: string[]
  maskedValues: string[]
  severity: 'low' | 'medium' | 'high'
}

export interface SecurityAnalysis {
  hasFiles: boolean
  fileTypes: string[]
  sensitivePatterns: DetectedPattern[]
  riskLevel: 'low' | 'medium' | 'high'
  customExceptions: string[]
}

export interface SecurityDecision {
  allowed: boolean
  blocked: boolean
  requiresPermission: boolean
  warnings: string[]
  detectedPatterns: DetectedPattern[]
}

export interface ExternalCommLog {
  timestamp: string
  model: string
  securityLevel: SecurityLevel
  contentType: 'text' | 'file' | 'image'
  filesShared?: string[]
  patternsDetected?: string[]
  userConsent: boolean
  blocked: boolean
}

export interface SecurityPatterns {
  sensitivePatterns: Record<string, RegExp>
  excludedPatterns: Record<string, RegExp>
}

export interface PluginSecurityOverride {
  version: string
  pluginName: string
  overrides: {
    sensitivePatterns?: Record<string, string>
    excludedPatterns?: Record<string, string>
    customVaultPatterns?: string[]
  }
  enabled: boolean
}

export interface SecuritySettings {
  securityLevel: SecurityLevel
  customVaultPatterns: string
  privateMode: boolean
  logRetentionDays: number
  patternDetection: boolean
  fileWarnings: boolean
  exportLogsEnabled: boolean
}
