/**
 * Smart Content Chunking System
 * Hybrid approach: semantic chunking with size limits for local model processing
 */

export interface ContentChunk {
  id: string
  content: string
  startIndex: number
  endIndex: number
  tokenCount: number
  semanticBoundary: 'paragraph' | 'section' | 'chapter' | 'forced'
  summary?: string
  keyThemes?: string[]
}

export interface ChunkingOptions {
  maxTokens: number
  overlapTokens: number
  preferSemantic: boolean
  minChunkSize: number
}

/**
 * Default chunking options optimized for local models
 */
export const DEFAULT_CHUNKING_OPTIONS: ChunkingOptions = {
  maxTokens: 4000,        // 4K tokens for most local models
  overlapTokens: 500,     // 500 token overlap for context continuity
  preferSemantic: true,   // Prefer semantic boundaries
  minChunkSize: 1000      // Minimum 1K tokens per chunk
}

/**
 * Estimate token count (rough approximation)
 * Chinese: ~1.5 chars per token, English: ~4 chars per token
 */
export function estimateTokenCount(text: string): number {
  if (!text) return 0
  
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
  const otherChars = text.length - chineseChars
  
  // Chinese: ~1.5 chars per token, others: ~4 chars per token
  return Math.ceil(chineseChars / 1.5 + otherChars / 4)
}

/**
 * Create semantic chunks based on document structure
 */
function createSemanticChunks(text: string, options: ChunkingOptions): ContentChunk[] {
  const chunks: ContentChunk[] = []
  const paragraphs = text.split(/\n\s*\n/) // Split by double newlines
  
  let currentChunk = ''
  let startIndex = 0
  let chunkId = 0
  
  for (let i = 0; i < paragraphs.length; i++) {
    const paragraph = paragraphs[i].trim()
    if (!paragraph) continue
    
    const paragraphTokens = estimateTokenCount(paragraph)
    const currentTokens = estimateTokenCount(currentChunk)
    
    // If adding this paragraph would exceed max tokens, create a new chunk
    if (currentTokens + paragraphTokens > options.maxTokens && currentChunk.length > 0) {
      // Create chunk
      chunks.push({
        id: `chunk_${chunkId++}`,
        content: currentChunk.trim(),
        startIndex,
        endIndex: startIndex + currentChunk.length,
        tokenCount: currentTokens,
        semanticBoundary: 'paragraph'
      })
      
      // Start new chunk with overlap
      const overlapText = getOverlapText(currentChunk, options.overlapTokens)
      currentChunk = overlapText + '\n\n' + paragraph
      startIndex = startIndex + currentChunk.length - overlapText.length - paragraph.length - 2
    } else {
      // Add to current chunk
      if (currentChunk) {
        currentChunk += '\n\n' + paragraph
      } else {
        currentChunk = paragraph
      }
    }
  }
  
  // Add final chunk
  if (currentChunk.trim()) {
    chunks.push({
      id: `chunk_${chunkId++}`,
      content: currentChunk.trim(),
      startIndex,
      endIndex: startIndex + currentChunk.length,
      tokenCount: estimateTokenCount(currentChunk),
      semanticBoundary: 'paragraph'
    })
  }
  
  return chunks
}

/**
 * Create size-based chunks when semantic chunking fails
 */
function createSizeBasedChunks(text: string, options: ChunkingOptions): ContentChunk[] {
  const chunks: ContentChunk[] = []
  const totalTokens = estimateTokenCount(text)
  const chunkCount = Math.ceil(totalTokens / options.maxTokens)
  
  for (let i = 0; i < chunkCount; i++) {
    const startChar = Math.floor((i * text.length) / chunkCount)
    const endChar = Math.floor(((i + 1) * text.length) / chunkCount)
    
    let chunkContent = text.substring(startChar, endChar)
    
    // Try to break at sentence boundaries
    if (i < chunkCount - 1) {
      const lastSentence = chunkContent.lastIndexOf('.')
      const lastQuestion = chunkContent.lastIndexOf('?')
      const lastExclamation = chunkContent.lastIndexOf('!')
      const lastBreak = Math.max(lastSentence, lastQuestion, lastExclamation)
      
      if (lastBreak > chunkContent.length * 0.7) {
        chunkContent = chunkContent.substring(0, lastBreak + 1)
      }
    }
    
    chunks.push({
      id: `chunk_${i}`,
      content: chunkContent.trim(),
      startIndex: startChar,
      endIndex: startChar + chunkContent.length,
      tokenCount: estimateTokenCount(chunkContent),
      semanticBoundary: 'forced'
    })
  }
  
  return chunks
}

/**
 * Get overlap text for context continuity
 */
function getOverlapText(text: string, overlapTokens: number): string {
  const words = text.split(/\s+/)
  const overlapWords = Math.ceil(overlapTokens * 1.5) // Rough approximation
  
  if (words.length <= overlapWords) {
    return text
  }
  
  return words.slice(-overlapWords).join(' ')
}

/**
 * Main chunking function - hybrid approach
 */
export function createSmartChunks(
  text: string, 
  options: Partial<ChunkingOptions> = {}
): ContentChunk[] {
  const finalOptions = { ...DEFAULT_CHUNKING_OPTIONS, ...options }
  
  if (!text || text.length < 1000) {
    return [{
      id: 'chunk_0',
      content: text,
      startIndex: 0,
      endIndex: text.length,
      tokenCount: estimateTokenCount(text),
      semanticBoundary: 'paragraph'
    }]
  }
  
  // Try semantic chunking first
  let chunks = createSemanticChunks(text, finalOptions)
  
  // Validate chunks meet size requirements
  const validChunks = chunks.filter(chunk => 
    chunk.tokenCount >= finalOptions.minChunkSize && 
    chunk.tokenCount <= finalOptions.maxTokens * 1.2 // Allow 20% overflow
  )
  
  // If semantic chunking failed to create valid chunks, fall back to size-based
  if (validChunks.length === 0 || validChunks.length < chunks.length * 0.7) {
    console.log('[Chunking] Semantic chunking failed, falling back to size-based')
    chunks = createSizeBasedChunks(text, finalOptions)
  }
  
  // Add overlap between chunks for context continuity
  return addChunkOverlap(chunks, finalOptions.overlapTokens)
}

/**
 * Add overlap between chunks for better context
 */
function addChunkOverlap(chunks: ContentChunk[], overlapTokens: number): ContentChunk[] {
  if (chunks.length <= 1) return chunks
  
  const enhancedChunks: ContentChunk[] = []
  
  for (let i = 0; i < chunks.length; i++) {
    const chunk = { ...chunks[i] }
    
    // Add overlap from previous chunk
    if (i > 0) {
      const prevChunk = chunks[i - 1]
      const overlapText = getOverlapText(prevChunk.content, overlapTokens)
      chunk.content = overlapText + '\n\n' + chunk.content
      chunk.startIndex = prevChunk.endIndex - overlapText.length
      chunk.tokenCount = estimateTokenCount(chunk.content)
    }
    
    // Add overlap to next chunk
    if (i < chunks.length - 1) {
      const nextChunk = chunks[i + 1]
      const overlapText = getOverlapText(chunk.content, overlapTokens)
      chunk.content = chunk.content + '\n\n' + overlapText
      chunk.endIndex = chunk.startIndex + chunk.content.length
      chunk.tokenCount = estimateTokenCount(chunk.content)
    }
    
    enhancedChunks.push(chunk)
  }
  
  return enhancedChunks
}

/**
 * Analyze chunks and generate hierarchical labels
 */
export async function analyzeChunksWithLabels(
  chunks: ContentChunk[],
  language: string
): Promise<{
  documentSummary: string
  mainThemes: string[]
  detailedLabels: string[]
  chunkAnalyses: Array<{
    chunkId: string
    summary: string
    keyThemes: string[]
  }>
}> {
  // This would integrate with your AI analysis service
  // For now, return a placeholder structure
  
  return {
    documentSummary: `Document analyzed in ${chunks.length} chunks`,
    mainThemes: ['Theme 1', 'Theme 2', 'Theme 3'],
    detailedLabels: ['Label 1', 'Label 2', 'Label 3', 'Label 4', 'Label 5'],
    chunkAnalyses: chunks.map(chunk => ({
      chunkId: chunk.id,
      summary: `Chunk ${chunk.id} summary`,
      keyThemes: ['Chunk theme 1', 'Chunk theme 2']
    }))
  }
}
