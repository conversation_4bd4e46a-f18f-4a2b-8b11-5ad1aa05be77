/**
 * Centralized ID Generation Utility
 * Ensures unique IDs across the application to prevent React key collisions
 */

let idCounter = 0;
const sessionId = Date.now().toString(36);

/**
 * Generate a guaranteed unique ID with session prefix and counter
 */
export function generateUniqueId(prefix: string = 'id'): string {
  idCounter++;
  return `${prefix}_${sessionId}_${idCounter.toString(36)}`;
}

/**
 * Generate a stable hash-based ID for content
 * Uses improved hashing to reduce collisions
 */
export function generateContentId(content: string, prefix: string = 'content'): string {
  let hash = 0;
  const str = content.toLowerCase().trim();
  
  // Use a better hashing algorithm with prime numbers to reduce collisions
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Add timestamp component to ensure uniqueness
  const timestamp = Date.now().toString(36).slice(-4);
  return `${prefix}_${Math.abs(hash).toString(36)}_${timestamp}`;
}

/**
 * Generate a file-based ID that's stable for the same file path
 */
export function generateFileId(filePath: string): string {
  const normalizedPath = filePath.toLowerCase().replace(/[\/\\]/g, '/');
  return generateContentId(normalizedPath, 'file');
}

/**
 * Generate an idea ID for KeyIdea objects
 */
export function generateIdeaId(text: string, index?: number): string {
  const baseId = generateContentId(text, 'idea');
  return index !== undefined ? `${baseId}_${index}` : baseId;
}

/**
 * Generate a session-based ID for temporary objects
 */
export function generateSessionId(prefix: string = 'session'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
