/**
 * Language Detection & Localization Utilities
 * Automatically detects content language and ensures AI labels match
 */

export interface LanguageInfo {
  language: 'zh' | 'en' | 'ja' | 'ko' | 'other'
  confidence: number
  script: 'latin' | 'hanzi' | 'hiragana' | 'katakana' | 'hangul' | 'other'
}

/**
 * Detect the primary language of text content
 */
export function detectLanguage(text: string): LanguageInfo {
  if (!text || text.length < 50) {
    return { language: 'en', confidence: 0.5, script: 'latin' }
  }

  // Count character types
  const totalChars = text.length
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
  const japaneseChars = (text.match(/[\u3040-\u309f\u30a0-\u30ff]/g) || []).length
  const koreanChars = (text.match(/[\uac00-\ud7af]/g) || []).length
  const latinChars = (text.match(/[a-zA-Z]/g) || []).length

  // Calculate percentages
  const chineseRatio = chineseChars / totalChars
  const japaneseRatio = japaneseChars / totalChars
  const koreanRatio = koreanChars / totalChars
  const latinRatio = latinChars / totalChars

  // Determine primary language with confidence
  if (chineseRatio > 0.3) {
    return {
      language: 'zh',
      confidence: Math.min(chineseRatio * 2, 0.95),
      script: 'hanzi'
    }
  }

  if (japaneseRatio > 0.2) {
    return {
      language: 'ja',
      confidence: Math.min(japaneseRatio * 2.5, 0.9),
      script: japaneseChars > 0 ? 'hiragana' : 'katakana'
    }
  }

  if (koreanRatio > 0.2) {
    return {
      language: 'ko',
      confidence: Math.min(koreanRatio * 2.5, 0.9),
      script: 'hangul'
    }
  }

  if (latinRatio > 0.5) {
    return {
      language: 'en',
      confidence: Math.min(latinRatio * 1.5, 0.9),
      script: 'latin'
    }
  }

  return {
    language: 'other',
    confidence: 0.3,
    script: 'other'
  }
}

/**
 * Get language-specific label generation prompts
 */
export function getLabelPrompt(language: string, isDetailed: boolean = false): string {
  const prompts = {
    zh: {
      simple: '请为这个文档生成3-5个主要主题标签，用中文描述核心概念：',
      detailed: '请为这个文档生成10-15个详细标签，用中文描述具体主题和概念，包括主要主题和子主题：'
    },
    en: {
      simple: 'Please generate 3-5 main theme labels for this document, describing core concepts in English:',
      detailed: 'Please generate 10-15 detailed labels for this document, describing specific topics and concepts in English, including main themes and subtopics:'
    },
    ja: {
      simple: 'この文書の3-5個の主要テーマラベルを日本語で生成し、核心概念を説明してください：',
      detailed: 'この文書の10-15個の詳細ラベルを日本語で生成し、主要テーマとサブテーマを含む具体的なトピックと概念を説明してください：'
    },
    ko: {
      simple: '이 문서에 대한 3-5개의 주요 테마 라벨을 한국어로 생성하여 핵심 개념을 설명하세요:',
      detailed: '이 문서에 대한 10-15개의 상세 라벨을 한국어로 생성하여 주요 테마와 하위 테마를 포함한 구체적인 주제와 개념을 설명하세요:'
    }
  }

  const lang = prompts[language as keyof typeof prompts] || prompts.en
  return isDetailed ? lang.detailed : lang.simple
}

/**
 * Get language-specific UI text
 */
export function getUIText(language: string): Record<string, string> {
  const uiTexts = {
    zh: {
      more: '更多',
      labels: '标签',
      selected: '已选择',
      add: '添加',
      smartAnnotations: '智能注释',
      notes: '笔记',
      saveNote: '保存笔记',
      smartInput: '输入想法或询问AI关于此文档的问题...'
    },
    en: {
      more: 'more',
      labels: 'labels',
      selected: 'selected',
      add: '+ Add',
      smartAnnotations: 'Smart Annotations',
      notes: 'notes',
      saveNote: 'Save Note',
      smartInput: 'Type thoughts or ask AI questions about this document...'
    },
    ja: {
      more: 'もっと',
      labels: 'ラベル',
      selected: '選択済み',
      add: '追加',
      smartAnnotations: 'スマート注釈',
      notes: 'メモ',
      saveNote: 'メモを保存',
      smartInput: 'この文書についての考えを入力するか、AIに質問してください...'
    },
    ko: {
      more: '더보기',
      labels: '라벨',
      selected: '선택됨',
      add: '추가',
      smartAnnotations: '스마트 주석',
      notes: '메모',
      saveNote: '메모 저장',
      smartInput: '이 문서에 대한 생각을 입력하거나 AI에게 질문하세요...'
    }
  }

  return uiTexts[language as keyof typeof uiTexts] || uiTexts.en
}
