import { describe, it, expect } from 'vitest'
import { PathResolver } from '../electron/core/PathResolver'

// These tests validate only the path mapping logic (kernel-side), not disk I/O

const vault = 'C:/Users/<USER>/Documents/Post-Kernel-Test4/personal-vault'

describe('V03 path contracts', () => {
  it('context-notes mode writes under <vault>/.intelligence/context-notes/<name>.json', () => {
    const { jsonPath, filesDir } = PathResolver.getIntelligenceJsonPath('context-notes/my-convo.json', vault)
    const norm = (s: string) => s.replace(/\\/g, '/');
    expect(norm(filesDir)).toMatch(/\/\.intelligence\/context-notes$/)
    expect(norm(jsonPath)).toMatch(/\/\.intelligence\/context-notes\/my-convo\.json$/)
  })

  it('safety shim: absolute .intelligence/context-notes path normalizes to context-notes under vault', () => {
    const fp = 'C:/Users/<USER>/Documents/Post-Kernel-Test4/personal-vault/.intelligence/context-notes/abc.json'
    const { jsonPath } = PathResolver.getIntelligenceJsonPath(fp, vault)
    const norm = (s: string) => s.replace(/\\/g, '/');
    expect(norm(jsonPath)).toMatch(/\/\.intelligence\/context-notes\/abc\.json$/)
  })

  it('file intelligence mode maps original file path to <vault>/.intelligence/files/<hash>.json', () => {
    const fp = 'C:/Users/<USER>/Documents/Post-Kernel-Test4/personal-vault/documents/guide.md'
    const { jsonPath, filesDir } = PathResolver.getIntelligenceJsonPath(fp, vault)
    const norm = (s: string) => s.replace(/\\/g, '/');
    expect(norm(filesDir)).toMatch(/\/\.intelligence\/files$/)
    expect(norm(jsonPath)).toMatch(/\/\.intelligence\/files\/.+\.json$/)
  })
})

