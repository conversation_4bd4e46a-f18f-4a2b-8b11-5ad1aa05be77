/**
 * Comprehensive Model Update Logic
 * Centralized logic for model updates, categorization, and OTA updates
 * Moving to src/services/ for proper Vite import resolution
 */

export interface ModelManifest {
  version: string
  last_updated: string
  crawl_timestamp: number
  statistics: {
    total_models: number
    flagship_models: number
    free_models: number
    vision_models: number
    reasoning_models: number
    code_models: number
    search_models: number
    providers: number
  }
  models: EnhancedModel[]
  featured_models: string[]
  deprecated_models: string[]
  metadata: {
    source: string
    generator_version: string
    note?: string
  }
}

export interface EnhancedModel {
  id: string
  name: string
  description: string
  context_length: number
  pricing: {
    prompt: string
    completion: string
  }
  top_provider: {
    max_completion_tokens?: number
  }
  architecture: {
    modality: string
    tokenizer: string
    instruct_type?: string
  }
  per_request_limits?: any
  provider: string
  categories: string[]
  is_flagship: boolean
  is_free: boolean
  created_timestamp: number
  last_updated: string
}

export class ModelUpdateLogic {
  private static instance: ModelUpdateLogic
  private manifestUrl = '/models-manifest.json'
  private fallbackUrl = 'https://openrouter.ai/api/v1/models'

  // Flagship criteria - latest and largest models from major providers
  private flagshipCriteria = {
    'openai': {
      patterns: ['gpt-4o', 'o1-preview', 'o1-mini', 'chatgpt-4o-latest'],
      exclude: ['turbo-preview', '0314', '0613', '1106-preview', 'gpt-3.5']
    },
    'anthropic': {
      patterns: ['claude-3.5-sonnet', 'claude-3-opus', 'claude-3.5-haiku', 'claude-opus-4', 'claude-sonnet-4'],
      exclude: ['claude-2.1', 'claude-2.0', 'claude-instant']
    },
    'google': {
      patterns: ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-pro-1.5', 'gemini-1.5-pro'],
      exclude: ['gemini-1.0', 'gemma']
    },
    'x-ai': {
      patterns: ['grok-4', 'grok-3', 'grok-2'],
      exclude: ['grok-1']
    },
    'meta-llama': {
      patterns: ['llama-3.3-70b', 'llama-3.1-405b', 'llama-3.1-70b'],
      exclude: ['llama-2', 'llama-3-8b', 'llama-3.1-8b']
    },
    'deepseek': {
      patterns: ['deepseek-r1', 'deepseek-v3', 'deepseek-v2.5'],
      exclude: ['deepseek-coder-1.3b', 'deepseek-math']
    },
    'qwen': {
      patterns: ['qwen-2.5-72b', 'qwen-2.5-coder-32b', 'qwen-max'],
      exclude: ['qwen-1.5', 'qwen-7b', 'qwen-14b']
    }
  }

  static getInstance(): ModelUpdateLogic {
    if (!ModelUpdateLogic.instance) {
      ModelUpdateLogic.instance = new ModelUpdateLogic()
    }
    return ModelUpdateLogic.instance
  }

  // Check if model is flagship based on provider and patterns
  isFlagshipModel(model: any): boolean {
    const modelId = model.id.toLowerCase()
    const modelName = model.name.toLowerCase()
    
    for (const [provider, config] of Object.entries(this.flagshipCriteria)) {
      if (modelId.includes(provider) || modelName.includes(provider.replace('-', ' '))) {
        // Check if matches flagship patterns
        const matchesPattern = config.patterns.some(pattern => 
          modelId.includes(pattern.toLowerCase()) || modelName.includes(pattern.toLowerCase())
        )
        
        // Check if excluded
        const isExcluded = config.exclude.some(exclude => 
          modelId.includes(exclude.toLowerCase()) || modelName.includes(exclude.toLowerCase())
        )
        
        return matchesPattern && !isExcluded
      }
    }
    
    return false
  }

  // Dynamic categorization based on model properties
  categorizeModel(model: any): string[] {
    const modelId = model.id.toLowerCase()
    const modelName = model.name.toLowerCase()
    const description = (model.description || '').toLowerCase()
    
    const categories: string[] = []
    
    // Free models
    if (model.pricing.prompt === "0" && model.pricing.completion === "0") {
      categories.push('free')
    }
    
    // Flagship models
    if (this.isFlagshipModel(model)) {
      categories.push('flagship')
    }
    
    // Reasoning models
    if (modelId.includes('o1-') || modelId.includes('reasoning') || 
        modelName.includes('thinking') || modelName.includes('reasoning') ||
        description.includes('reasoning') || description.includes('chain-of-thought') ||
        modelId.includes('grok') || description.includes('spend more time thinking')) {
      categories.push('reasoning')
    }
    
    // Vision models
    if (model.architecture?.modality?.includes('image') ||
        modelId.includes('vision') || modelName.includes('vision') ||
        description.includes('vision') || description.includes('multimodal') ||
        description.includes('image')) {
      categories.push('vision')
    }
    
    // Code models
    if (modelId.includes('code') || modelId.includes('devstral') ||
        modelName.includes('code') || modelName.includes('dev') ||
        description.includes('coding') || description.includes('software') ||
        description.includes('programming')) {
      categories.push('code')
    }

    // Search models
    if (modelId.includes('search') || modelId.includes('perplexity') ||
        modelName.includes('search') || modelName.includes('perplexity') ||
        description.includes('search') || description.includes('web search') ||
        description.includes('real-time') || description.includes('internet access')) {
      categories.push('search')
    }

    return categories
  }

  // Process raw OpenRouter model into enhanced format
  processModel(model: any): EnhancedModel {
    return {
      id: model.id,
      name: model.name,
      description: model.description || '',
      context_length: model.context_length,
      pricing: {
        prompt: model.pricing.prompt,
        completion: model.pricing.completion
      },
      top_provider: {
        max_completion_tokens: model.top_provider?.max_completion_tokens
      },
      architecture: {
        modality: model.architecture?.modality || 'text->text',
        tokenizer: model.architecture?.tokenizer || 'unknown',
        instruct_type: model.architecture?.instruct_type
      },
      per_request_limits: model.per_request_limits,
      provider: model.id.split('/')[0],
      categories: this.categorizeModel(model),
      is_flagship: this.isFlagshipModel(model),
      is_free: model.pricing.prompt === "0" && model.pricing.completion === "0",
      created_timestamp: model.created || Date.now(),
      last_updated: new Date().toISOString()
    }
  }

  // Fetch model manifest
  async fetchManifest(): Promise<ModelManifest | null> {
    try {
      const response = await fetch(this.manifestUrl)
      if (!response.ok) {
        console.warn('Failed to fetch model manifest, falling back to OpenRouter')
        return null
      }
      return await response.json()
    } catch (error) {
      console.error('Error fetching model manifest:', error)
      return null
    }
  }

  // Check if update is needed
  async shouldUpdate(): Promise<{ shouldUpdate: boolean; latestVersion?: string }> {
    try {
      const manifest = await this.fetchManifest()
      if (!manifest) return { shouldUpdate: false }

      const storedVersion = await this.getStoredVersion()
      
      return {
        shouldUpdate: manifest.version !== storedVersion,
        latestVersion: manifest.version
      }
    } catch (error) {
      console.error('Error checking for updates:', error)
      return { shouldUpdate: false }
    }
  }

  // Get stored version from database/localStorage
  private async getStoredVersion(): Promise<string | null> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.settings) {
        return await window.electronAPI.settings.get('model-manifest-version')
      } else if (typeof localStorage !== 'undefined') {
        return localStorage.getItem('model-manifest-version')
      }
      return null
    } catch (error) {
      console.error('Error getting stored version:', error)
      return null
    }
  }

  // Update stored version
  async updateStoredVersion(version: string): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.settings) {
        await window.electronAPI.settings.set('model-manifest-version', version)
      } else if (typeof localStorage !== 'undefined') {
        localStorage.setItem('model-manifest-version', version)
      }
    } catch (error) {
      console.error('Error updating stored version:', error)
    }
  }

  // Generate statistics from models
  generateStatistics(models: EnhancedModel[]) {
    return {
      total_models: models.length,
      flagship_models: models.filter(m => m.is_flagship).length,
      free_models: models.filter(m => m.is_free).length,
      vision_models: models.filter(m => m.categories.includes('vision')).length,
      reasoning_models: models.filter(m => m.categories.includes('reasoning')).length,
      code_models: models.filter(m => m.categories.includes('code')).length,
      providers: [...new Set(models.map(m => m.provider))].length
    }
  }

  // Get models with update logic
  async getModels(apiKey?: string): Promise<EnhancedModel[]> {
    try {
      // Try manifest first
      const manifest = await this.fetchManifest()
      if (manifest) {
        console.log(`Using model manifest v${manifest.version}`)
        return manifest.models
      }

      // Fallback to OpenRouter API
      if (apiKey) {
        console.log('Falling back to OpenRouter API')
        const response = await fetch(this.fallbackUrl, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          const rawModels = data.data || []
          return rawModels.map(model => this.processModel(model))
        }
      }

      return []
    } catch (error) {
      console.error('Error getting models:', error)
      return []
    }
  }

  // Force update for testing
  async forceUpdate(): Promise<void> {
    await this.updateStoredVersion('')
    console.log('Forced model update - cleared version cache')
  }
}

// Export singleton instance
export const modelUpdateLogic = ModelUpdateLogic.getInstance()
